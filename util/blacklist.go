package util

import (
	"context"
	"git.garena.com/shopee/game_platform/comm_lib/anticheat_rpc"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/proto/game_rank"
	"git.garena.com/shopee/game_platform/proto/gameplatform_game"
	"github.com/golang/protobuf/proto"
)

func CheckUserInBlacklist(ctx context.Context, userId int64, appId string, rsp *game_rank.CommonResponse) bool {
	er := anticheat_rpc.CheckUserBlackList(ctx, gameplatform_game.GameGrpcClient, appId, userId, anticheat_rpc.BizRank)

	if er != nil && er.GetErrCode() == anticheat_rpc.ErrorCodeBlacklistMatch {
		gamelog.Info("User has been blacked out", gamelog.Fields{
			"userId": userId,
			"appId":  appId,
		})

		if rsp != nil {
			rsp.ErrCode = proto.Int32(int32(anticheat_rpc.BlackListMatchError.GetErrCode()))
			rsp.ErrMsg = proto.String(anticheat_rpc.BlackListMatchError.GetErrMsg())
		}

		return true
	}

	return false
}
