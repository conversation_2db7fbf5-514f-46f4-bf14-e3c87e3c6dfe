package util

import (
	"time"
)

func MillSecondsToToday(ts time.Time, timeZone string) int64 {
	location, _ := time.LoadLocation(timeZone)
	ts = ts.In(location)
	year, month, day := ts.Date()
	zeroTs := time.Date(year, month, day, 0, 0, 0, 0, location)
	return (ts.UnixNano() - zeroTs.UnixNano()) / 1000 / 1000
}

func ZeroTimeIn(ts time.Time, timeZone string) time.Time {
	local, _ := time.LoadLocation(timeZone)
	year, month, day := ts.Date()
	zeroTs := time.Date(year, month, day, 0, 0, 0, 0, local)
	return zeroTs
}

func ConvertNanoSecond(timestamp int64) time.Time {
	second := timestamp / 1e9
	nanosecond := timestamp - second*1e9
	return time.Unix(second, nanosecond)
}
