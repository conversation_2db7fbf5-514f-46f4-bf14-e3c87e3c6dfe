<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .user { fill: #e1bee7; stroke: #8e24aa; stroke-width: 2; }
      .external { fill: #81c784; stroke: #388e3c; stroke-width: 2; }
      .gateway { fill: #64b5f6; stroke: #1976d2; stroke-width: 2; }
      .middleware { fill: #fff176; stroke: #f57f17; stroke-width: 2; }
      .business { fill: #a5d6a7; stroke: #388e3c; stroke-width: 2; }
      .async { fill: #ffcc80; stroke: #f57c00; stroke-width: 2; }
      .dao { fill: #f8bbd9; stroke: #c2185b; stroke-width: 2; }
      .storage { fill: #ffab91; stroke: #d84315; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; }
      .title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; text-anchor: middle; }
      .layer-title { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; text-anchor: middle; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="600" y="30" class="layer-title">Rank Server System Architecture</text>
  
  <!-- External Users -->
  <rect x="50" y="60" width="100" height="60" class="user" rx="5"/>
  <text x="100" y="85" class="title">Game User</text>
  
  <rect x="200" y="60" width="100" height="60" class="user" rx="5"/>
  <text x="250" y="85" class="title">Game Admin</text>
  
  <rect x="350" y="60" width="100" height="60" class="user" rx="5"/>
  <text x="400" y="85" class="title">ODP System</text>
  
  <!-- External Services -->
  <rect x="850" y="60" width="300" height="180" class="external" rx="10" fill="none"/>
  <text x="1000" y="80" class="layer-title">External Services</text>
  
  <rect x="870" y="100" width="80" height="40" class="external" rx="5"/>
  <text x="910" y="115" class="text">SPEX</text>
  <text x="910" y="130" class="text">Discovery</text>
  
  <rect x="970" y="100" width="80" height="40" class="external" rx="5"/>
  <text x="1010" y="115" class="text">Game</text>
  <text x="1010" y="130" class="text">Platform</text>
  
  <rect x="1070" y="100" width="80" height="40" class="external" rx="5"/>
  <text x="1110" y="115" class="text">Friend</text>
  <text x="1110" y="130" class="text">Service</text>
  
  <!-- Main Application -->
  <rect x="50" y="160" width="750" height="500" class="gateway" rx="10" fill="none" stroke="#1976d2"/>
  <text x="425" y="180" class="layer-title">Rank Server Application</text>
  
  <!-- Gateway Layer -->
  <rect x="70" y="200" width="710" height="60" class="gateway" rx="5" fill="none"/>
  <text x="425" y="220" class="title">gRPC Gateway</text>
  <rect x="350" y="230" width="150" height="25" class="gateway" rx="3"/>
  <text x="425" y="245" class="text">gRPC Server</text>
  
  <!-- Middleware Layer -->
  <rect x="70" y="280" width="710" height="60" class="middleware" rx="5" fill="none"/>
  <text x="425" y="300" class="title">Middleware</text>
  
  <rect x="90" y="315" width="70" height="20" class="middleware" rx="3"/>
  <text x="125" y="327" class="text">Access Log</text>
  
  <rect x="180" y="315" width="70" height="20" class="middleware" rx="3"/>
  <text x="215" y="327" class="text">Metrics</text>
  
  <rect x="270" y="315" width="70" height="20" class="middleware" rx="3"/>
  <text x="305" y="327" class="text">Tracing</text>
  
  <rect x="360" y="315" width="70" height="20" class="middleware" rx="3"/>
  <text x="395" y="327" class="text">Recovery</text>
  
  <rect x="450" y="315" width="70" height="20" class="middleware" rx="3"/>
  <text x="485" y="327" class="text">Health</text>
  
  <!-- Business Logic Layer -->
  <rect x="70" y="360" width="710" height="80" class="business" rx="5" fill="none"/>
  <text x="425" y="380" class="title">Business Logic Handlers</text>
  
  <rect x="90" y="390" width="80" height="40" class="business" rx="3"/>
  <text x="130" y="405" class="text">Handler</text>
  <text x="130" y="420" class="text">V1.0</text>
  
  <rect x="190" y="390" width="80" height="40" class="business" rx="3"/>
  <text x="230" y="405" class="text">Handler</text>
  <text x="230" y="420" class="text">V2.0</text>
  
  <rect x="290" y="390" width="80" height="40" class="business" rx="3"/>
  <text x="330" y="405" class="text">Handler</text>
  <text x="330" y="420" class="text">V3.0</text>
  
  <rect x="390" y="390" width="80" height="40" class="business" rx="3"/>
  <text x="430" y="405" class="text">Friends</text>
  <text x="430" y="420" class="text">Handler</text>
  
  <rect x="490" y="390" width="80" height="40" class="business" rx="3"/>
  <text x="530" y="405" class="text">Module</text>
  <text x="530" y="420" class="text">Handler</text>
  
  <rect x="590" y="390" width="80" height="40" class="business" rx="3"/>
  <text x="630" y="405" class="text">Admin</text>
  <text x="630" y="420" class="text">Handler</text>
  
  <!-- Async Processing Layer -->
  <rect x="70" y="460" width="710" height="60" class="async" rx="5" fill="none"/>
  <text x="425" y="480" class="title">Async Processing</text>
  
  <rect x="150" y="495" width="100" height="20" class="async" rx="3"/>
  <text x="200" y="507" class="text">Rank Writer</text>
  
  <rect x="300" y="495" width="100" height="20" class="async" rx="3"/>
  <text x="350" y="507" class="text">ES Writer</text>
  
  <rect x="450" y="495" width="100" height="20" class="async" rx="3"/>
  <text x="500" y="507" class="text">Task Scheduler</text>
  
  <!-- Data Access Layer -->
  <rect x="70" y="540" width="710" height="60" class="dao" rx="5" fill="none"/>
  <text x="425" y="560" class="title">Data Access Layer</text>
  
  <rect x="150" y="575" width="100" height="20" class="dao" rx="3"/>
  <text x="200" y="587" class="text">Cache DAO</text>
  
  <rect x="300" y="575" width="100" height="20" class="dao" rx="3"/>
  <text x="350" y="587" class="text">ES DAO</text>
  
  <rect x="450" y="575" width="100" height="20" class="dao" rx="3"/>
  <text x="500" y="587" class="text">MySQL DAO</text>
  
  <!-- Storage Layer -->
  <rect x="50" y="680" width="750" height="80" class="storage" rx="10" fill="none"/>
  <text x="425" y="700" class="layer-title">Storage Layer</text>
  
  <ellipse cx="200" cy="735" rx="80" ry="25" class="storage"/>
  <text x="200" y="730" class="text">Redis</text>
  <text x="200" y="745" class="text">Ranking Cache</text>
  
  <ellipse cx="425" cy="735" rx="80" ry="25" class="storage"/>
  <text x="425" y="730" class="text">Elasticsearch</text>
  <text x="425" y="745" class="text">Search &amp; Analytics</text>
  
  <ellipse cx="650" cy="735" rx="80" ry="25" class="storage"/>
  <text x="650" y="730" class="text">MySQL</text>
  <text x="650" y="745" class="text">Configuration</text>
  
  <!-- Connection Lines -->
  <!-- Users to Gateway -->
  <line x1="100" y1="120" x2="425" y2="200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="250" y1="120" x2="425" y2="200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="400" y1="120" x2="425" y2="200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- External Services to Gateway -->
  <line x1="910" y1="140" x2="500" y2="200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Gateway to Middleware -->
  <line x1="425" y1="260" x2="425" y2="280" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Middleware to Business Logic -->
  <line x1="425" y1="340" x2="425" y2="360" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Business Logic to Async Processing -->
  <line x1="330" y1="430" x2="350" y2="460" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Async Processing to Data Access -->
  <line x1="350" y1="520" x2="350" y2="540" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Data Access to Storage -->
  <line x1="200" y1="600" x2="200" y2="680" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="350" y1="600" x2="425" y2="680" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="500" y1="600" x2="650" y2="680" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
</svg>
