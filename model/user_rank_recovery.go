package model

type UserRankRecovery struct {
	ID          uint64 `gorm:"column:id;primary_key"`
	Scope       string `gorm:"column:scope"`
	RankName    string `gorm:"column:rank_name"`
	RankWeek    string `gorm:"column:rank_week"`
	RankType    uint32 `gorm:"column:rank_type"`
	UserId      uint64 `gorm:"column:user_id"`
	Username    string `gorm:"column:username"`
	Score       uint64 `gorm:"column:score"`
	Recover     uint32 `gorm:"column:recover"`
	Operator    string `gorm:"column:operator"`
	CreateTime  int64  `gorm:"column:create_time"`
	RecoverTime int64  `gorm:"column:recover_time"`
	Reserve1    string `gorm:"column:reserve1"`
}

func (UserRankRecovery) TableName() string {
	return "gameplatform_user_rank_recovery_tab"
}
