package model

import (
	"database/sql/driver"
	"encoding/json"
	"git.garena.com/shopee/game_platform/proto/game_rank"
)

type NewRankConfigVersionTab struct {
	ConfigId          int32    `gorm:"column:config_id;primary_key" json:"config_id"`
	ModuleId          int32    `gorm:"column:module_id" json:"module_id"`
	IsDraft           int32    `gorm:"column:is_draft;is_draft" json:"is_draft"`
	ConfigData        *RankJSON `gorm:"column:config_data;type:json" json:"config_data"`
	ConfigDataVersion int32    `gorm:"column:config_data_version" json:"config_data_version"`
	Ctime             int64    `gorm:"column:ctime" json:"ctime"`
	Mtime             int64    `gorm:"column:mtime" json:"mtime"`
}

type RankJSON game_rank.NewRankModuleConfig

func (t *RankJSON) Value() (driver.Value, error) {
	return json.Marshal(t)
}

func (t *RankJSON) Scan(input interface{}) error {
	return json.Unmarshal(input.([]byte), t)
}

func (m NewRankConfigVersionTab) TableName() string {
	return "gameplatform_newrank_config_version_tab"
}

/*func (m *NewRankConfigVersionTab) ParseChatConfig() *ChatConfig {
	ret := &ChatConfig{}
	if m.ConfigData != "" {
		if err := json.Unmarshal([]byte(m.ConfigData), ret); err != nil {
			gamelog.Error("parse chat draft config data", gamelog.Fields{"err": err, "chat draft": m})
		}
	}
	return ret
}*/
