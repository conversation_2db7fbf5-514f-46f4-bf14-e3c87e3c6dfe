# Rank Server System Architecture

## Overview
This is a game ranking service system that supports multi-version APIs (V1.0, V2.0, V3.0) and provides comprehensive ranking functionality including score updates, rank queries, friend rankings, etc.

## Architecture Diagram

```mermaid
graph TB
    %% External Users
    GameUser[Game User]
    GameAdmin[Game Admin]
    ODP[ODP System]

    %% External Services
    subgraph ExternalServices ["External Services"]
        SPEX[SPEX<br/>Service Discovery]
        GamePlatform[Game Platform<br/>Blacklist & Config]
        FriendService[Friend Service<br/>User Info & Friends]
    end

    %% Main Application
    subgraph Application ["Rank Server Application"]
        
        %% Gateway Layer
        subgraph Gateway ["gRPC Gateway"]
            GrpcServer[gRPC Server<br/>mod/grpc_server]
        end

        %% Middleware Layer  
        subgraph Middleware ["Middleware"]
            AccessLog[Access Log]
            Metrics[Metrics & Reporter]
            Tracing[Distributed Tracing]
            Recovery[Panic Recovery]
            HealthCheck[Health Check]
        end

        %% Business Logic Layer
        subgraph BusinessLogic ["Business Logic Handlers"]
            RankHandlerV1[Rank Handler V1.0<br/>Basic Functions]
            RankHandlerV2[Rank Handler V2.0<br/>Bucket & TopN]
            RankHandlerV3[Rank Handler V3.0<br/>Module Config]
            FriendsHandler[Friends Handler<br/>Friend Rankings]
            ModuleHandler[Module Handler<br/>Config Management]
            AdminHandler[Admin Handler<br/>ES Operations]
        end

        %% Async Processing Layer
        subgraph AsyncProcessing ["Async Processing"]
            RankWriter[Rank Writer<br/>Channel + Workers]
            EsWriter[ES Writer<br/>Bulk Operations]
            TaskScheduler[Task Scheduler<br/>Index Cleanup]
        end

        %% Data Access Layer
        subgraph DataAccess ["Data Access Layer"]
            CacheDAO[Cache DAO<br/>Redis Operations]
            ESDAO[ES DAO<br/>Search Operations]
            MySQLDAO[MySQL DAO<br/>Config & Recovery]
        end
    end

    %% Storage Layer
    subgraph Storage ["Storage Layer"]
        Redis[(Redis<br/>Ranking Cache<br/>Bucket Strategy)]
        ES[(Elasticsearch<br/>Search & Analytics<br/>Historical Data)]
        MySQL[(MySQL<br/>Configuration<br/>Recovery Data)]
    end

    %% User Flow
    GameUser --> GrpcServer
    GameAdmin --> GrpcServer
    ODP --> GrpcServer

    %% External Service Integration
    SPEX --> GrpcServer
    
    %% Gateway to Middleware
    GrpcServer --> Middleware

    %% Middleware to Business Logic
    Middleware --> RankHandlerV1
    Middleware --> RankHandlerV2
    Middleware --> RankHandlerV3
    Middleware --> FriendsHandler
    Middleware --> ModuleHandler
    Middleware --> AdminHandler

    %% Business Logic to External Services
    RankHandlerV2 --> GamePlatform
    RankHandlerV3 --> GamePlatform
    FriendsHandler --> FriendService
    AdminHandler --> GamePlatform

    %% Business Logic to Async Processing
    RankHandlerV1 --> RankWriter
    RankHandlerV2 --> RankWriter
    RankHandlerV3 --> RankWriter
    RankWriter --> EsWriter

    %% Business Logic to Data Access
    RankHandlerV1 --> CacheDAO
    RankHandlerV2 --> CacheDAO
    RankHandlerV3 --> CacheDAO
    FriendsHandler --> ESDAO
    ModuleHandler --> MySQLDAO
    AdminHandler --> ESDAO

    %% Async Processing to Data Access
    RankWriter --> CacheDAO
    EsWriter --> ESDAO
    TaskScheduler --> ESDAO

    %% Data Access to Storage
    CacheDAO --> Redis
    ESDAO --> ES
    MySQLDAO --> MySQL

    %% Styling
    classDef user fill:#e1bee7
    classDef external fill:#81c784
    classDef gateway fill:#64b5f6
    classDef middleware fill:#fff176
    classDef business fill:#a5d6a7
    classDef async fill:#ffcc80
    classDef dao fill:#f8bbd9
    classDef storage fill:#ffab91

    class GameUser,GameAdmin,ODP user
    class SPEX,GamePlatform,FriendService external
    class GrpcServer gateway
    class AccessLog,Metrics,Tracing,Recovery,HealthCheck middleware
    class RankHandlerV1,RankHandlerV2,RankHandlerV3,FriendsHandler,ModuleHandler,AdminHandler business
    class RankWriter,EsWriter,TaskScheduler async
    class CacheDAO,ESDAO,MySQLDAO dao
    class Redis,ES,MySQL storage
```

## Key Components

### 1. Gateway Layer
- **gRPC Server**: Unified entry point for all ranking services
- **Protocol**: gRPC with Protocol Buffers
- **Location**: `mod/grpc_server/server.go`

### 2. Middleware Layer
- **Access Log**: Request/response logging
- **Metrics**: Performance monitoring and reporting
- **Tracing**: Distributed tracing for debugging
- **Recovery**: Panic recovery mechanism
- **Health Check**: Service health monitoring

### 3. Business Logic Layer
- **Rank Handler V1.0**: Basic ranking functions (`handler/rank_handler.go`)
- **Rank Handler V2.0**: Enhanced with bucket strategy and TopN optimization (`handler/rank_handler_2.0.go`)
- **Rank Handler V3.0**: Module-based configuration support (`handler/rank_handler_3.0.go`)
- **Friends Handler**: Friend ranking functionality (`handler/rank_handler_friends.go`)
- **Module Handler**: Ranking module management (`handler/rank_handler_module.go`)
- **Admin Handler**: Administrative operations (`handler/admin_es.go`, `handler/admin_es_v2.go`)

### 4. Async Processing Layer
- **Rank Writer**: Channel + Goroutine based async processing
- **ES Writer**: Bulk operations for Elasticsearch
- **Task Scheduler**: Scheduled tasks for index cleanup (`handler/close_index_task.go`)

### 5. Data Access Layer
- **Cache DAO**: Redis operations (`dao/cache/`)
- **ES DAO**: Elasticsearch operations
- **MySQL DAO**: Database operations (`dao/mysql/`)

### 6. Storage Layer
- **Redis**: Primary ranking cache with bucket strategy
- **Elasticsearch**: Search, analytics, and historical data
- **MySQL**: Configuration and recovery data

## Architecture Characteristics

### Performance Optimization
- **Bucket Strategy**: Reduces Redis hot key issues
- **TopN Optimization**: Precise ranking for top 10,000, approximate for others
- **Async Processing**: Non-blocking operations using channels
- **Batch Operations**: Bulk ES writes for better throughput
- **Local Cache**: User info caching to reduce RPC calls

### Data Consistency
- **Dual Write**: Redis buckets + TopN key synchronization
- **Retry Mechanism**: Automatic retry for failed operations
- **Scheduled Cleanup**: Automatic cleanup of expired data

### Scalability
- **Multi-version Support**: V1.0, V2.0, V3.0 APIs
- **Modular Design**: Separate handlers for different functionalities
- **Service Integration**: External service integration via gRPC
- **Configuration Management**: Dynamic configuration support
