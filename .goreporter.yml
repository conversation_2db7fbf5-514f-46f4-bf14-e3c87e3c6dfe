# golangci-lint 1.57.0及以上,2.0以下配置，golangci-lint的跳过文件或文件夹的配置位于issues处
# 该版本支持nilaway检测空指针插件
linters-settings:
  staticcheck:
    checks:
      - all
      - '-SA1019'
  gocognit:
    min-complexity: 20
  dupl:
    threshold: 150
  exhaustive:
    default-signifies-exhaustive: false
  funlen:
    lines: 120
    statements: 50
  gci:
    local-prefixes: github.com/golangci/golangci-lint
  goconst:
    min-len: 2
    min-occurrences: 2
  gocritic:
    enabled-tags:
      - diagnostic
      - experimental
      - opinionated
      - performance
      - style
    disabled-checks:
      - dupImport # https://github.com/go-critic/go-critic/issues/845
      - ifElseChain
      - octalLiteral
      - whyNoLint
      - wrapperFunc
  goimports:
    local-prefixes: github.com/golangci/golangci-lint
  govet:
    check-shadowing: false
    settings:
      printf:
        funcs:
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Infof
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Warnf
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Errorf
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Fatalf
  misspell:
    locale: US
  nolintlint:
    allow-leading-space: true # don't require machine-readable nolint directives (i.e. with no leading space)
    allow-unused: false # report any unused nolint directives
    require-explanation: false # don't require an explanation for nolint directives
    require-specific: false # don't require nolint directives to be specific about which linter is being skipped
  gosec:
    excludes:
      - G404
  errchkjson:
    check-error-free-encoding: true
# nilaway 空指针检测
# setting for nilaway, enable it if you need it.
#  custom:
#    nilaway:
#      type: "module"
#      description: Static analysis tool to detect potential nil panics in Go code.
#      settings:
#        # Settings must be a "map from string to string" to mimic command line flags: the keys are
#        # flag names and the values are the values to the particular flags.
#        exclude-pkgs: ""

linters:
  # please, do not use `enable-all`: it's deprecated and will be removed soon.
  # inverted configuration with `enable-all` and `disable` is not scalable during updates of golangci-lint
  disable-all: true
  enable:
    #bug linters
    -  errcheck
    -  govet
    -  staticcheck
    -  asasalint
    -  asciicheck
    -  bidichk
    -  durationcheck
    -  errchkjson
    -  exportloopref
    -  loggercheck
    -  makezero
    -  reassign
#    -  nilaway  #空指针检测
    # sucurity linters
    -  gosec

    #cognitive linters
    -  gocognit

    # style linters
    -  gosimple
    -  ineffassign
    -  unused
    -  dogsled
    -  dupl
    -  dupword
    -  errname
    -  execinquery
    -  funlen
    -  goconst
    -  gofmt
    -  goimports
    -  goprintffuncname
    -  misspell
    -  nakedret
    -  nilnil
    -  nolintlint
    -  predeclared
    -  stylecheck
    -  tenv
    -  typecheck
    -  unconvert
    -  whitespace

issues:
  # Excluding configuration per-path, per-linter, per-text and per-source
  exclude-rules:
    - path: _test\.go
      linters:
        - gomnd
  # 此处可以填入不希望golangci-lint扫描的文件或者文件夹
  # 对于想忽略的issues，可以在代码处使用 //nolint 进行忽略
  # Which dirs to exclude: issues from them won't be reported.
  # Can use regexp here: `generated.*`, regexp is applied on full path,
  # including the path prefix if one is set.
  # Default dirs are skipped independently of this option's value (see exclude-dirs-use-default).
  # "/" will be replaced by current OS file path separator to properly work on Windows.
  # Default: []
  exclude-dirs:
    - vendor
  # Which files to exclude: they will be analyzed, but issues from them won't be reported.
  # There is no need to include all autogenerated files,
  # we confidently recognize autogenerated files.
  # If it's not, please let us know.
  # "/" will be replaced by current OS file path separator to properly work on Windows.
  # Default: []
  exclude-files:
    - ".*_test\\.go$"
    - ".*mock\\.go$"
run:
  # Allow multiple parallel golangci-lint instances running.
  # If false, golangci-lint acquires file lock on start.
  # Default: false
  allow-parallel-runners: true

service:
  golangci-lint-version: 1.51.2 # use the fixed version to not introduce new linters unexpectedly
  prepare:
    - echo "here I can run custom commands, but no preparation needed for this repo"

###########此处是 UnitTest 配置##########
unit-test:
  skip-dirs:
  # goreporter 默认忽略 vendor 和 proto文件夹，如果启用以下设置，则默认设置失效，如有需要可以自行再添加
  #    此处可以添加一些不希望加入覆盖率计算的文件夹，支持正则表达式，使用正则表达式是请记得加双引号
  #    - vendor
  #    - proto
  #    - svr/client
  #   - internal/testutils
  skip-files:
  # goreporter 默认忽略 pb.go microkit.go mock.go 文件，如果启用以下设置，则默认设置失效，如有需要可以自行再添加
  #    此处可以添加一些不希望加入覆盖率计算的文件，支持正则表达式，使用正则表达式是请记得加双引号
  #    - "\\.pb\\.go$",
  #    - "\\.microkit\\.go$",
  #    - ".*mock.*\\.go$",
  options:
#    此处可以添加一些测试命令
#    - -gcflags=all=-l
###########################################