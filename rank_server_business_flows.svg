<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000">
  <defs>
    <style>
      .user { fill: #e1bee7; stroke: #8e24aa; stroke-width: 2; }
      .gateway { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .handler { fill: #c8e6c9; stroke: #4caf50; stroke-width: 2; }
      .redis { fill: #ffcdd2; stroke: #f44336; stroke-width: 2; }
      .es { fill: #ffcdd2; stroke: #f44336; stroke-width: 2; }
      .service { fill: #bbdefb; stroke: #2196f3; stroke-width: 2; }
      .process { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; }
      .title { font-family: Arial, sans-serif; font-size: 12px; font-weight: bold; text-anchor: middle; }
      .flow-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; text-anchor: start; }
      .arrow { stroke: #666; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="700" y="25" class="title" style="font-size: 18px;">Rank Server Key Business Flows</text>
  
  <!-- Flow 1: 设置/增加分数流程 -->
  <text x="50" y="60" class="flow-title"> 1. 设置/增加分数流程</text>
  <rect x="50" y="80" width="80" height="30" class="user" rx="5"/>
  <text x="90" y="100" class="text">Game User</text>
  
  <rect x="150" y="80" width="80" height="30" class="gateway" rx="5"/>
  <text x="190" y="100" class="text">User Gateway</text>
  
  <rect x="250" y="80" width="100" height="30" class="handler" rx="5"/>
  <text x="300" y="100" class="text">RankHandler V2.0</text>
  
  <rect x="370" y="80" width="80" height="30" class="process" rx="5"/>
  <text x="410" y="100" class="text">异步Channel</text>
  
  <rect x="470" y="80" width="80" height="30" class="process" rx="5"/>
  <text x="510" y="100" class="text">Worker</text>
  
  <rect x="570" y="80" width="80" height="30" class="redis" rx="5"/>
  <text x="610" y="100" class="text">Redis ZAdd</text>
  
  <rect x="670" y="80" width="80" height="30" class="es" rx="5"/>
  <text x="710" y="100" class="text">ES Writer</text>
  
  <!-- Flow 1 Arrows -->
  <line x1="130" y1="95" x2="150" y2="95" class="arrow"/>
  <line x1="230" y1="95" x2="250" y2="95" class="arrow"/>
  <line x1="350" y1="95" x2="370" y2="95" class="arrow"/>
  <line x1="450" y1="95" x2="470" y2="95" class="arrow"/>
  <line x1="550" y1="95" x2="570" y2="95" class="arrow"/>
  <line x1="650" y1="95" x2="670" y2="95" class="arrow"/>
  
  <!-- Flow 2: 查询用户排名流程 -->
  <text x="50" y="180" class="flow-title"> 2. 查询用户排名流程</text>
  <rect x="50" y="200" width="80" height="30" class="user" rx="5"/>
  <text x="90" y="220" class="text">Game User</text>
  
  <rect x="150" y="200" width="80" height="30" class="gateway" rx="5"/>
  <text x="190" y="220" class="text">User Gateway</text>
  
  <rect x="250" y="200" width="100" height="30" class="handler" rx="5"/>
  <text x="300" y="220" class="text">UserRankV2</text>
  
  <rect x="370" y="180" width="80" height="30" class="redis" rx="5"/>
  <text x="410" y="200" class="text">TopN Key</text>
  
  <rect x="370" y="220" width="80" height="30" class="redis" rx="5"/>
  <text x="410" y="240" class="text">分桶查询</text>
  
  <rect x="470" y="200" width="80" height="30" class="process" rx="5"/>
  <text x="510" y="220" class="text">排名结果</text>
  
  <!-- Flow 2 Arrows -->
  <line x1="130" y1="215" x2="150" y2="215" class="arrow"/>
  <line x1="230" y1="215" x2="250" y2="215" class="arrow"/>
  <line x1="350" y1="210" x2="370" y2="195" class="arrow"/>
  <line x1="350" y1="220" x2="370" y2="235" class="arrow"/>
  <line x1="450" y1="195" x2="470" y2="210" class="arrow"/>
  <line x1="450" y1="235" x2="470" y2="220" class="arrow"/>
  
  <!-- Flow 3: 查询排行榜流程 -->
  <text x="50" y="300" class="flow-title"> 3. 查询排行榜流程</text>
  <rect x="50" y="320" width="80" height="30" class="user" rx="5"/>
  <text x="90" y="340" class="text">Game User</text>
  
  <rect x="150" y="320" width="80" height="30" class="gateway" rx="5"/>
  <text x="190" y="340" class="text">User Gateway</text>
  
  <rect x="250" y="320" width="100" height="30" class="handler" rx="5"/>
  <text x="300" y="340" class="text">RankListV2</text>
  
  <rect x="370" y="320" width="80" height="30" class="service" rx="5"/>
  <text x="410" y="340" class="text">Game Service</text>
  
  <rect x="470" y="300" width="80" height="30" class="redis" rx="5"/>
  <text x="510" y="320" class="text">TopN查询</text>
  
  <rect x="470" y="340" width="80" height="30" class="redis" rx="5"/>
  <text x="510" y="360" class="text">分桶查询</text>
  
  <rect x="570" y="320" width="80" height="30" class="process" rx="5"/>
  <text x="610" y="340" class="text">排行榜结果</text>
  
  <!-- Flow 3 Arrows -->
  <line x1="130" y1="335" x2="150" y2="335" class="arrow"/>
  <line x1="230" y1="335" x2="250" y2="335" class="arrow"/>
  <line x1="350" y1="335" x2="370" y2="335" class="arrow"/>
  <line x1="450" y1="330" x2="470" y2="315" class="arrow"/>
  <line x1="450" y1="340" x2="470" y2="355" class="arrow"/>
  <line x1="550" y1="315" x2="570" y2="330" class="arrow"/>
  <line x1="550" y1="355" x2="570" y2="340" class="arrow"/>
  
  <!-- Flow 4: 好友排行榜流程 -->
  <text x="50" y="420" class="flow-title"> 4. 好友排行榜流程</text>
  <rect x="50" y="440" width="80" height="30" class="user" rx="5"/>
  <text x="90" y="460" class="text">Game User</text>
  
  <rect x="150" y="440" width="80" height="30" class="gateway" rx="5"/>
  <text x="190" y="460" class="text">User Gateway</text>
  
  <rect x="250" y="440" width="100" height="30" class="handler" rx="5"/>
  <text x="300" y="460" class="text">FriendsRankV3</text>
  
  <rect x="370" y="440" width="80" height="30" class="service" rx="5"/>
  <text x="410" y="460" class="text">Friend Service</text>
  
  <rect x="470" y="440" width="80" height="30" class="process" rx="5"/>
  <text x="510" y="460" class="text">好友列表</text>
  
  <rect x="570" y="440" width="80" height="30" class="es" rx="5"/>
  <text x="610" y="460" class="text">ES批量查询</text>
  
  <rect x="670" y="440" width="80" height="30" class="process" rx="5"/>
  <text x="710" y="460" class="text">好友排行榜</text>
  
  <!-- Flow 4 Arrows -->
  <line x1="130" y1="455" x2="150" y2="455" class="arrow"/>
  <line x1="230" y1="455" x2="250" y2="455" class="arrow"/>
  <line x1="350" y1="455" x2="370" y2="455" class="arrow"/>
  <line x1="450" y1="455" x2="470" y2="455" class="arrow"/>
  <line x1="550" y1="455" x2="570" y2="455" class="arrow"/>
  <line x1="650" y1="455" x2="670" y2="455" class="arrow"/>
  
  <!-- Flow 5: 管理员批量操作流程 -->
  <text x="50" y="540" class="flow-title">️ 5. 管理员批量操作流程</text>
  <rect x="50" y="560" width="80" height="30" class="user" rx="5"/>
  <text x="90" y="580" class="text">Games Admin</text>
  
  <rect x="150" y="560" width="80" height="30" class="gateway" rx="5"/>
  <text x="190" y="580" class="text">Admin Gateway</text>
  
  <rect x="250" y="560" width="100" height="30" class="handler" rx="5"/>
  <text x="300" y="580" class="text">IncrScoreV3Admin</text>
  
  <rect x="370" y="560" width="80" height="30" class="es" rx="5"/>
  <text x="410" y="580" class="text">ES查询更新</text>
  
  <rect x="470" y="560" width="80" height="30" class="redis" rx="5"/>
  <text x="510" y="580" class="text">Redis同步</text>
  
  <rect x="570" y="560" width="80" height="30" class="process" rx="5"/>
  <text x="610" y="580" class="text">操作完成</text>
  
  <!-- Flow 5 Arrows -->
  <line x1="130" y1="575" x2="150" y2="575" class="arrow"/>
  <line x1="230" y1="575" x2="250" y2="575" class="arrow"/>
  <line x1="350" y1="575" x2="370" y2="575" class="arrow"/>
  <line x1="450" y1="575" x2="470" y2="575" class="arrow"/>
  <line x1="550" y1="575" x2="570" y2="575" class="arrow"/>
  
  <!-- Legend -->
  <rect x="50" y="650" width="300" height="200" fill="none" stroke="#ccc" stroke-width="1" rx="5"/>
  <text x="200" y="675" class="title">图例说明</text>
  
  <rect x="70" y="690" width="20" height="15" class="user"/>
  <text x="100" y="702" class="text" style="text-anchor: start;">用户/管理员</text>
  
  <rect x="70" y="710" width="20" height="15" class="gateway"/>
  <text x="100" y="722" class="text" style="text-anchor: start;">网关层</text>
  
  <rect x="70" y="730" width="20" height="15" class="handler"/>
  <text x="100" y="742" class="text" style="text-anchor: start;">业务处理器</text>
  
  <rect x="70" y="750" width="20" height="15" class="service"/>
  <text x="100" y="762" class="text" style="text-anchor: start;">外部服务</text>
  
  <rect x="70" y="770" width="20" height="15" class="redis"/>
  <text x="100" y="782" class="text" style="text-anchor: start;">Redis/ES存储</text>
  
  <rect x="70" y="790" width="20" height="15" class="process"/>
  <text x="100" y="802" class="text" style="text-anchor: start;">处理过程</text>
  
</svg>
