package tests

import (
	"context"
	"git.garena.com/shopee/feed/microkit"
	"git.garena.com/shopee/game_platform/proto/game_rank"
	"github.com/golang/protobuf/proto"
	"testing"
)

const (
	scope    = "9h2sd3LQjZ2AcYDTZW"
	rankName = "1"
	rankType = 0
	host     = "*************:58837"
	userId   = 12001101401
	UserName = "liwei0507.51"
	EventId  = 1
)

func init() {
	microkit.Init()
}

func TestShowRankList(t *testing.T) {
	req := &game_rank.RankListRequestV2{
		EventId:  proto.Int32(EventId),
		Scope:    proto.String(scope),
		RankType: proto.Uint32(rankType),
		UserName: proto.String(""),
		RankWeek: proto.String(""),
	}
	resp, err := game_rank.NewClient().RankListV2(context.Background(), req,
		microkit.SpecifyHost(host))
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestIncrV2(t *testing.T) {
	req := &game_rank.IncrScoreRequestV2{
		Req: &game_rank.RankV2CommonRequest{
			Scope:    proto.String(scope),
			RankName: proto.String(rankName),
			RankType: proto.Uint32(rankType),
		},
		Score:    proto.Float64(123),
		UserName: proto.String(UserName),
		UserId:   proto.Uint64(userId),
	}
	resp, err := game_rank.NewClient().IncrScoreV2(context.Background(), req,
		microkit.SpecifyHost(host))
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestDeleteUserRankV2(t *testing.T) {
	req := &game_rank.DeleteUserRankRequestV2{
		Req: &game_rank.RankV2CommonRequest{
			Scope:    proto.String(scope),
			RankName: proto.String(rankName),
			RankType: proto.Uint32(rankType),
		},
		UserId: proto.Uint64(userId),
	}
	resp, err := game_rank.NewClient().DeleteUserRankV2(context.Background(), req,
		microkit.SpecifyHost(host))
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestIncrV1(t *testing.T) {
	req := &game_rank.IncrScoreRequest{
		Scope:    proto.String(scope),
		RankName: proto.String(rankName),
		Score:    proto.Float64(10),
		UserName: proto.String("liwei0507.4"),
		UserId:   proto.Uint64(1200110140),
	}
	resp, err := game_rank.NewClient().IncrScore(context.Background(), req,
		microkit.SpecifyHost(host))
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestTopnV1(t *testing.T) {
	req := &game_rank.TopnRequest{
		Scope:    proto.String("9h2sd3LQjZ2AcYDTZW"),
		RankName: proto.String(rankName),
		N:        proto.Int(100),
	}
	resp, err := game_rank.NewClient().Topn(context.Background(), req,
		microkit.SpecifyHost(host))
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestUserRankV1(t *testing.T) {
	req := &game_rank.UserRankRequest{
		Scope:    proto.String("9h2sd3LQjZ2AcYDTZW"),
		RankName: proto.String(rankName),
		UserName: proto.String("liwei0507.4"),
		UserId:   proto.Uint64(1200110140),
	}
	resp, err := game_rank.NewClient().UserRank(context.Background(), req,
		microkit.SpecifyHost(host))
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestUserScoreV1(t *testing.T) {
	req := &game_rank.UserScoreRequest{
		Scope:    proto.String(scope),
		RankName: proto.String(rankName),
		UserName: proto.String(UserName),
		UserId:   proto.Uint64(userId),
	}
	resp, err := game_rank.NewClient().UserScore(context.Background(), req,
		microkit.SpecifyHost(host))
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestTopnV2(t *testing.T) {
	req := &game_rank.TopNRequestV2{
		Req: &game_rank.RankV2CommonRequest{
			Scope:    proto.String(scope),
			RankName: proto.String(rankName),
			RankType: proto.Uint32(rankType),
		},
		N: proto.Int(100),
	}
	resp, err := game_rank.NewClient().TopNV2(context.Background(), req,
		microkit.SpecifyHost(host))
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestUserRankV2(t *testing.T) {
	req := &game_rank.UserRankRequestV2{
		Req: &game_rank.RankV2CommonRequest{
			Scope:    proto.String(scope),
			RankName: proto.String(rankName),
			RankType: proto.Uint32(rankType),
		},
		UserName: proto.String("liwei0507.4"),
		UserId:   proto.Uint64(1200110140),
	}
	resp, err := game_rank.NewClient().UserRankV2(context.Background(), req,
		microkit.SpecifyHost(host))
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestUserScoreV2(t *testing.T) {
	req := &game_rank.UserScoreRequestV2{
		Req: &game_rank.RankV2CommonRequest{
			Scope:    proto.String(scope),
			RankName: proto.String(rankName),
			RankType: proto.Uint32(rankType),
		},
		UserName: proto.String("liwei0507.4"),
		UserId:   proto.Uint64(1200110140),
	}
	resp, err := game_rank.NewClient().UserScoreV2(context.Background(), req,
		microkit.SpecifyHost(host))
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestUserScoreListV2(t *testing.T) {
	req := &game_rank.UserScoreListRequestV2{
		Req: &game_rank.RankV2CommonRequest{
			Scope:    proto.String(scope),
			RankName: proto.String(rankName),
			RankType: proto.Uint32(rankType),
		},
		UserIds: []uint64{1200110140},
		Detail:  proto.Bool(true),
	}
	resp, err := game_rank.NewClient().UserScoreListV2(context.Background(), req,
		microkit.SpecifyHost(host))
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}
