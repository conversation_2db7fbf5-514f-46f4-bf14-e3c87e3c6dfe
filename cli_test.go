package main

import (
	"context"
	"fmt"
	"math/rand"
	"testing"
	"time"

	"git.garena.com/shopee/feed/microkit"
	"git.garena.com/shopee/game_platform/proto/game_rank"
	"git.garena.com/shopee/game_platform/rank_server/handler"
	"github.com/golang/protobuf/proto"
)

func TestIncrScore(t *testing.T) {
	microkit.Init()
	cli := game_rank.NewClient()
	rankName, _ := handler.WeekRankName(100, 27)
	scope := "gamerc"

	for i := 0; i < 11; i++ {
		incrRes, err := cli.IncrScore(context.TODO(), &game_rank.IncrScoreRequest{
			Scope:    proto.String(scope),
			RankName: proto.String(rankName),
			Score:    proto.Float64(float64(i)),
			UserName: proto.String("test"),
			UserId:   proto.Uint64(uint64(i)),
		})
		time.Sleep(time.Millisecond)
		fmt.Println("IncScore:", incrRes, err)
	}

}

func TestIncrScore2(t *testing.T) {
	microkit.Init()
	cli := game_rank.NewClient()
	rankName, _ := handler.WeekRankName(10, 27)
	scope := "gamerc"

	for j := 0; j < 100; j++ {
		for i := 0; i < 11; i++ {
			incrRes, err := cli.IncrScore(context.TODO(), &game_rank.IncrScoreRequest{
				Scope:    proto.String(scope),
				RankName: proto.String(rankName),
				Score:    proto.Float64(float64(i)),
				UserName: proto.String("test"),
				UserId:   proto.Uint64(uint64(i)),
			})
			time.Sleep(time.Millisecond)
			fmt.Println("IncScore:", incrRes, err)
		}
	}

}

func TestIncrOneScore(t *testing.T) {
	microkit.Init()
	cli := game_rank.NewClient()
	rankName, _ := handler.WeekRankName(157, 27)
	scope := "gamerc"

	incrRes, err := cli.IncrScore(context.TODO(), &game_rank.IncrScoreRequest{
		Scope:    proto.String(scope),
		RankName: proto.String(rankName),
		Score:    proto.Float64(68.24),
		UserName: proto.String("test"),
		UserId:   proto.Uint64(3000002),
	})
	time.Sleep(time.Millisecond)
	fmt.Println("IncScore:", incrRes, err)

}

func TestSameScore(t *testing.T) {
	microkit.Init()
	cli := game_rank.NewClient()
	rankName, _ := handler.WeekRankName(157, 27)
	scope := "gamerc"

	//for i := 500; i < 510; i++ {
	//	incrRes, err := cli.IncrScore(context.TODO(), &game_rank.IncrScoreRequest{
	//		Scope:    proto.String(scope),
	//		RankName: proto.String(rankName),
	//		Score:    proto.Float64(float64(i)),
	//		UserName: proto.String("test"),
	//		UserId:   proto.Uint64(uint64(i)),
	//	})
	//	time.Sleep(time.Millisecond)
	//	fmt.Println("IncScore:", incrRes, err)
	//}
	incrRes, err := cli.IncrScore(context.TODO(), &game_rank.IncrScoreRequest{
		Scope:    proto.String(scope),
		RankName: proto.String(rankName),
		Score:    proto.Float64(0),
		UserName: proto.String("test"),
		UserId:   proto.Uint64(19999),
	})
	time.Sleep(time.Millisecond)
	fmt.Println("IncScore:", incrRes, err)
}

func TestSameScoreSortWithTime(t *testing.T) {
	microkit.Init()
	cli := game_rank.NewClient()
	rankName, _ := handler.WeekRankName(157, 27)
	scope := "gamerc"

	for i := 0; i < 20; i++ {
		incrRes, err := cli.IncrScore(context.TODO(), &game_rank.IncrScoreRequest{
			Scope:    proto.String(scope),
			RankName: proto.String(rankName),
			Score:    proto.Float64(float64(30)),
			UserName: proto.String("test"),
			UserId:   proto.Uint64(uint64(i)),
		})
		time.Sleep(time.Millisecond * 2)
		fmt.Println("IncScore:", incrRes, err)
	}
	//for i := 0; i < 20000; i++ {
	//	incrRes, err := cli.IncrScore(context.TODO(), &game_rank.IncrScoreRequest{
	//		Scope:    proto.String(scope),
	//		RankName: proto.String(rankName),
	//		Score:    proto.Float32(float32(2000 - i)),
	//		UserName: proto.String("test"),
	//		UserId:   proto.Uint64(uint64(i)),
	//	})
	//	time.Sleep(time.Millisecond)
	//	fmt.Println("IncScore:", incrRes, err)
	//}

}

func TestGetOneUserRank(t *testing.T) {
	microkit.Init()
	rand.Seed(time.Now().Unix())
	cli := game_rank.NewClient()
	rankName, _ := handler.WeekRankName(157, 27)
	scope := "gamerc"

	rankRes, err := cli.UserRank(context.Background(), &game_rank.UserRankRequest{
		Scope:    proto.String(scope),
		RankName: proto.String(rankName),
		UserName: proto.String("test"),
		UserId:   proto.Uint64(19999),
	})
	fmt.Println("UserRank:", rankRes, err)

}

func TestGetOneUserScore(t *testing.T) {
	microkit.Init()
	rand.Seed(time.Now().Unix())
	cli := game_rank.NewClient()
	rankName, _ := handler.WeekRankName(157, 27)
	scope := "gamerc"

	scoreRes, err := cli.UserScore(context.Background(), &game_rank.UserScoreRequest{
		Scope:    proto.String(scope),
		RankName: proto.String(rankName),
		UserName: proto.String("test"),
		UserId:   proto.Uint64(uint64(3000002)),
	})
	fmt.Println("UserScore:", scoreRes, err)

}

func TestTopn(t *testing.T) {
	microkit.Init()
	rand.Seed(time.Now().Unix())
	cli := game_rank.NewClient()
	rankName, _ := handler.WeekRankName(157, 27)
	scope := "gamerc"

	topNRes, err := cli.Topn(context.Background(), &game_rank.TopnRequest{
		Scope:    proto.String(scope),
		RankName: proto.String(rankName),
		N:        proto.Int32(2000),
	})
	fmt.Println("topNRes:", topNRes, err)
}

func TestDownLadTopn(t *testing.T) {
	microkit.Init()
	rand.Seed(time.Now().Unix())
	cli := game_rank.NewClient()
	rankName, _ := handler.WeekRankName(157, 27)
	scope := "gamerc"

	topNRes, err := cli.DownLoadTopn(context.Background(), &game_rank.TopnRequest{
		Scope:    proto.String(scope),
		RankName: proto.String(rankName),
		N:        proto.Int32(2000),
	})
	fmt.Println("topNRes:", topNRes, err)
}

func TestTopnAsc(t *testing.T) {
	microkit.Init()
	rand.Seed(time.Now().Unix())
	cli := game_rank.NewClient()
	rankName, _ := handler.WeekRankName(157, 27)
	scope := "gamerc"
	topNRes, err := cli.Topn(context.Background(), &game_rank.TopnRequest{
		Scope:    proto.String(scope),
		RankName: proto.String(rankName),
		N:        proto.Int32(1000),
		Reverse:  proto.Bool(true),
	})
	fmt.Println("topNRes:", topNRes, err)
}

func TestDeleteUserRank(t *testing.T) {
	microkit.Init()
	cli := game_rank.NewClient()
	rankName, _ := handler.WeekRankName(157, 27)
	scope := "gamerc"
	deleteUserRankRes, err := cli.DeleteUserRank(context.Background(), &game_rank.DeleteUserRankRequest{
		Scope:    proto.String(scope),
		RankName: proto.String(rankName),
		UserId:   proto.Uint64(19995),
	})
	fmt.Println("deleteUserRankRes:", deleteUserRankRes, err)
}

func TestDeleteRank(t *testing.T) {
	microkit.Init()
	cli := game_rank.NewClient()
	rankName, _ := handler.WeekRankName(157, 27)
	scope := "gamerc"
	deleteRankRes, err := cli.DeleteRank(context.Background(), &game_rank.DeleteRankRequest{
		Scope:    proto.String(scope),
		RankName: proto.String(rankName),
	})
	fmt.Println("deleteRankRes:", deleteRankRes, err)
}

func TestUserScoreList(t *testing.T) {
	microkit.Init()
	cli := game_rank.NewClient()
	rankName, _ := handler.WeekRankName(157, 27)
	scope := "gamerc"
	userids := []uint64{19999, 19998, 19997, 19996}
	userScoreListRes, err := cli.UserScoreList(context.Background(), &game_rank.UserScoreListRequest{
		Scope:    proto.String(scope),
		RankName: proto.String(rankName),
		UserIds:  userids,
	})
	fmt.Println("userScoreListRes:", userScoreListRes, err)

	userScoreListRes, err = cli.UserScoreList(context.Background(), &game_rank.UserScoreListRequest{
		Scope:    proto.String(scope),
		RankName: proto.String(rankName),
		UserIds:  userids,
		Detail:   proto.Bool(true),
	})
	fmt.Println("userScoreListRes:", userScoreListRes, err)
}
