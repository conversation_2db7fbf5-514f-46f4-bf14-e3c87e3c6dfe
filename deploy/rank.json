{"project_dir_depth": 2, "project_name": "game", "module_name": "rank", "build": {"docker_image": {"base_image": "harbor.shopeemobile.com/shopee/golang-base:1.20.7-20", "dependent_libraries_files": [], "run_commands": ["export GO111MODULE=on"]}, "template_files": [], "commands": ["echo '#####'", "echo '# Setting up game rank micro service'", "echo '#####'", "echo $(pwd) ${GOPATH}", "ls -lh", "go build -o svr ./"]}, "run": {"enable_cni": true, "enable_prometheus": true, "prometheus_port": 9090, "depend_services": [], "command": "GOTRACEBACK=crash ./svr -config conf/rank_conf.yml", "port_definitions": [{"submodule": "rank", "expose_port": 8080}, {"submodule": "pprof", "expose_port": 9090, "disable_check": true}], "smoke": {"protocol": "GRPC", "timeout": 5, "retry": 10}, "check": {"protocol": "GRPC", "timeout": 5, "retry": 10}, "shutdown": {"test": {"unregister_deadline_seconds": 5, "terminate_deadline_seconds": 8}, "uat": {"unregister_deadline_seconds": 5, "terminate_deadline_seconds": 8}, "live": {"unregister_deadline_seconds": 5, "terminate_deadline_seconds": 8}}, "acquire_prometheus_port": true}, "deploy": {"idcs": {"live": {"sg": ["sg5"], "my": ["sg5"], "th": ["sg5"], "ph": ["sg5"], "vn": ["sg5"], "id": ["sg5"], "xx": ["sg5"], "br": ["us3"], "mx": ["sg5"], "co": ["sg5"], "cl": ["sg5"], "tw": ["sg5"], "ar": ["sg5"]}}, "#": "The default value of resources is 1 cpu and 1024MB mem.", "resources": {"live": {"cpu": 4, "mem": 16384}, "test": {"cpu": 1, "mem": 2048}, "uat": {"cpu": 1, "mem": 1024}, "staging": {"cpu": 1, "mem": 1024}}, "instances": {"live": {"sg": 2, "my": 2, "ph": 4, "th": 4, "vn": 4, "id": 30, "xx": 30, "br": 2, "mx": 2, "co": 2, "cl": 2, "tw": 2, "ar": 2}}}}