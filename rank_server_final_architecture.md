# Rank Server Architecture Documentation

## Overview

The Rank Server is a high-performance game ranking system built with microservices architecture. It provides comprehensive ranking functionality including score management, leaderboard queries, friend rankings, and administrative operations.

## Architecture Components

### 1. **Client Layer**
External clients that interact with the ranking system:

- **Games User** - Game client applications where players interact with rankings
- **Games Admin** - Administrative portal for game operators and managers
- **ODP** - Operations Data Platform for system monitoring and data analysis

### 2. **SPEX (Service Platform Extension)**
Core infrastructure services for service discovery and configuration:

- **gRPC Service Discovery (grpc4spex.Init)** - Automatic service discovery and registration
- **Service Connection (grpc4spex.GetConn)** - Connection pool management and load balancing
- **Configuration Center (config_center.get)** - Centralized configuration management
- **Service Registry (service_registry.register)** - Service registration and health monitoring

### 3. **Application Services**

#### **User Gateway**
Primary interface for game users with comprehensive ranking operations:

- **SetScoreV2/V3** - Set user scores with version support
- **IncrScoreV2/V3** - Increment user scores atomically
- **TopNV2/V3** - Retrieve top N rankings with optimization
- **FriendsRankV3** - Friend-based ranking queries
- **UserRankV2/V3** - Get individual user ranking position
- **UserScoreV2/V3** - Retrieve user score information
- **UserScoreListV2/V3** - Batch user score queries

#### **Admin Gateway**
Administrative interface for system management and operations:

- **IncrScoreV3Admin** - Administrative score increment with elevated privileges
- **RankListV2** - Administrative ranking list queries
- **DeleteUserRankV2** - Remove user from rankings
- **QueryNewRankModule** - Query ranking module configurations
- **UpsertNewRankModule** - Create or update ranking modules
- **ListNewRankModule** - List all available ranking modules

#### **Task Scheduler**
Background task management for system maintenance:

- **AsyncCloseEsIndex** - Automated Elasticsearch index lifecycle management
- **tryAcquireTaskLock** - Distributed task locking mechanism
- **doCloseEsIndex** - Execute index closure operations
- **ClearUserInfoLocalCache** - Local cache cleanup and memory management

#### **Middleware**
Cross-cutting concerns for all requests:

- **Access Log** - Request/response logging and audit trails
- **Metrics** - Performance monitoring and metrics collection
- **Tracing** - Distributed tracing for request flow analysis
- **Recovery** - Panic recovery and error handling
- **Health Check** - Service health monitoring and status reporting

#### **Async Processing**
Asynchronous data processing pipeline:

- **Rank Writer** - Asynchronous ranking data processing with channel-based workers
- **ES Writer** - Bulk Elasticsearch operations for improved performance
- **Data Access Layer** - Abstraction layer for storage operations

#### **Business Logic Handlers**
Core business logic implementation with version support:

- **Rank Handler V1.0/V2.0/V3.0** - Evolution of ranking algorithms and optimizations
- **Friends Handler** - Friend-specific ranking business logic
- **Module Handler** - Configuration and module management logic

### 4. **Upstream Services**
External services that provide additional functionality:

- **Game Platform** - Core game services including:
  - Blacklist management and anti-cheat integration
  - Game configuration and activity management
  - User validation and authorization

- **Friend Service** - Social features including:
  - Friend relationship management
  - User profile information
  - Social interaction data

### 5. **Storage Layer**
Persistent data storage with specialized purposes:

- **Redis** - High-performance caching layer
  - Real-time ranking cache with bucket strategy
  - TopN optimization for frequent queries
  - Distributed locking for task coordination

- **Elasticsearch** - Search and analytics engine
  - Complex ranking queries and aggregations
  - Historical data storage and analysis
  - Full-text search capabilities

- **MySQL** - Relational database
  - Configuration data management
  - User recovery data and audit logs
  - Module settings and metadata

## Key Architectural Principles

### 1. **Scalability**
- Bucket strategy for Redis to distribute load
- Asynchronous processing to handle high throughput
- Multi-version API support for gradual migration

### 2. **Reliability**
- Distributed task locking prevents duplicate operations
- Automatic retry mechanisms for failed operations
- Health monitoring and recovery procedures

### 3. **Performance Optimization**
- TopN key optimization for frequent leaderboard queries
- Local caching to reduce external service calls
- Bulk operations for improved database performance

### 4. **Service Integration**
- Standardized SPEX integration for all services
- Clean separation between gateway and business logic
- External service abstraction for maintainability

### 5. **Data Consistency**
- Dual-write mechanism for cache and persistent storage
- Eventual consistency model for high availability
- Data recovery mechanisms for system resilience

## Service Dependencies

| Service | Redis | Elasticsearch | MySQL | Upstream Services | SPEX |
|---------|-------|---------------|-------|-------------------|------|
| User Gateway | ✅ | ✅ | ❌ | ✅ | ✅ |
| Admin Gateway | ✅ | ✅ | ✅ | ✅ | ✅ |
| Task Scheduler | ✅ | ✅ | ❌ | ❌ | ✅ |
| Business Logic | ✅ | ✅ | ✅ | ✅ | ✅ |
| Middleware | ❌ | ❌ | ❌ | ❌ | ✅ |

## Request Flow Examples

### User Score Update
```
Games User → User Gateway → Business Logic Handlers → Async Processing → Redis/ES
```

### Admin Operations
```
ODP → Admin Gateway → Business Logic Handlers → Direct ES Operations → Redis Sync
```

### Friend Rankings
```
Games User → User Gateway → Business Logic Handlers → Friend Service → ES Query → Response
```

This architecture provides a robust, scalable, and maintainable foundation for game ranking systems, supporting both real-time user operations and comprehensive administrative functions.
