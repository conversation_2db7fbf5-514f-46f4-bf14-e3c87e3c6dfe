package constant

const (
	// no platform scope name
	GameRCScope = "gamerc"
	GameARScope = "gamear"

	// redis key
	// rankingKey = scopeRankName.bucket = scope.rankName.bucket
	rankingKeyFormat    = "%s.%s"
	scopeRankNameFormat = "%s.%s"

	// es index live prefix
	EsLiveIndexPrefix = "mkt_"

	// es index
	// explain games_appID_env_region_rank_rankName_year.week
	// example games_1_live_sg_rank_157_2019.34
	WriteRankIndexFormatV2 = EsLiveIndexPrefix + "games_%s_%s_%s_rank_%s_%s"

	// scope_env_region_rankName_year.week
	IndexBasicFormat              = EsLiveIndexPrefix + "rank_%s_%s_%s_%s_%s"
	WriteRankDetailIndexFormatV2  = IndexBasicFormat + "_add_detail"
	DeleteRankDetailIndexFormatV2 = IndexBasicFormat + "_delete_detail"

	// consume rankEsChan constants
	DefaultRankEsChanCap   = 1024 * 1024
	DefaultBufferWriteSize = 64
	ConsumerRetrys         = 10

	// prometheus monitor label
	RankingsEsChanLabel         = "rankingsEsChan"
	RankDetailEsChanLabel       = "rankDetailEsChan"
	DeleteRankingsEsChanLabel   = "deleteRankingsEsChan"
	DeleteRankDetailEsChanLabel = "deleteRankDetailEsChan"

	// Es Operation
	EsAdd    = "es_operation_add"
	EsDelete = "es_operation_delete"

	// Es Semaphore
	DefaultSemaphore = 32

	// Es http client
	HttpClientMaxConnsPerHost = 64

	// Es scroll setting
	EsScrollLimit = 9000

	// Rank Type
	RankTypeRetention = 0
	RankTypeWeek      = 1
	RankTypeDaily     = 2
	RankTypeMonth     = 3

	// Rank Key
	RankRetention = "retention"
	RankDaily     = "daily"
	RankMonthly   = "monthly"

	// Expiration
	RankKeyWeekExp      = 691200  // 8 day
	RankKeyDailyExp     = 172800  // 2 day
	RankKeyMonthExp     = 2764800 // 32 day
	RankKeyRetentionExp = 8640000 // 100 day
	RankKeyStressExp    = 3600    // 1 hour

	// base timestamp
	//DefaultBaseTimestamp = 3587710117  //此值会导致redis拼接的时间戳过大，会有分数默认增加0.01的问题
	DefaultBaseTimestamp = 2222307393

	UsersRankTimestampExp       = 7776000
	UsersRankTimestampExpStress = 180

	MaxRankEsScore = uint64(2 << 62)

	FlowTypeAdmin = "AdminFlow"

	UserLimit            = 1000
	MetricUserLimitTotal = "gp_rank_user_limit_total"
)
