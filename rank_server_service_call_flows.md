# Rank Server 服务调用流程

## 1. 用户分数设置流程

### **SetScoreV2/V3 调用链路**
```
Game Client → User Gateway → Business Logic Handler → Async Processing → Storage
```

#### **详细调用步骤：**
1. **Client Request**
   ```
   Game Client → gRPC → HandlerWarp.SetScoreV2()
   ```

2. **Gateway Layer**
   ```go
   HandlerWarp.SetScoreV2() → RankHandler.SetScoreV2()
   ```

3. **Business Logic**
   ```go
   RankHandler.SetScoreV2() → {
       util.CheckUserInBlacklist()           // 黑名单检查
       checkRankWeekOrFill()                 // 参数验证
       RankWriter.putRankChan()              // 异步处理
   }
   ```

4. **Async Processing**
   ```go
   RankWriter.putRankChan() → {
       doSetRankScore()                      // Worker处理
       cache.RedisUtilZAdd()                 // Redis写入
       DoubleWriteTopNKey()                  // TopN优化
       RankingsEsWriter.Write()              // ES异步写入
   }
   ```

5. **External Service Calls**
   ```go
   gameplatform_game.CheckBlacklistOpen()   // 黑名单服务
   ```

---

## 2. 排行榜查询流程

### **TopNV2/V3 调用链路**
```
Game Client → User Gateway → Business Logic Handler → Game Platform → Storage → Response
```

#### **详细调用步骤：**
1. **Client Request**
   ```
   Game Client → gRPC → HandlerWarp.TopNV2()
   ```

2. **Gateway Layer**
   ```go
   HandlerWarp.TopNV2() → RankHandler.TopNV2()
   ```

3. **Business Logic**
   ```go
   RankHandler.TopNV2() → {
       checkRankWeekOrFill()                 // 参数验证
       anticheat_rpc.CheckBlacklistOpen()    // 黑名单检查
       QueryTopNFromSingleKey() OR           // TopN查询
       allbucketZSetRange()                  // 分桶查询
   }
   ```

4. **External Service Calls**
   ```go
   gameplatform_game.GameGrpcClient         // 游戏平台服务
   gameplatform_friend.BatchGetUserBaseInfo() // 用户信息服务
   ```

5. **Storage Access**
   ```go
   cache.RedisUtilZRange()                  // Redis查询
   esutil.EsClient.Search()                 // ES查询（如需要）
   ```

---

## 3. 好友排行榜流程

### **FriendsRankV3 调用链路**
```
Game Client → User Gateway → Friend Service → Business Logic → ES Query → Response
```

#### **详细调用步骤：**
1. **Client Request**
   ```
   Game Client → gRPC → HandlerWarp.FriendsRankV3()
   ```

2. **Gateway Layer**
   ```go
   HandlerWarp.FriendsRankV3() → RankHandler.FriendsRankV3()
   ```

3. **Business Logic**
   ```go
   RankHandler.FriendsRankV3() → {
       CheckParamValid()                     // 参数验证
       getContactFriends()                   // 获取好友列表
       QueryFriendsRank()                    // 查询好友排名
   }
   ```

4. **Friend Service Integration**
   ```go
   GetFriends() → {
       gameplatform_friend.GetGrpcClient()
       cli.GetFriends()                      // 好友服务调用
   }
   ```

5. **Batch Query**
   ```go
   QueryFriendsRank() → {
       UserScoreListV3()                     // 批量查询分数
       esQueryRankListV2ByUsers()            // ES批量查询
       sort.Slice()                          // 内存排序
   }
   ```

---

## 4. 管理员操作流程

### **IncrScoreV3Admin 调用链路**
```
ODP/Admin Portal → Admin Gateway → Business Logic → Direct ES → Optional Redis Sync
```

#### **详细调用步骤：**
1. **Admin Request**
   ```
   Admin Portal → gRPC → HandlerWarp.IncrScoreV3Admin()
   ```

2. **Gateway Layer**
   ```go
   HandlerWarp.IncrScoreV3Admin() → RankHandler.IncrScoreV3Admin()
   ```

3. **Business Logic**
   ```go
   RankHandler.IncrScoreV3Admin() → {
       CheckParamValid()                     // 参数验证
       esutil.EsClient.Get()                 // 查询当前数据
       esutil.EsClient.Update()              // 直接更新ES
       cache.RedisUtilZAdd()                 // 可选Redis同步
       DoubleWriteTopNKey()                  // TopN一致性
   }
   ```

4. **Direct Storage Access**
   ```go
   esutil.EsClient.Get()                    // ES直接查询
   esutil.EsClient.Update()                 // ES直接更新
   cache.RedisUtilZAdd()                    // Redis同步（可选）
   ```

---

## 5. 配置管理流程

### **UpsertNewRankModule 调用链路**
```
Admin Portal → Admin Gateway → Business Logic → MySQL → Game Platform Validation
```

#### **详细调用步骤：**
1. **Admin Request**
   ```
   Admin Portal → gRPC → HandlerWarp.UpsertNewRankModule()
   ```

2. **Gateway Layer**
   ```go
   HandlerWarp.UpsertNewRankModule() → RankHandler.UpsertNewRankModule()
   ```

3. **Business Logic**
   ```go
   RankHandler.UpsertNewRankModule() → {
       isConfigValid()                       // 配置验证
       createNewRankModule() OR              // 创建新模块
       updateNewRankModule()                 // 更新现有模块
   }
   ```

4. **Database Operations**
   ```go
   createNewRankModule() → {
       dao.NewRankDao().CreateNewRank()      // 创建基础记录
       dao.NewRankDao().CreateNewRankConfigVersion() // 创建配置版本
   }
   ```

5. **Game Platform Integration**
   ```go
   GetCachedNewRankModule() → {
       gameplatform_game.GetGrpcClient()
       cli.GetEventIDByModule()              // 获取事件ID
       cli.GetGameActivity()                 // 获取活动信息
   }
   ```

---

## 6. 任务调度流程

### **AsyncCloseEsIndex 调用链路**
```
Timer Trigger → Task Scheduler → Distributed Lock → ES Operations
```

#### **详细调用步骤：**
1. **Timer Trigger**
   ```go
   time.After(time.Hour * 24) → doCloseEsIndex()
   ```

2. **Distributed Lock**
   ```go
   doCloseEsIndex() → {
       tryAcquireTaskLock()                  // 获取分布式锁
       cache.RedisUtilSetNX()                // Redis锁实现
   }
   ```

3. **ES Operations**
   ```go
   esutil.EsClient.CatIndices()             // 获取所有索引
   needCloseIndex()                         // 判断是否需要关闭
   esutil.EsClient.CloseIndex()             // 关闭过期索引
   ```

4. **Lock Release**
   ```go
   delTaskLock()                            // 释放分布式锁
   ```

---

## 7. 服务启动流程

### **Application Bootstrap 调用链路**
```
Main → SPEX Init → Service Registration → Handler Setup → Server Start
```

#### **详细调用步骤：**
1. **SPEX Initialization**
   ```go
   grpc4spex.Init()                         // 服务发现初始化
   grpc4spex.WithSpexConfig()               // 配置加载
   ```

2. **Service Registration**
   ```go
   grpcSPI.RegisterServer() → {
       grpc_server.NewHandlerWarp()          // 创建处理器
       Registerer.Register()                 // 注册gRPC服务
   }
   ```

3. **Middleware Setup**
   ```go
   gas.Construct() → {
       accesslog-interceptor                 // 访问日志
       metrics-interceptor                   // 指标监控
       trace-interceptor                     // 链路追踪
       recovery-interceptor                  // 异常恢复
       healthchecker                         // 健康检查
   }
   ```

4. **Background Tasks**
   ```go
   AsyncCloseEsIndex()                      // 启动ES清理任务
   RunCronOfUserInfoLocalCache()            // 启动缓存清理任务
   ```

---

## 8. 错误处理流程

### **Error Handling Chain**
```
Business Logic Error → BizError → CommonResponse → Client Response
```

#### **错误处理步骤：**
1. **Business Validation**
   ```go
   CheckParamValid() → BizError{Code, Msg}
   ```

2. **Error Wrapping**
   ```go
   fillBizError(rsp.Resp, err)              // 填充错误响应
   ```

3. **Response Format**
   ```go
   CommonResponse{
       ErrCode: proto.Int32(code)
       ErrMsg:  proto.String(message)
   }
   ```

---

## 9. 关键调用特点

### **异步处理模式**
- 用户操作（SetScore/IncrScore）采用异步处理
- 通过Channel + Goroutine实现高并发

### **同步处理模式**
- 查询操作（TopN/UserRank）采用同步处理
- 管理员操作需要立即生效

### **缓存策略**
- 本地缓存：用户信息、模块配置
- Redis缓存：排行榜数据、分布式锁
- ES存储：历史数据、复杂查询

### **服务集成**
- Game Platform：黑名单、配置验证
- Friend Service：好友关系、用户信息
- SPEX：服务发现、配置管理

这些调用流程展示了rank_server系统的完整服务交互模式，体现了微服务架构的分层设计和异步处理能力。
