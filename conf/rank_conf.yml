# this file is generated by protoc-gen-mocrokit at Tue Apr 30 11:48:04 CST 2019
client:
  pool_size: 200          # Connection pool size
  pool_ttl: 30000         # Connection TTL (ms)
  request_timeout: 3000   # Transport Dial Timeout (ms)
  retries: 0              # Request/Response timeout
  dial_timeout: 1000

server:
  host: 127.0.0.1
  port: 8090
  register_ttl: 30000         # Register TTL in seconds
  project_name: Game

log:
  level: info
  path: ./log
  max_size: 1024       # megabytes
  max_backups:  0         
  max_age: 30          # days
  enable_caller: true
  dev:
    log_level: debug
  test:
    log_level: debug
  uat:
    log_level: debug
  live:
    log_level: info
breaker:
  switch: 1          # whether use the command breaker
  MaxRequests: 1     # MaxRequests is the maximum number of requests allowed to pass through
  Interval:  60000   # The cyclic period of the closed state
  Timeout: 10000     # The period of the open state


registry:
  address: '127.0.0.1:2181'
