include:
- project: shopee/games-public/goreporter-ci
  ref: master
  file: /go/root-template.yml

variables:
  GR_CMDB_NAME: shopee.marketplace_others.game.gameplatform.gameplatform.rank
  GR_TRIGGER_BRANCH: ^(test.*)
  GR_BASE_BRANCH: master

stages:
- goReporter
- echoTest
- release_verify

job-echoTest:
  stage: echoTest
  script:
  - echo "test $CI_PIPELINE_SOURCE"

release_verify:
  stage: release_verify
  before_script: []
  cache: {}
  dependencies: []
  script: |-
    result=`curl --request POST 'http://deploy-platform.shopee.io/apis/release/v1/gitlab/merge_request/verify' --header 'Content-Type: application/json' -d "{\"merge_request_id\": $CI_MERGE_REQUEST_IID,\"project_id\": $CI_PROJECT_ID}"`
    echo $result;
    echo $result | grep '"pass":true'
  rules:
  - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
      == "release"

job-goReporter:
  extends: .goReporter-template
  stage: goReporter
  rules:
  - if: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME != "release" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
      =~ "^(test.*)"

