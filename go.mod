module git.garena.com/shopee/game_platform/rank_server

go 1.22

replace (
	git.garena.com/shopee/feed/microkit => git.garena.com/shopee/feed/microkit v1.2.9-0.20240802094310-f89de91e71a8
	git.garena.com/shopee/platform/config-sdk-go => git.garena.com/shopee/platform/config-sdk-go v0.10.0
	git.garena.com/shopee/platform/service-governance/observability/metric => git.garena.com/shopee/platform/service-governance/observability/metric v1.0.20
	github.com/codahale/hdrhistogram => github.com/HdrHistogram/hdrhistogram-go v1.1.2
	github.com/lucas-clemente/quic-go => git.garena.com/shopee/game/thirdparty/quic-go v0.12.1
	github.com/micro/cli => git.garena.com/shopee/game/thirdparty/micro/cli v0.0.0-20250417025509-cd87ba4e1a4f
	github.com/micro/go-log => git.garena.com/shopee/game/thirdparty/micro/go-log v0.0.0-20250417025509-cd87ba4e1a4f
	github.com/micro/go-micro => github.com/micro/go-micro v1.10.0
	github.com/micro/go-rcache => git.garena.com/shopee/game/thirdparty/micro/go-rcache v0.0.0-20250417025509-cd87ba4e1a4f
	github.com/micro/mdns => git.garena.com/shopee/game/thirdparty/micro/mdns v0.0.0-20250417025509-cd87ba4e1a4f
	github.com/micro/util => git.garena.com/shopee/game/thirdparty/micro/util v0.0.0-20250417025509-cd87ba4e1a4f
	github.com/prometheus/client_golang => github.com/prometheus/client_golang v1.16.0
	github.com/prometheus/common => github.com/prometheus/common v0.26.0
	go.opentelemetry.io/otel => go.opentelemetry.io/otel v1.0.1
	go.opentelemetry.io/otel/exporters/prometheus => go.opentelemetry.io/otel/exporters/prometheus v0.24.0
	go.opentelemetry.io/otel/metric => go.opentelemetry.io/otel/metric v0.24.0
	go.opentelemetry.io/otel/sdk => go.opentelemetry.io/otel/sdk v1.0.1
	go.opentelemetry.io/otel/sdk/metric => go.opentelemetry.io/otel/sdk/metric v0.24.0
	go.opentelemetry.io/otel/trace => go.opentelemetry.io/otel/trace v1.0.1
	google.golang.org/grpc => google.golang.org/grpc v1.63.0

)

require (
	git.garena.com/shopee/feed/comm_lib v1.1.19
	git.garena.com/shopee/feed/microkit v1.2.9-0.20240802094310-f89de91e71a8
	git.garena.com/shopee/game/grpc4spex v1.0.0
	git.garena.com/shopee/game_platform/comm_lib v0.0.0-20250408013956-2499b6fd80e5
	git.garena.com/shopee/game_platform/proto v1.0.6-0.20250103124937-019255be9e6a
	git.garena.com/shopee/mts/go-application-server/gas v1.6.0
	git.garena.com/shopee/mts/go-application-server/spi/cache v1.5.0
	git.garena.com/shopee/mts/go-application-server/spi/config v1.3.2
	git.garena.com/shopee/mts/go-application-server/spi/db/hardy v1.6.0
	git.garena.com/shopee/mts/go-application-server/spi/grpc v1.0.0-rc.1
	git.garena.com/shopee/mts/go-application-server/testing/gastest v0.1.0
	github.com/gogo/protobuf v1.3.2
	github.com/golang/protobuf v1.5.4
	github.com/google/uuid v1.6.0
	github.com/jinzhu/gorm v1.9.16
	github.com/micro/go-micro v1.16.0
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826
	github.com/olivere/elastic v6.2.30+incompatible
	github.com/shopspring/decimal v1.2.0
	github.com/smartystreets/goconvey v1.7.2
	github.com/stretchr/testify v1.10.0
	go.uber.org/zap v1.24.0
	google.golang.org/grpc v1.67.1
	gorm.io/gorm v1.25.11
)

require (
	git.garena.com/common/gommon/crypt v0.0.0-20210211075301-867bb6bc3c33 // indirect
	git.garena.com/shopee/common/gdbc/datum v0.1.1 // indirect
	git.garena.com/shopee/common/gdbc/hardy v0.7.0 // indirect
	git.garena.com/shopee/common/gdbc/parser v0.7.0 // indirect
	git.garena.com/shopee/common/gdbc/sddl v0.5.0 // indirect
	git.garena.com/shopee/common/jsonext v0.1.0 // indirect
	git.garena.com/shopee/common/observability_config v0.2.1 // indirect
	git.garena.com/shopee/common/ulog v0.2.8 // indirect
	git.garena.com/shopee/devops/golang_aegislib v0.0.10 // indirect
	git.garena.com/shopee/feed/ginweb v1.1.9 // indirect
	git.garena.com/shopee/game/grpc4spex/protoc-gen-go-spex-grpc v0.0.0-20240613061742-e210b10c6528 // indirect
	git.garena.com/shopee/game_platform/microkit-gateway v0.0.0-20211012014905-581e7a4da0df // indirect
	git.garena.com/shopee/game_platform/spex_proto v0.0.0-20230321092917-8b1cc2416f46 // indirect
	git.garena.com/shopee/mts/go-application-server/spi/http v1.5.0 // indirect
	git.garena.com/shopee/mts/go-application-server/spi/kafka v1.7.0 // indirect
	git.garena.com/shopee/mts/go-application-server/spi/spex v1.6.0 // indirect
	git.garena.com/shopee/mts/sddl-api v0.1.0 // indirect
	git.garena.com/shopee/mts/servicecontext v0.2.0 // indirect
	git.garena.com/shopee/platform/config-sdk-go v0.10.0 // indirect
	git.garena.com/shopee/platform/golang_splib v1.1.4 // indirect
	git.garena.com/shopee/platform/ipds/ipds-sdk-go v0.3.3 // indirect
	git.garena.com/shopee/platform/service-gov/thin-sdk-go v1.0.5 // indirect
	git.garena.com/shopee/platform/service-governance/api/grpc_middleware v1.0.5 // indirect
	git.garena.com/shopee/platform/service-governance/observability/metric v1.0.16 // indirect
	git.garena.com/shopee/platform/service-governance/viewercontext v1.0.14-0.20240117033058-0404faedd9be // indirect
	git.garena.com/shopee/platform/splog v1.4.11-alpha.2 // indirect
	git.garena.com/shopee/platform/trace v0.1.0 // indirect
	git.garena.com/shopee/platform/tracing v1.11.10 // indirect
	git.garena.com/shopee/platform/tracing-contrib/dynamic-sampler v0.1.1 // indirect
	git.garena.com/shopee/sp_protocol v1.3.29 // indirect
	github.com/BurntSushi/toml v0.4.1 // indirect
	github.com/DATA-DOG/go-sqlmock v1.5.0 // indirect
	github.com/afex/hystrix-go v0.0.0-20180502004556-fa1af6a1f4f5 // indirect
	github.com/armon/go-metrics v0.0.0-20190430140413-ec5e00d3c878 // indirect
	github.com/benbjohnson/clock v1.1.0 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bitly/go-simplejson v0.5.0 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/cheekybits/genny v1.0.0 // indirect
	github.com/codegangsta/inject v0.0.0-20150114235600-33e0aa1cb7c0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dgraph-io/ristretto v0.1.0 // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/fortytw2/leaktest v1.3.0 // indirect
	github.com/fsnotify/fsnotify v1.4.9 // indirect
	github.com/ghodss/yaml v1.0.0 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/gin-gonic/gin v1.7.7 // indirect
	github.com/go-log/log v0.1.0 // indirect
	github.com/go-playground/locales v0.13.0 // indirect
	github.com/go-playground/universal-translator v0.17.0 // indirect
	github.com/go-playground/validator/v10 v10.4.1 // indirect
	github.com/go-sql-driver/mysql v1.7.0 // indirect
	github.com/golang/glog v1.2.2 // indirect
	github.com/gomodule/redigo v2.0.0+incompatible // indirect
	github.com/google/btree v1.0.0 // indirect
	github.com/gopherjs/gopherjs v0.0.0-20181017120253-0766667cb4d1 // indirect
	github.com/hashicorp/consul/api v1.3.0 // indirect
	github.com/hashicorp/errwrap v1.0.0 // indirect
	github.com/hashicorp/go-cleanhttp v0.5.1 // indirect
	github.com/hashicorp/go-immutable-radix v1.1.0 // indirect
	github.com/hashicorp/go-msgpack v0.5.5 // indirect
	github.com/hashicorp/go-multierror v1.0.0 // indirect
	github.com/hashicorp/go-rootcerts v1.0.1 // indirect
	github.com/hashicorp/go-sockaddr v1.0.2 // indirect
	github.com/hashicorp/golang-lru v0.6.0 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/hashicorp/memberlist v0.1.4 // indirect
	github.com/hashicorp/serf v0.8.3 // indirect
	github.com/imdario/mergo v0.3.8 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/jtolds/gls v4.20.0+incompatible // indirect
	github.com/juju/errors v0.0.0-20190930114154-d42613fe1ab9 // indirect
	github.com/konsorten/go-windows-terminal-sequences v1.0.3 // indirect
	github.com/leodido/go-urn v1.2.1 // indirect
	github.com/lucas-clemente/quic-go v0.12.1 // indirect
	github.com/magiconair/properties v1.8.1 // indirect
	github.com/mailru/easyjson v0.7.1 // indirect
	github.com/marten-seemann/qtls v0.3.2 // indirect
	github.com/mattn/go-isatty v0.0.19 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.4 // indirect
	github.com/micro/cli v0.2.0 // indirect
	github.com/micro/mdns v0.3.0 // indirect
	github.com/miekg/dns v1.1.35 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/mitchellh/hashstructure v1.0.0 // indirect
	github.com/mitchellh/mapstructure v1.1.2 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/nats-io/jwt v0.3.2 // indirect
	github.com/nats-io/nats.go v1.9.1 // indirect
	github.com/nats-io/nkeys v0.1.3 // indirect
	github.com/nats-io/nuid v1.0.1 // indirect
	github.com/panjf2000/ants/v2 v2.4.6 // indirect
	github.com/patrickmn/go-cache v2.1.0+incompatible // indirect
	github.com/pelletier/go-toml v1.4.0 // indirect
	github.com/pingcap/errors v0.11.5-0.20210425183316-da1aaba5fb63 // indirect
	github.com/pingcap/log v0.0.0-20210625125904-98ed8e2eb1c7 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/prometheus/client_golang v1.20.5 // indirect
	github.com/prometheus/client_model v0.6.1 // indirect
	github.com/prometheus/common v0.60.0 // indirect
	github.com/prometheus/procfs v0.15.1 // indirect
	github.com/samuel/go-zookeeper v0.0.0-20190923202752-2cc03de413da // indirect
	github.com/sean-/seed v0.0.0-20170313163322-e2103e2c3529 // indirect
	github.com/sirupsen/logrus v1.6.0 // indirect
	github.com/smartystreets/assertions v1.2.0 // indirect
	github.com/spf13/afero v1.10.0 // indirect
	github.com/spf13/cast v1.3.0 // indirect
	github.com/spf13/jwalterweatherman v1.1.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/spf13/viper v1.7.0 // indirect
	github.com/subosito/gotenv v1.2.0 // indirect
	github.com/ugorji/go/codec v1.1.7 // indirect
	github.com/valyala/fastrand v1.0.0 // indirect
	github.com/vmihailenco/msgpack v4.0.4+incompatible // indirect
	go.uber.org/atomic v1.9.0 // indirect
	go.uber.org/multierr v1.7.0 // indirect
	golang.org/x/crypto v0.31.0 // indirect
	golang.org/x/net v0.32.0 // indirect
	golang.org/x/sync v0.10.0 // indirect
	golang.org/x/sys v0.28.0 // indirect
	golang.org/x/text v0.21.0 // indirect
	golang.org/x/time v0.7.0 // indirect
	golang.org/x/xerrors v0.0.0-20231012003039-104605ab7028 // indirect
	google.golang.org/appengine v1.6.8 // indirect
	google.golang.org/genproto v0.0.0-20241015192408-796eee8c2d53 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20241202173237-19429a94021a // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20241202173237-19429a94021a // indirect
	google.golang.org/grpc/cmd/protoc-gen-go-grpc v1.1.0 // indirect
	google.golang.org/protobuf v1.35.2 // indirect
	gopkg.in/ini.v1 v1.51.1 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.0.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/driver/mysql v1.5.7 // indirect
)
