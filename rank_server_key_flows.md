# Rank Server 关键业务链路分析

## 🎯 1. 设置/增加分数流程

### 链路：User Gateway → Rank Handler → Redis → Elasticsearch

**详细流程：**
```
Game User → User Gateway → RankHandler V2.0 (SetScoreV2/IncrScoreV2) 
→ 参数验证 → 异步Channel → Worker Goroutine → Redis ZAdd → ES异步写入
```

**关键组件：**
- **入口**: `handler/rank_handler_2.0.go` - `SetScoreV2()` / `IncrScoreV2()`
- **异步处理**: `RankWriter.putRankChan()` - 将请求放入channel
- **Worker**: `doSetRankScore()` - 异步处理分数更新
- **Redis操作**: `cache.RedisUtilZAdd()` - 更新分桶中的分数
- **双写机制**: `DoubleWriteTopNKey()` - 同时写入TopN优化Key
- **ES写入**: `RankingsEsWriter.Write()` - 异步写入Elasticsearch

**性能优化：**
- 异步处理，不阻塞用户请求
- 分桶存储，提高并发性能
- 双写TopN Key，优化查询性能

---

## 🔍 2. 查询用户排名流程

### 链路：User Gateway → Rank Handler → Redis

**详细流程：**
```
Game User → User Gateway → RankHandler V2.0 (UserRankV2) 
→ 判断使用TopN Key还是分桶 → Redis查询 → 返回排名
```

**关键组件：**
- **入口**: `handler/rank_handler_2.0.go` - `UserRankV2()`
- **策略选择**: `IsUseTop10kKey()` - 判断使用哪种查询方式
- **TopN查询**: `QueryUserRankV2()` - 从TopN Key精确查询
- **分桶查询**: `RedisUtilZRank()` - 从分桶粗略查询
- **排名计算**: 前10000名精确排名，其他粗略排名

**性能策略：**
- 前10000名：精确排名，使用TopN Key
- 10000名后：粗略排名，使用分桶估算

---

## 📊 3. 查询排行榜流程

### 链路：User Gateway → Game Service → Rank Handler → Redis

**详细流程：**
```
Game User → User Gateway → RankHandler V2.0 (RankListV2) 
→ 黑名单检查 → Game Service → 选择查询方式 → Redis查询 → 数据格式化
```

**关键组件：**
- **入口**: `handler/rank_handler_2.0.go` - `RankListV2()`
- **黑名单检查**: `CheckBlacklistOpen()` - 调用Game Service检查
- **外部服务**: `gameplatform_game` - 游戏服务gRPC调用
- **TopN查询**: `QueryTopNFromSingleKey()` - 高性能TopN查询
- **分桶查询**: `allbucketZSetRange()` - 多桶聚合查询
- **数据格式化**: `transZElem2RankItem()` - 转换为前端格式

**特殊处理：**
- 黑名单过滤：根据游戏配置过滤作弊用户
- 留存排行榜：按时间重新排序 `ReSortByUserTime()`

---

## 👥 4. 好友排行榜流程

### 链路：User Gateway → Friend Service → Rank Handler → Elasticsearch

**详细流程：**
```
Game User → User Gateway → RankHandler (FriendsRankV3) 
→ Friend Service → 获取好友列表 → ES批量查询 → 合并排序
```

**关键组件：**
- **入口**: `handler/rank_handler_friends.go` - `FriendsRankV3()`
- **好友服务**: `GetFriends()` - 调用好友服务获取列表
- **外部服务**: `gameplatform_friend` - 好友服务gRPC调用
- **批量查询**: `QueryFriendsRank()` - 批量查询好友分数
- **ES查询**: `UserScoreListV3()` → `esQueryRankListV2ByUsers()`
- **数据合并**: 合并好友信息（分数+昵称）和排序

**优化点：**
- 批量查询：一次性查询所有好友分数
- ES查询：利用ES的批量查询能力
- 数据合并：在内存中完成排序和格式化

---

## ⚙️ 5. 管理员批量操作流程

### 链路：Admin Gateway → Rank Handler → Elasticsearch → Redis

**详细流程：**
```
Games Admin → Admin Gateway → RankHandler V3.0 (IncrScoreV3Admin) 
→ ES查询用户 → ES更新分数 → 可选Redis同步 → 双写TopN Key
```

**关键组件：**
- **入口**: `handler/rank_handler_3.0.go` - `IncrScoreV3Admin()`
- **ES查询**: `esutil.EsClient.Get()` - 查询用户当前信息
- **ES更新**: `esutil.EsClient.Update()` - 直接更新ES中的分数
- **Redis同步**: `cache.RedisUtilZAdd()` - 可选的Redis缓存同步
- **双写机制**: `DoubleWriteTopNKey()` - 保持TopN Key一致性

**管理员特权：**
- 直接操作ES：绕过异步队列，立即生效
- 可选Redis同步：根据需要决定是否同步缓存
- 批量操作：支持批量修改用户分数

---

## 🔧 技术特点总结

### 1. 异步处理架构
- **Channel + Goroutine**: 分数更新异步处理
- **批量写入**: ES批量写入提高性能
- **失败重试**: 自动重试机制保证数据一致性

### 2. 多层存储策略
- **Redis分桶**: 提高并发写入性能
- **TopN Key**: 优化前10000名查询性能
- **Elasticsearch**: 支持复杂查询和分析

### 3. 性能优化
- **分桶策略**: 减少Redis热点Key问题
- **双写机制**: 保证数据一致性
- **缓存过期**: 自动清理过期数据

### 4. 服务集成
- **微服务调用**: 集成游戏服务和好友服务
- **黑名单过滤**: 实时反作弊能力
- **监控告警**: 完整的监控和错误处理

这个架构设计能够支撑大规模游戏的排行榜需求，具有高性能、高可用、易扩展的特点。
