# http://marmelab.com/blog/2016/02/29/auto-documented-makefile.html
help: ## Show list of make targets and their description
	@grep -E '^[/%.a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) \
		| awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-15s\033[0m %s\n", $$1, $$2}'

.DEFAULT_GOAL:= help

.PHONY: gen
gen:
	spkit gen go

.PHONY: lint
lint:
	spkit lint

.PHONY: test
test:
	spkit test

.PHONY: build
build:
	spkit build .

.PHONY: clean
clean:
	spkit clean .

all: clean gen build
