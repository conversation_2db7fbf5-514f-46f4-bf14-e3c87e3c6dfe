package semaphore

import (
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"sync"
	"time"
)

// Es Semaphore
const DefaultSemaphore = 32

var commandMap = sync.Map{}

type Semaphore struct {
	innerChan chan struct{}
}

func (s *Semaphore) Acquire() {
	for {
		select {
		case s.innerChan <- struct{}{}:
			return
		default:
			gamelog.Info("semaphore acquire is blocking", gamelog.Fields{})
			time.Sleep(100 * time.Millisecond)
		}
	}
}

func (s *Semaphore) Release() {
	select {
	case <-s.innerChan:
		return
	default:
		return
	}
}

func NewSemaphore(num uint64) *Semaphore {
	return &Semaphore{
		innerChan: make(chan struct{}, num),
	}
}

type asyncFunc func() (interface{}, error)

type CommandConfig struct {
	MaxConcurrentRequests uint64
}

// warn: may lead so many goroutine
func AsyncGo(command string, f asyncFunc) {
	v, _ := commandMap.LoadOrStore(command, NewSemaphore(DefaultSemaphore))
	sem := v.(Semaphore)

	go func() {
		sem.Acquire()
		_, _ = f()
		sem.Release()
	}()
}

func SyncGo(command string, f asyncFunc) {
	v, _ := commandMap.LoadOrStore(command, NewSemaphore(DefaultSemaphore))
	sem := v.(Semaphore)

	sem.Acquire()
	go func() {
		_, _ = f()
		sem.Release()
	}()
}

func ConfigCommand(command string, config CommandConfig) {
	sem := NewSemaphore(config.MaxConcurrentRequests)
	commandMap.Store(command, sem)
}
