module git.garena.com/shopee/game_platform/rank_server

go 1.22

require (
	git.garena.com/shopee/feed/comm_lib v1.1.19
	git.garena.com/shopee/feed/microkit v1.2.9-0.20240802094310-f89de91e71a8
	git.garena.com/shopee/game/grpc4spex v1.0.0
	git.garena.com/shopee/game_platform/comm_lib v0.0.0-20250124101405-bd6f834cf6e4
	git.garena.com/shopee/game_platform/proto v1.0.6-0.20250103124937-019255be9e6a
	git.garena.com/shopee/mts/go-application-server/gas v1.6.0
	git.garena.com/shopee/mts/go-application-server/spi/cache v1.6.0-rc.2
	git.garena.com/shopee/mts/go-application-server/spi/config v1.3.2
	git.garena.com/shopee/mts/go-application-server/spi/db/hardy v1.6.0
	git.garena.com/shopee/mts/go-application-server/spi/grpc v1.0.0-rc.1
	git.garena.com/shopee/mts/go-application-server/testing/gastest v0.1.0
	github.com/gogo/protobuf v1.3.2
	github.com/golang/protobuf v1.5.4
	github.com/google/uuid v1.6.0
	github.com/jinzhu/gorm v1.9.16
	github.com/micro/go-micro v1.16.0
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826
	github.com/olivere/elastic v6.2.30+incompatible
	github.com/shopspring/decimal v1.2.0
	github.com/smartystreets/goconvey v1.7.2
	github.com/stretchr/testify v1.10.0
	go.uber.org/zap v1.24.0
	google.golang.org/grpc v1.67.1
	gorm.io/gorm v1.25.11
)

require (
	git.garena.com/shopee/common/cache v0.26.0-rc.2 // indirect
	git.garena.com/shopee/common/circuitbreaker v0.8.0-rc.1 // indirect
	git.garena.com/shopee/common/deepcopy v1.0.0 // indirect
	git.garena.com/shopee/common/gdbc/datum v0.1.1 // indirect
	git.garena.com/shopee/common/gdbc/gdbc v0.5.1 // indirect
	git.garena.com/shopee/common/gdbc/hardy v0.8.1 // indirect
	git.garena.com/shopee/common/gdbc/parser v0.8.0 // indirect
	git.garena.com/shopee/common/gdbc/sddl v0.6.0 // indirect
	git.garena.com/shopee/common/gomemcache v0.7.0 // indirect
	git.garena.com/shopee/common/http v0.10.0-rc.1 // indirect
	git.garena.com/shopee/common/jsonext v0.2.0 // indirect
	git.garena.com/shopee/common/log v0.6.0 // indirect
	git.garena.com/shopee/common/mq-contrib/muse v0.4.4 // indirect
	git.garena.com/shopee/common/mq-contrib/muse-protocol v0.1.2 // indirect
	git.garena.com/shopee/common/observability_config v0.2.1 // indirect
	git.garena.com/shopee/common/redigo v1.10.1-fifo // indirect
	git.garena.com/shopee/common/sarama v1.42.1-1.2 // indirect
	git.garena.com/shopee/common/spex-contrib/interceptor/gateway_protocol v0.2.0 // indirect
	git.garena.com/shopee/common/spex-contrib/interceptor/logging v0.13.0-rc.1 // indirect
	git.garena.com/shopee/common/spex-contrib/interceptor/monitoring v0.12.0 // indirect
	git.garena.com/shopee/common/spex-contrib/interceptor/tracing v0.5.0 // indirect
	git.garena.com/shopee/common/ulog v0.2.8 // indirect
	git.garena.com/shopee/common/uniconfig v0.17.0-rc.1 // indirect
	git.garena.com/shopee/core-server/enhanced-kafka-lib v0.13.0-rc.7 // indirect
	git.garena.com/shopee/core-server/hashring v0.0.1 // indirect
	git.garena.com/shopee/core-server/hashring-server-selector v0.2.0 // indirect
	git.garena.com/shopee/devops/golang_aegislib v0.0.13 // indirect
	git.garena.com/shopee/feed/ginweb v1.1.9 // indirect
	git.garena.com/shopee/game/grpc4spex/protoc-gen-go-spex-grpc v0.0.0-20240613061742-e210b10c6528 // indirect
	git.garena.com/shopee/game_platform/microkit-gateway v0.0.0-20211012014905-581e7a4da0df // indirect
	git.garena.com/shopee/game_platform/spex_proto v0.0.0-20230321092917-8b1cc2416f46 // indirect
	git.garena.com/shopee/mts/go-application-server/control-center/sdk v1.3.1 // indirect
	git.garena.com/shopee/mts/go-application-server/datacollector v1.0.0 // indirect
	git.garena.com/shopee/mts/go-application-server/engine v1.6.0-rc.10 // indirect
	git.garena.com/shopee/mts/go-application-server/engine/admin v1.0.0 // indirect
	git.garena.com/shopee/mts/go-application-server/ioc v0.6.1 // indirect
	git.garena.com/shopee/mts/go-application-server/sp/cache v1.7.0-rc.2 // indirect
	git.garena.com/shopee/mts/go-application-server/sp/config v1.9.0-rc.1 // indirect
	git.garena.com/shopee/mts/go-application-server/sp/db/gdbc v1.6.0 // indirect
	git.garena.com/shopee/mts/go-application-server/sp/db/hardy v1.7.1 // indirect
	git.garena.com/shopee/mts/go-application-server/sp/grpc v1.0.0-rc.1 // indirect
	git.garena.com/shopee/mts/go-application-server/sp/http v1.8.0-rc.3 // indirect
	git.garena.com/shopee/mts/go-application-server/sp/job v1.1.0 // indirect
	git.garena.com/shopee/mts/go-application-server/sp/kafka v1.8.0-rc.8 // indirect
	git.garena.com/shopee/mts/go-application-server/sp/spex v1.8.0-rc.2 // indirect
	git.garena.com/shopee/mts/go-application-server/sp/spex/spexcontext v1.0.0 // indirect
	git.garena.com/shopee/mts/go-application-server/spi/db/gdbc v1.5.0 // indirect
	git.garena.com/shopee/mts/go-application-server/spi/http v1.5.0 // indirect
	git.garena.com/shopee/mts/go-application-server/spi/job v1.0.0 // indirect
	git.garena.com/shopee/mts/go-application-server/spi/kafka v1.8.0-rc.2 // indirect
	git.garena.com/shopee/mts/go-application-server/spi/spex v1.7.0-rc.1 // indirect
	git.garena.com/shopee/mts/go-application-server/testing/wiretest v0.8.0 // indirect
	git.garena.com/shopee/mts/sddl-api v0.1.0 // indirect
	git.garena.com/shopee/mts/servicecontext v0.3.0 // indirect
	git.garena.com/shopee/mts/util/safefunc v1.3.0 // indirect
	git.garena.com/shopee/pl/efficacy/shark_agentsdk v0.1.0-rc.4 // indirect
	git.garena.com/shopee/pl/efficacy/sharkdiff/sp v0.1.4-rc.3 // indirect
	git.garena.com/shopee/pl/efficacy/sharkdiff/spi v0.1.3-rc.1 // indirect
	git.garena.com/shopee/pl/efficacy/sharklog v0.0.5 // indirect
	git.garena.com/shopee/pl/efficacy/sharkreplay/sp v0.1.4-rc.7 // indirect
	git.garena.com/shopee/pl/efficacy/sharkreplay/spi v0.1.2 // indirect
	git.garena.com/shopee/pl/shopeepay-common/basepkg v0.0.40 // indirect
	git.garena.com/shopee/pl/shopeepay-common/log v1.4.0 // indirect
	git.garena.com/shopee/pl/shopeepay-common/shark v0.2.3 // indirect
	git.garena.com/shopee/pl/shopeepay-common/shark-plugins/eklV2 v0.2.4-rc.6 // indirect
	git.garena.com/shopee/pl/shopeepay-common/shark-plugins/general v0.2.2 // indirect
	git.garena.com/shopee/pl/shopeepay-common/shark-plugins/general-server v0.2.4-rc.1 // indirect
	git.garena.com/shopee/pl/shopeepay-common/shark-plugins/http v0.2.3 // indirect
	git.garena.com/shopee/pl/shopeepay-common/shark-plugins/httpserver v0.2.4-rc.2 // indirect
	git.garena.com/shopee/pl/shopeepay-common/shark-plugins/sconfigs v0.2.3 // indirect
	git.garena.com/shopee/pl/shopeepay-common/shark-plugins/spex-platform v0.2.3-beta.6 // indirect
	git.garena.com/shopee/pl/shopeepay-common/shark-plugins/sql v0.2.3-rc.1 // indirect
	git.garena.com/shopee/pl/shopeepay-common/shark-plugins/ucache v0.2.2 // indirect
	git.garena.com/shopee/pl/shopeepay-common/shark-plugins/uniconfig v0.2.4-rc.2 // indirect
	git.garena.com/shopee/platform/config-sdk-go v0.11.0 // indirect
	git.garena.com/shopee/platform/golang_splib v1.2.4 // indirect
	git.garena.com/shopee/platform/ipds/ipds-sdk-go v0.3.4 // indirect
	git.garena.com/shopee/platform/jaeger-tracer v1.9.2 // indirect
	git.garena.com/shopee/platform/local-tracer v1.2.0 // indirect
	git.garena.com/shopee/platform/service-gov/thin-sdk-go v1.0.5 // indirect
	git.garena.com/shopee/platform/service-governance/api/grpc_middleware v1.0.5 // indirect
	git.garena.com/shopee/platform/service-governance/observability/metric v1.0.19 // indirect
	git.garena.com/shopee/platform/service-governance/viewercontext v1.0.14-0.20240117033058-0404faedd9be // indirect
	git.garena.com/shopee/platform/splog v1.7.0 // indirect
	git.garena.com/shopee/platform/trace v0.1.0 // indirect
	git.garena.com/shopee/platform/tracing v1.12.0 // indirect
	git.garena.com/shopee/platform/tracing-contrib/dynamic-sampler v0.1.1 // indirect
	git.garena.com/shopee/seller-epd/goc_helper v1.0.1 // indirect
	git.garena.com/shopee/seller-server/mktzlib/http_over_spex v1.1.5 // indirect
	git.garena.com/shopee/shopee-tracing/grpc-zap v0.0.0-20210527025615-5e750cb69840 // indirect
	git.garena.com/shopee/shopee-tracing/zap-extension v0.0.0-20210629081750-fb6eee7c390c // indirect
	git.garena.com/shopee/sp_protocol v1.3.29 // indirect
	github.com/BurntSushi/toml v0.4.1 // indirect
	github.com/DATA-DOG/go-sqlmock v1.5.0 // indirect
	github.com/Shopify/sarama v1.29.1 // indirect
	github.com/afex/hystrix-go v0.0.0-20180502004556-fa1af6a1f4f5 // indirect
	github.com/andybalholm/brotli v1.0.5 // indirect
	github.com/armon/go-metrics v0.0.0-20190430140413-ec5e00d3c878 // indirect
	github.com/aws/aws-sdk-go-v2 v1.22.2 // indirect
	github.com/aws/aws-sdk-go-v2/aws/protocol/eventstream v1.5.0 // indirect
	github.com/aws/aws-sdk-go-v2/credentials v1.16.0 // indirect
	github.com/aws/aws-sdk-go-v2/internal/configsources v1.2.2 // indirect
	github.com/aws/aws-sdk-go-v2/internal/endpoints/v2 v2.5.2 // indirect
	github.com/aws/aws-sdk-go-v2/internal/v4a v1.2.2 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/accept-encoding v1.10.0 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/checksum v1.2.2 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/presigned-url v1.10.2 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/s3shared v1.16.2 // indirect
	github.com/aws/aws-sdk-go-v2/service/s3 v1.42.1 // indirect
	github.com/aws/smithy-go v1.16.0 // indirect
	github.com/benbjohnson/clock v1.3.0 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bitly/go-simplejson v0.5.0 // indirect
	github.com/bradfitz/gomemcache v0.0.0-20190913173617-a41fca850d0b // indirect
	github.com/brentp/intintmap v0.0.0-20190211203843-30dc0ade9af9 // indirect
	github.com/cespare/xxhash v1.1.0 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/cheekybits/genny v1.0.0 // indirect
	github.com/codahale/hdrhistogram v0.0.0-00010101000000-000000000000 // indirect
	github.com/codegangsta/inject v0.0.0-20150114235600-33e0aa1cb7c0 // indirect
	github.com/cznic/mathutil v0.0.0-20181122101859-297441e03548 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dgraph-io/ristretto v0.1.0 // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/eapache/go-resiliency v1.4.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-20230731223053-c322873962e3 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/edsrzf/mmap-go v1.0.0 // indirect
	github.com/fsnotify/fsnotify v1.4.9 // indirect
	github.com/getsentry/sentry-go v0.10.0 // indirect
	github.com/ghodss/yaml v1.0.0 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/gin-gonic/gin v1.7.7 // indirect
	github.com/go-chi/chi/v5 v5.0.11 // indirect
	github.com/go-log/log v0.1.0 // indirect
	github.com/go-ole/go-ole v1.2.6 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.4.1 // indirect
	github.com/go-sql-driver/mysql v1.7.0 // indirect
	github.com/gobwas/glob v0.2.3 // indirect
	github.com/goccy/go-json v0.10.2 // indirect
	github.com/golang/glog v1.2.2 // indirect
	github.com/golang/mock v1.6.0 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/gomodule/redigo v2.0.0+incompatible // indirect
	github.com/google/btree v1.0.0 // indirect
	github.com/google/go-querystring v1.1.0 // indirect
	github.com/gookit/color v1.5.2 // indirect
	github.com/gopherjs/gopherjs v0.0.0-20181017120253-0766667cb4d1 // indirect
	github.com/grpc-ecosystem/go-grpc-middleware v1.4.0 // indirect
	github.com/hashicorp/consul/api v1.3.0 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-cleanhttp v0.5.1 // indirect
	github.com/hashicorp/go-immutable-radix v1.1.0 // indirect
	github.com/hashicorp/go-msgpack v0.5.5 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/go-rootcerts v1.0.1 // indirect
	github.com/hashicorp/go-sockaddr v1.0.2 // indirect
	github.com/hashicorp/go-uuid v1.0.3 // indirect
	github.com/hashicorp/golang-lru v0.6.0 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/hashicorp/memberlist v0.1.4 // indirect
	github.com/hashicorp/serf v0.8.3 // indirect
	github.com/imdario/mergo v0.3.8 // indirect
	github.com/jcmturner/aescts/v2 v2.0.0 // indirect
	github.com/jcmturner/dnsutils/v2 v2.0.0 // indirect
	github.com/jcmturner/gofork v1.7.6 // indirect
	github.com/jcmturner/gokrb5/v8 v8.4.4 // indirect
	github.com/jcmturner/rpc/v2 v2.0.3 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/jtolds/gls v4.20.0+incompatible // indirect
	github.com/juju/errors v0.0.0-20190930114154-d42613fe1ab9 // indirect
	github.com/klauspost/compress v1.17.11 // indirect
	github.com/konsorten/go-windows-terminal-sequences v1.0.3 // indirect
	github.com/leodido/go-urn v1.2.4 // indirect
	github.com/lucas-clemente/quic-go v0.12.1 // indirect
	github.com/magiconair/properties v1.8.5 // indirect
	github.com/mailru/easyjson v0.7.1 // indirect
	github.com/marten-seemann/qtls v0.3.2 // indirect
	github.com/mattn/go-isatty v0.0.19 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.4 // indirect
	github.com/micro/cli v0.2.0 // indirect
	github.com/micro/mdns v0.3.0 // indirect
	github.com/miekg/dns v1.1.35 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/mitchellh/hashstructure v1.0.0 // indirect
	github.com/mitchellh/mapstructure v1.1.2 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/nats-io/jwt v0.3.2 // indirect
	github.com/nats-io/nats.go v1.9.1 // indirect
	github.com/nats-io/nkeys v0.1.3 // indirect
	github.com/nats-io/nuid v1.0.1 // indirect
	github.com/opentracing/opentracing-go v1.1.0 // indirect
	github.com/panjf2000/ants/v2 v2.5.0 // indirect
	github.com/patrickmn/go-cache v2.1.0+incompatible // indirect
	github.com/pelletier/go-toml v1.4.0 // indirect
	github.com/pierrec/lz4 v2.6.0+incompatible // indirect
	github.com/pierrec/lz4/v4 v4.1.18 // indirect
	github.com/pingcap/errors v0.11.5-0.20210425183316-da1aaba5fb63 // indirect
	github.com/pingcap/log v0.0.0-20210625125904-98ed8e2eb1c7 // indirect
	github.com/pingcap/tidb/parser v0.0.0-20211124132551-4a1b2e9fe5b5 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/prometheus/client_golang v1.20.5 // indirect
	github.com/prometheus/client_model v0.6.1 // indirect
	github.com/prometheus/common v0.60.0 // indirect
	github.com/prometheus/procfs v0.15.1 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/remyoudompheng/bigfft v0.0.0-20230129092748-24d4a6f8daec // indirect
	github.com/rs/xid v1.5.0 // indirect
	github.com/samuel/go-zookeeper v0.0.0-20190923202752-2cc03de413da // indirect
	github.com/sean-/seed v0.0.0-20170313163322-e2103e2c3529 // indirect
	github.com/shirou/gopsutil v3.21.11+incompatible // indirect
	github.com/sirupsen/logrus v1.6.0 // indirect
	github.com/smartystreets/assertions v1.2.0 // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/spf13/afero v1.10.0 // indirect
	github.com/spf13/cast v1.5.1 // indirect
	github.com/spf13/jwalterweatherman v1.1.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/spf13/viper v1.7.0 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/subosito/gotenv v1.2.0 // indirect
	github.com/tidwall/gjson v1.17.1 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.0 // indirect
	github.com/timandy/routine v1.1.1 // indirect
	github.com/tklauser/go-sysconf v0.3.12 // indirect
	github.com/tklauser/numcpus v0.6.1 // indirect
	github.com/twmb/murmur3 v1.1.6 // indirect
	github.com/uber/jaeger-client-go v2.22.1+incompatible // indirect
	github.com/uber/jaeger-lib v2.2.0+incompatible // indirect
	github.com/ugorji/go/codec v1.2.11 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasthttp v1.26.0 // indirect
	github.com/valyala/fastrand v1.1.0 // indirect
	github.com/vmihailenco/msgpack v4.0.4+incompatible // indirect
	github.com/xdg/scram v1.0.3 // indirect
	github.com/xdg/stringprep v1.0.3 // indirect
	github.com/xo/terminfo v0.0.0-20210125001918-ca9a967f8778 // indirect
	github.com/yusufpapurcu/wmi v1.2.3 // indirect
	github.com/zhenjl/cityhash v0.0.0-20131128155616-cdd6a94144ab // indirect
	go.opentelemetry.io/otel v1.22.0 // indirect
	go.opentelemetry.io/otel/exporters/prometheus v0.24.0 // indirect
	go.opentelemetry.io/otel/internal/metric v0.24.0 // indirect
	go.opentelemetry.io/otel/metric v1.22.0 // indirect
	go.opentelemetry.io/otel/sdk v1.21.0 // indirect
	go.opentelemetry.io/otel/sdk/export/metric v0.24.0 // indirect
	go.opentelemetry.io/otel/sdk/metric v0.24.0 // indirect
	go.opentelemetry.io/otel/trace v1.22.0 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go.uber.org/multierr v1.9.0 // indirect
	golang.org/x/crypto v0.31.0 // indirect
	golang.org/x/net v0.32.0 // indirect
	golang.org/x/sync v0.10.0 // indirect
	golang.org/x/sys v0.28.0 // indirect
	golang.org/x/text v0.21.0 // indirect
	golang.org/x/time v0.7.0 // indirect
	golang.org/x/xerrors v0.0.0-20231012003039-104605ab7028 // indirect
	google.golang.org/appengine v1.6.8 // indirect
	google.golang.org/genproto v0.0.0-20241015192408-796eee8c2d53 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20241202173237-19429a94021a // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20241202173237-19429a94021a // indirect
	google.golang.org/grpc/cmd/protoc-gen-go-grpc v1.1.0 // indirect
	google.golang.org/protobuf v1.35.2 // indirect
	gopkg.in/fsnotify.v1 v1.4.7 // indirect
	gopkg.in/ini.v1 v1.51.1 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.0.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/driver/mysql v1.5.7 // indirect
)

replace git.garena.com/shopee/common/log => git.garena.com/shopee/common/log v0.6.0

replace git.garena.com/shopee/common/spex-contrib/interceptor/monitoring => git.garena.com/shopee/common/spex-contrib/interceptor/monitoring v0.12.0

replace git.garena.com/shopee/pl/shopeepay-common/shark-plugins/general => git.garena.com/shopee/pl/shopeepay-common/shark-plugins/general v0.2.2

replace git.garena.com/shopee/pl/shopeepay-common/shark-plugins/http => git.garena.com/shopee/pl/shopeepay-common/shark-plugins/http v0.2.3

replace git.garena.com/shopee/common/cache => git.garena.com/shopee/common/cache v0.26.0-rc.2

replace git.garena.com/shopee/pl/shopeepay-common/shark-plugins/sconfigs => git.garena.com/shopee/pl/shopeepay-common/shark-plugins/sconfigs v0.2.3

replace git.garena.com/shopee/mts/go-application-server/sp/kafka => git.garena.com/shopee/mts/go-application-server/sp/kafka v1.8.0-rc.8

replace git.garena.com/shopee/pl/shopeepay-common/shark-plugins/eklV2 => git.garena.com/shopee/pl/shopeepay-common/shark-plugins/eklV2 v0.2.4-rc.6

replace git.garena.com/shopee/pl/shopeepay-common/shark => git.garena.com/shopee/pl/shopeepay-common/shark v0.2.3

replace git.garena.com/shopee/common/gdbc/datum => git.garena.com/shopee/common/gdbc/datum v0.1.1

replace git.garena.com/shopee/platform/config-sdk-go => git.garena.com/shopee/platform/config-sdk-go v0.11.0

replace git.garena.com/shopee/common/gdbc/hardy => git.garena.com/shopee/common/gdbc/hardy v0.8.1

replace git.garena.com/shopee/platform/service-governance/viewercontext => git.garena.com/shopee/platform/service-governance/viewercontext v1.0.13

replace git.garena.com/shopee/common/gdbc/parser => git.garena.com/shopee/common/gdbc/parser v0.8.0

replace git.garena.com/shopee/platform/splog => git.garena.com/shopee/platform/splog v1.7.0

replace git.garena.com/shopee/mts/go-application-server/sp/spex/spexcontext => git.garena.com/shopee/mts/go-application-server/sp/spex/spexcontext v1.0.0

replace git.garena.com/shopee/mts/go-application-server/sp/spex => git.garena.com/shopee/mts/go-application-server/sp/spex v1.8.0-rc.2

replace git.garena.com/shopee/common/gdbc/gdbc => git.garena.com/shopee/common/gdbc/gdbc v0.5.1

replace git.garena.com/shopee/pl/shopeepay-common/shark-plugins/uniconfig => git.garena.com/shopee/pl/shopeepay-common/shark-plugins/uniconfig v0.2.4-rc.2

replace git.garena.com/shopee/mts/go-application-server/sp/cache => git.garena.com/shopee/mts/go-application-server/sp/cache v1.7.0-rc.2

replace git.garena.com/shopee/pl/shopeepay-common/shark-plugins/sql => git.garena.com/shopee/pl/shopeepay-common/shark-plugins/sql v0.2.3-rc.1

replace git.garena.com/shopee/devops/golang_aegislib => git.garena.com/shopee/devops/golang_aegislib v0.0.13

replace git.garena.com/shopee/common/gdbc/sddl => git.garena.com/shopee/common/gdbc/sddl v0.6.0

replace git.garena.com/shopee/mts/go-application-server/sp/job => git.garena.com/shopee/mts/go-application-server/sp/job v1.1.0

replace git.garena.com/shopee/platform/service-gov/thin-sdk-go => git.garena.com/shopee/platform/service-gov/thin-sdk-go v1.0.5

replace git.garena.com/shopee/platform/golang_splib => git.garena.com/shopee/platform/golang_splib v1.2.4

replace git.garena.com/shopee/common/spex-contrib/interceptor/gateway_protocol => git.garena.com/shopee/common/spex-contrib/interceptor/gateway_protocol v0.2.0

replace git.garena.com/shopee/mts/go-application-server/sp/config => git.garena.com/shopee/mts/go-application-server/sp/config v1.9.0-rc.1

replace git.garena.com/shopee/pl/shopeepay-common/shark-plugins/spex-platform => git.garena.com/shopee/pl/shopeepay-common/shark-plugins/spex-platform v0.2.3-beta.6

replace git.garena.com/shopee/core-server/enhanced-kafka-lib => git.garena.com/shopee/core-server/enhanced-kafka-lib v0.13.0-rc.7

replace git.garena.com/shopee/pl/efficacy/sharklog => git.garena.com/shopee/pl/efficacy/sharklog v0.0.5

replace git.garena.com/shopee/common/spex-contrib/interceptor/logging => git.garena.com/shopee/common/spex-contrib/interceptor/logging v0.13.0-rc.1

replace git.garena.com/shopee/pl/shopeepay-common/shark-plugins/ucache => git.garena.com/shopee/pl/shopeepay-common/shark-plugins/ucache v0.2.2

replace git.garena.com/shopee/common/spex-contrib/interceptor/tracing => git.garena.com/shopee/common/spex-contrib/interceptor/tracing v0.5.0

replace git.garena.com/shopee/mts/go-application-server/sp/db/hardy => git.garena.com/shopee/mts/go-application-server/sp/db/hardy v1.7.1

replace git.garena.com/shopee/pl/shopeepay-common/shark-plugins/httpserver => git.garena.com/shopee/pl/shopeepay-common/shark-plugins/httpserver v0.2.4-rc.2

replace git.garena.com/shopee/common/http => git.garena.com/shopee/common/http v0.10.0-rc.1

replace git.garena.com/shopee/mts/go-application-server/sp/grpc => git.garena.com/shopee/mts/go-application-server/sp/grpc v1.0.0-rc.1

replace git.garena.com/shopee/mts/go-application-server/sp/http => git.garena.com/shopee/mts/go-application-server/sp/http v1.8.0-rc.3

replace git.garena.com/shopee/common/uniconfig => git.garena.com/shopee/common/uniconfig v0.17.0-rc.1

replace git.garena.com/shopee/mts/go-application-server/sp/db/gdbc => git.garena.com/shopee/mts/go-application-server/sp/db/gdbc v1.6.0

replace git.garena.com/shopee/pl/shopeepay-common/shark-plugins/general-server => git.garena.com/shopee/pl/shopeepay-common/shark-plugins/general-server v0.2.4-rc.1

replace go.opentelemetry.io/otel/trace => go.opentelemetry.io/otel/trace v1.0.1

replace go.opentelemetry.io/otel => go.opentelemetry.io/otel v1.0.1

replace go.opentelemetry.io/otel/metric => go.opentelemetry.io/otel/metric v0.24.0

replace git.garena.com/shopee/feed/microkit => git.garena.com/shopee/feed/microkit v1.2.9-0.20240802094310-f89de91e71a8

replace github.com/prometheus/client_golang => github.com/prometheus/client_golang v1.16.0

replace git.garena.com/shopee/mts/go-application-server/testing/gastest => /var/folders/47/ckxvldyn25v9x_l93p_z66yw0000gq/T/gas_it_03d32f2c031f29d4a80038293f0b8643

replace google.golang.org/grpc => google.golang.org/grpc v1.63.0

replace github.com/lucas-clemente/quic-go => git.garena.com/shopee/game/thirdparty/quic-go v0.12.1

replace github.com/prometheus/common => github.com/prometheus/common v0.26.0

replace go.opentelemetry.io/otel/sdk/metric => go.opentelemetry.io/otel/sdk/metric v0.24.0

replace go.opentelemetry.io/otel/sdk => go.opentelemetry.io/otel/sdk v1.0.1

replace git.garena.com/shopee/platform/service-governance/observability/metric => git.garena.com/shopee/platform/service-governance/observability/metric v1.0.20

replace github.com/codahale/hdrhistogram => github.com/HdrHistogram/hdrhistogram-go v1.1.2

replace go.opentelemetry.io/otel/exporters/prometheus => go.opentelemetry.io/otel/exporters/prometheus v0.24.0

replace github.com/micro/go-micro => github.com/micro/go-micro v1.10.0
