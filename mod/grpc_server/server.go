package grpc_server

import (
	"context"
	"fmt"
	"git.garena.com/shopee/game_platform/comm_lib/middleware"
	"git.garena.com/shopee/game_platform/proto/base"
	"git.garena.com/shopee/game_platform/proto/game_rank"
	"git.garena.com/shopee/game_platform/rank_server/handler"
	grpcSPI "git.garena.com/shopee/mts/go-application-server/spi/grpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health/grpc_health_v1"
)

type HandlerWarp struct {
	// you can have other deps such as configSPI.Registry here
	game_rank.UnimplementedRankServer
	trueHandler *handler.RankHandler
}

func NewHandlerWarp() *HandlerWarp {
	return &HandlerWarp{
		trueHandler: &handler.RankHandler{},
	}
}

func (h *HandlerWarp) IncrScore(ctx context.Context, req *game_rank.IncrScoreRequest) (resp *game_rank.CommonResponse, err error) {
	resp = &game_rank.CommonResponse{}
	err = h.trueHandler.IncrScore(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) Topn(ctx context.Context, req *game_rank.TopnRequest) (resp *game_rank.TopnResponse, err error) {
	resp = &game_rank.TopnResponse{
		Resp: &game_rank.CommonResponse{},
	}
	err = h.trueHandler.Topn(ctx, req, resp)
	return resp, err
}
func (h *HandlerWarp) DownLoadTopn(ctx context.Context, req *game_rank.TopnRequest) (resp *game_rank.TopnResponse, err error) {
	resp = &game_rank.TopnResponse{
		Resp: &game_rank.CommonResponse{},
	}
	err = h.trueHandler.DownLoadTopn(ctx, req, resp)
	return resp, err
}
func (h *HandlerWarp) UserRank(ctx context.Context, req *game_rank.UserRankRequest) (resp *game_rank.UserRankResponse, err error) {
	resp = &game_rank.UserRankResponse{
		Resp: &game_rank.CommonResponse{},
	}
	err = h.trueHandler.UserRank(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) UserScore(ctx context.Context, req *game_rank.UserScoreRequest) (resp *game_rank.UserScoreResponse, err error) {
	resp = &game_rank.UserScoreResponse{
		Resp: &game_rank.CommonResponse{},
	}
	err = h.trueHandler.UserScore(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) DeleteUserRank(ctx context.Context, req *game_rank.DeleteUserRankRequest) (resp *game_rank.CommonResponse, err error) {
	resp = &game_rank.CommonResponse{}
	err = h.trueHandler.DeleteUserRank(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) DeleteRank(ctx context.Context, req *game_rank.DeleteRankRequest) (resp *game_rank.CommonResponse, err error) {
	resp = &game_rank.CommonResponse{}
	err = h.trueHandler.DeleteRank(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) UserScoreList(ctx context.Context, req *game_rank.UserScoreListRequest) (resp *game_rank.UserScoreListResponse, err error) {
	resp = &game_rank.UserScoreListResponse{
		Resp: &game_rank.CommonResponse{},
	}
	err = h.trueHandler.UserScoreList(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) IncrScoreV2(ctx context.Context, req *game_rank.IncrScoreRequestV2) (resp *game_rank.CommonResponse, err error) {
	resp = &game_rank.CommonResponse{}
	err = h.trueHandler.IncrScoreV2(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) TopNV2(ctx context.Context, req *game_rank.TopNRequestV2) (resp *game_rank.TopnResponse, err error) {
	resp = &game_rank.TopnResponse{
		Resp: &game_rank.CommonResponse{},
	}
	err = h.trueHandler.TopNV2(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) DownLoadTopNV2(ctx context.Context, req *game_rank.TopNRequestV2) (resp *game_rank.TopnResponse, err error) {
	resp = &game_rank.TopnResponse{
		Resp: &game_rank.CommonResponse{},
	}
	err = h.trueHandler.DownLoadTopNV2(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) UserRankV2(ctx context.Context, req *game_rank.UserRankRequestV2) (resp *game_rank.UserRankResponse, err error) {
	resp = &game_rank.UserRankResponse{
		Resp: &game_rank.CommonResponse{},
	}
	err = h.trueHandler.UserRankV2(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) UserScoreV2(ctx context.Context, req *game_rank.UserScoreRequestV2) (resp *game_rank.UserScoreResponse, err error) {
	resp = &game_rank.UserScoreResponse{
		Resp: &game_rank.CommonResponse{},
	}
	err = h.trueHandler.UserScoreV2(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) DeleteUserRankV2(ctx context.Context, req *game_rank.DeleteUserRankRequestV2) (resp *game_rank.CommonResponse, err error) {
	resp = &game_rank.CommonResponse{}
	err = h.trueHandler.DeleteUserRankV2(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) DeleteRankV2(ctx context.Context, req *game_rank.RankV2CommonRequest) (resp *game_rank.CommonResponse, err error) {
	resp = &game_rank.CommonResponse{}
	err = h.trueHandler.DeleteRankV2(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) UserScoreListV2(ctx context.Context, req *game_rank.UserScoreListRequestV2) (resp *game_rank.UserScoreListResponse, err error) {
	resp = &game_rank.UserScoreListResponse{
		Resp: &game_rank.CommonResponse{},
	}
	err = h.trueHandler.UserScoreListV2(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) RankListARCatchV2(ctx context.Context, req *game_rank.RankListARCatchRequestV2) (resp *game_rank.RankListResponse, err error) {
	resp = &game_rank.RankListResponse{
		Resp: &game_rank.CommonResponse{},
	}
	err = h.trueHandler.RankListARCatchV2(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) RankListV2(ctx context.Context, req *game_rank.RankListRequestV2) (resp *game_rank.RankListResponseV2, err error) {
	resp = &game_rank.RankListResponseV2{
		Resp: &game_rank.CommonResponse{},
	}
	err = h.trueHandler.RankListV2(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) SetScoreV2(ctx context.Context, req *game_rank.SetScoreRequestV2) (resp *game_rank.CommonResponse, err error) {
	resp = &game_rank.CommonResponse{}
	err = h.trueHandler.SetScoreV2(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) RankListByUsers(ctx context.Context, req *game_rank.RankListByUsersRequest) (resp *game_rank.RankListByUsersResponse, err error) {
	resp = &game_rank.RankListByUsersResponse{
		Resp: &game_rank.CommonResponse{},
	}
	err = h.trueHandler.RankListByUsers(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) RecoverUserRank(ctx context.Context, req *game_rank.RecoverUserRankRequest) (resp *game_rank.RecoverUserRankResponse, err error) {
	resp = &game_rank.RecoverUserRankResponse{
		Resp: &game_rank.CommonResponse{},
	}
	err = h.trueHandler.RecoverUserRank(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) UpsertNewRankModule(ctx context.Context, req *game_rank.UpsertNewRankModuleReq) (resp *game_rank.UpsertNewRankModuleResp, err error) {
	resp = &game_rank.UpsertNewRankModuleResp{
		Resp: &base.CommonResponse{},
	}
	err = h.trueHandler.UpsertNewRankModule(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) QueryNewRankModule(ctx context.Context, req *game_rank.QueryNewRankModuleReq) (resp *game_rank.QueryNewRankModuleResp, err error) {
	resp = &game_rank.QueryNewRankModuleResp{
		Resp: &base.CommonResponse{},
	}
	err = h.trueHandler.QueryNewRankModule(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) ListNewRankModule(ctx context.Context, req *game_rank.ListNewRankModuleReq) (resp *game_rank.ListNewRankModuleResp, err error) {
	resp = &game_rank.ListNewRankModuleResp{
		Resp: &base.CommonResponse{},
	}
	err = h.trueHandler.ListNewRankModule(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) IncrScoreV3(ctx context.Context, req *game_rank.IncrScoreRequestV3) (resp *game_rank.IncrScoreResponseV3, err error) {
	resp = &game_rank.IncrScoreResponseV3{
		Resp: &base.CommonResponse{},
	}
	err = h.trueHandler.IncrScoreV3(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) SetScoreV3(ctx context.Context, req *game_rank.SetScoreRequestV3) (resp *game_rank.SetScoreResponseV3, err error) {
	resp = &game_rank.SetScoreResponseV3{
		Resp: &base.CommonResponse{},
	}
	err = h.trueHandler.SetScoreV3(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) TopNV3(ctx context.Context, req *game_rank.TopNRequestV3) (resp *game_rank.TopNResponseV3, err error) {
	resp = &game_rank.TopNResponseV3{
		Resp: &base.CommonResponse{},
	}
	err = h.trueHandler.TopNV3(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) UserRankV3(ctx context.Context, req *game_rank.UserRankRequestV3) (resp *game_rank.UserRankResponseV3, err error) {
	resp = &game_rank.UserRankResponseV3{
		Resp: &base.CommonResponse{},
	}
	err = h.trueHandler.UserRankV3(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) UserScoreV3(ctx context.Context, req *game_rank.UserScoreRequestV3) (resp *game_rank.UserScoreResponseV3, err error) {
	resp = &game_rank.UserScoreResponseV3{
		Resp: &base.CommonResponse{},
	}
	err = h.trueHandler.UserScoreV3(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) UserScoreListV3(ctx context.Context, req *game_rank.UserScoreListRequestV3) (resp *game_rank.UserScoreListResponseV3, err error) {
	resp = &game_rank.UserScoreListResponseV3{
		Resp: &base.CommonResponse{},
	}
	err = h.trueHandler.UserScoreListV3(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) FriendsRankV3(ctx context.Context, req *game_rank.FriendsRankRequestV3) (resp *game_rank.FriendsRankResponseV3, err error) {
	resp = &game_rank.FriendsRankResponseV3{
		Resp: &base.CommonResponse{},
	}
	err = h.trueHandler.FriendsRankV3(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) DeleteUserRankV3Admin(ctx context.Context, req *game_rank.DeleteUserRankV3AdminRequest) (resp *game_rank.DeleteUserRankV3AdminResponse, err error) {
	resp = &game_rank.DeleteUserRankV3AdminResponse{
		Resp: &base.CommonResponse{},
	}
	err = h.trueHandler.DeleteUserRankV3Admin(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) RankListV3Admin(ctx context.Context, req *game_rank.RankListV3AdminRequest) (resp *game_rank.RankListV3AdminResponse, err error) {
	resp = &game_rank.RankListV3AdminResponse{
		Resp: &base.CommonResponse{},
	}
	err = h.trueHandler.RankListV3Admin(ctx, req, resp)
	return resp, err
}

func (h *HandlerWarp) IncrScoreV3Admin(ctx context.Context, req *game_rank.IncrScoreV3AdminRequest) (resp *game_rank.IncrScoreV3AdminResponse, err error) {
	resp = &game_rank.IncrScoreV3AdminResponse{
		Resp: &base.CommonResponse{},
	}
	err = h.trueHandler.IncrScoreV3Admin(ctx, req, resp)
	return resp, err
}

type Registerer struct {
	Handler game_rank.RankServer
}

var _ grpcSPI.Registerer = (*Registerer)(nil)

// you must register your handler to grpc.Server
func (r *Registerer) Register(s *grpc.Server) {
	grpc_health_v1.RegisterHealthServer(s, new(middleware.Healthchecker))

	game_rank.RegisterRankServer(s, r.Handler)
}

func GetMethods() []string {
	methods := []string{}
	for _, m := range game_rank.Rank_ServiceDesc.Methods {
		methods = append(methods, fmt.Sprintf("/%s/%s", game_rank.Rank_ServiceDesc.ServiceName, m.MethodName))
	}
	return methods
}
