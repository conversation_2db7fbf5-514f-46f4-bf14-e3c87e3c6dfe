package module

import (
	"context"
	"flag"
	"fmt"
	"strings"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/feed/comm_lib/trace"
	commConfig "git.garena.com/shopee/game_platform/comm_lib/config"
	"git.garena.com/shopee/game_platform/comm_lib/middleware"
	"git.garena.com/shopee/game_platform/comm_lib/safe_go"
	"git.garena.com/shopee/game_platform/comm_lib/utils"
	"git.garena.com/shopee/game_platform/proto/game_rank"
	"git.garena.com/shopee/game_platform/proto/gameplatform_friend"
	"git.garena.com/shopee/game_platform/proto/gameplatform_game"
	"git.garena.com/shopee/game_platform/rank_server/config"
	"git.garena.com/shopee/game_platform/rank_server/dao/cache"
	"git.garena.com/shopee/game_platform/rank_server/dao/mysql"
	"git.garena.com/shopee/game_platform/rank_server/handler"
	"git.garena.com/shopee/game_platform/rank_server/mod/grpc_server"
	"git.garena.com/shopee/mts/go-application-server/gas"
	configspi "git.garena.com/shopee/mts/go-application-server/spi/config"
	grpcSPI "git.garena.com/shopee/mts/go-application-server/spi/grpc"

	"google.golang.org/grpc"
)

var (
	configFile = flag.String("config", "conf/rank_conf.yml", "config file")
)

func RegisterModuleWithConfig(reg configspi.Registry) (*gas.Module, error) {
	cid := env.GetCID()
	handyKey := fmt.Sprintf("gas.db.hardy.%s", cid)
	cacheKey := fmt.Sprintf("gas.cache.%s", cid)
	commConfig.SetCustomHardyConfig(reg, handyKey)
	commConfig.SetCustomCacheConfig(reg, cacheKey)

	if strings.ToUpper(env.GetEnv()) != "LIVE" {
		logkit.Init(logkit.Level("DEBUG"), logkit.EnableCaller(true))
	} else {
		logkit.Init(logkit.MaxAge(30), logkit.Level("INFO"), logkit.MaxSize(1024), logkit.MaxBackups(30), logkit.EnableCaller(true))
	}

	reporter.Init(
		reporter.Env(env.GetEnv()),
		reporter.Region(env.GetCID()),
		reporter.Host(env.GetHost()),
		reporter.Service("game-rank"),
	)

	return gas.New(
		gas.Object(new(config.ConfigWrapper)),
		gas.Object(new(cache.RedisClientWrapper)),
		gas.Object(new(mysql.DBInitialize)),
		gas.Object(new(module)),
		gas.Object(new(utils.HTTPCliWrapper)),

		// grpc client
		gas.Object(new(gameplatform_game.GrpcClientWrapper)),
		gas.Object(new(gameplatform_friend.GrpcClientWrapper)),

		gas.Construct(func() grpc.UnaryClientInterceptor {
			return reporter.ClientReporterInterceptor()
		}, gas.Returns("client-metrics")),
		gas.Construct(func(ic1 grpc.UnaryClientInterceptor) []grpcSPI.ClientOption {
			return []grpcSPI.ClientOption{grpcSPI.WithClientUnaryInterceptors(ic1)}
		}, gas.Param(0, "client-metrics"), gas.Returns("client-option")),

		grpcSPI.RegisterClientOption("game_grpc_client", "client-option"),
		grpcSPI.RegisterClientOption("friend_grpc_client", "client-option"),

		// grpc server 中间件
		gas.Construct(func() grpcSPI.XProtocolHealthChecker { return new(middleware.Healthchecker) }, gas.Returns("healthchecker")),

		// access log interceptor
		gas.Construct(func() grpc.UnaryServerInterceptor { return middleware.AccessLogInterceptor() }, gas.Returns("accesslog-interceptor")),
		// reporter interceptor
		gas.Construct(func() grpc.UnaryServerInterceptor { return reporter.ServerReportInterceptor() }, gas.Returns("metrics-interceptor")),
		// trace interceptor
		gas.Construct(func() grpc.UnaryServerInterceptor { return trace.UnaryServerInterceptor() }, gas.Returns("trace-interceptor")),
		// recovery interceptor
		gas.Construct(func() grpc.UnaryServerInterceptor { return middleware.RecoveryInterceptor() }, gas.Returns("recovery-interceptor")),
		// biz response error code report interceptor
		gas.Construct(func() grpc.UnaryServerInterceptor { return middleware.BizErrCodeReportInterceptor() }, gas.Returns("biz-interceptor")),

		gas.Construct(func(ic1, ic2, ic3, ic4, ic5 grpc.UnaryServerInterceptor, hc grpcSPI.XProtocolHealthChecker) []grpcSPI.ServerOption {
			return []grpcSPI.ServerOption{
				grpcSPI.WithServerUnaryInterceptors(ic1),
				grpcSPI.WithServerUnaryInterceptors(ic2),
				grpcSPI.WithServerUnaryInterceptors(ic3),
				grpcSPI.WithServerUnaryInterceptors(ic4),
				grpcSPI.WithServerUnaryInterceptors(ic5),
				grpcSPI.WithXProtocolHealthChecker(hc),
				grpcSPI.WithXProtocolRoutingRules(grpc_server.GetMethods()),
			}
		},
			gas.Param(0, "accesslog-interceptor"),
			gas.Param(1, "metrics-interceptor"),
			gas.Param(2, "trace-interceptor"),
			gas.Param(3, "recovery-interceptor"),
			gas.Param(4, "biz-interceptor"),
			gas.Param(5, "healthchecker"),
			gas.Returns("option"),
		),

		gas.Construct(func() game_rank.RankServer {
			return grpc_server.NewHandlerWarp()
		}, gas.Returns("handler")),
		gas.Construct(func(h game_rank.RankServer) grpcSPI.Registerer {
			return &grpc_server.Registerer{Handler: h}
		}, gas.Param(0, "handler"), gas.Returns("registerer")),

		// register your registerer to the target server defined in the config
		grpcSPI.RegisterServer("game.rank", "registerer",
			grpcSPI.WithServerOptionName("option"),
		),
		// register smoke test and health check
		gas.EventHandler(gas.OnEngineSmokeTest, new(middleware.SmokeTester)),
		gas.EventHandler(gas.OnEngineHealthCheck, new(middleware.GasHealthChecker)),
	)
}

type module struct {
	_ *config.ConfigWrapper     `inject:""`
	_ *mysql.DBInitialize       `inject:""`
	_ *cache.RedisClientWrapper `inject:""`
}

func (m *module) Init() error {
	// configure es,need to change according to env & region
	err := handler.InitElasticWriter(handler.EsUserCoisChanSize)
	if err != nil {
		return err
	}

	handler.InitRankWriter(handler.RankConfig{1024 * 512, 10, 1024 * 1024, 3})
	// add rank v2.0 init
	handler.InitRankEsWriter()

	return nil
}

func (m *module) Run(ctx context.Context, shutdown <-chan gas.ShutdownInfo) error {
	handler.AsyncCloseEsIndex()
	safe_go.Go(handler.RunCronOfUserInfoLocalCache)

	return nil
}
