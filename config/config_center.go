package config

import (
	"fmt"

	"git.garena.com/shopee/game_platform/comm_lib/env"
	"git.garena.com/shopee/game_platform/comm_lib/esutil"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/mts/go-application-server/spi/config/configcenter"
)

var (
	ns        configcenter.Namespace
	defaultNs configcenter.Namespace
)

type ConfigWrapper struct {
	apiDefaultCC configcenter.Namespace `inject:"marketplace_others.gameplatform.ns.rank_server_<ENV>_default"` // only for gas config
	apiCC        configcenter.Namespace `inject:"marketplace_others.gameplatform.ns.rank_server_<ENV>_<CID>"`
}

func (c *ConfigWrapper) Init() error {
	ns = c.apiCC
	defaultNs = c.apiDefaultCC

	fmt.Println("GAS Init: config center")
	return nil
}

func IsTopNReturnErr() bool {
	result := false
	err := ns.Get("is_top_N_return_err", &result)
	if err != nil {
		return true
	}

	return result
}

func GetNewKeyTimestampDaily() int64 {
	result := int64(0)
	_ = ns.Get("new_key_timestamp_daily", &result)
	return result
}

func GetNewKeyTimestampWeekly() int64 {
	result := int64(0)
	_ = ns.Get("new_key_timestamp_weekly", &result)
	return result
}

func GetNewKeyTimestampMonthly() int64 {
	result := int64(0)
	_ = ns.Get("new_key_timestamp_monthly", &result)
	return result
}

func GetLocalCacheExpireMinutes() int64 {
	result := int64(0)
	defaultValue := int64(60)

	err := ns.Get("local_cache_expire_minutes", &result)
	if err != nil {
		return defaultValue
	}
	return result
}

func GetLocalCacheDelCronTimeTicker() int64 {
	result := int64(0)
	defaultValue := int64(60)

	err := ns.Get("local_cache_del_cron_time_ticker_seconds", &result)
	if err != nil {
		return defaultValue
	}
	return result
}

func GetESConfig() (*esutil.ESConfig, error) {
	cfg := &esutil.ESConfig{}
	key := "es_conn_info_default"
	if env.GetEnv() == "live" && env.GetCID() == "br" {
		key = "es_conn_info_br"
	}
	if err := defaultNs.Get(key, cfg); err != nil {
		gamelog.Error("GetESConfig failed", gamelog.Fields{"err": err, "key": key})
		return nil, err
	}
	if len(cfg.Host) == 0 {
		return nil, fmt.Errorf("host fetch from config center is empty")
	}
	return cfg, nil
}
