package config

import (
	"sync"

	"git.garena.com/shopee/feed/microkit"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	goconfig "github.com/micro/go-micro/config"
	"github.com/micro/go-micro/config/source/file"
)

type RankConfig struct {
	BaseConfig microkit.BaseConfig
	once       sync.Once
}

func (h *RankConfig) Scan(configFile string) (err error) {
	h.once.Do(func() {

		goconfig.Load(file.NewSource(
			file.WithPath(configFile),
		))

		conf := goconfig.DefaultConfig
		err = h.BaseConfig.Scan(conf)
		if err != nil {
			gamelog.Error("scan config failed", map[string]interface{}{
				"configFile":configFile,
			})
			return
		}

		//Read Values From Config if need
		var serverName string
		{
			serverName = conf.Get("server.name").String("default-svr")

		}

		// watch change in config file
		go h.watch()

		gamelog.Info("Scan Config finished", map[string]interface{}{
			"serverName":serverName,
		})
	})
	return
}

func (h *RankConfig) watch() {
	w, err := goconfig.Watch("log")
	if err != nil {
		// do something
		gamelog.Error("watch config failed", map[string]interface{}{
			"err":err,
		})
	}
	for {
		// wait for next value
		_, err := w.Next()

		if err != nil {
			gamelog.Error("next config failed", map[string]interface{}{
				"err":err,
			})
			continue
		}
	}
}
