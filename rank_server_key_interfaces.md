# Rank Server 关键类及接口文档

## 1. User Gateway 接口

### 服务：User Gateway
### 关键类/接口：RankHandler
### 关键函数原型：

#### **SetScoreV2/V3 - 设置用户分数**
```go
func (s *RankHandler) SetScoreV2(ctx context.Context, req *game_rank.SetScoreRequestV2, rsp *game_rank.CommonResponse) error
func (s *RankHandler) SetScoreV3(ctx context.Context, req *game_rank.SetScoreRequestV3, rsp *game_rank.SetScoreResponseV3) error
```
**备注**: 异步设置用户分数，支持黑名单检查和分桶存储策略

#### **IncrScoreV2/V3 - 增加用户分数**
```go
func (s *RankHandler) IncrScoreV2(ctx context.Context, req *game_rank.IncrScoreRequestV2, rsp *game_rank.CommonResponse) error
func (s *RankHandler) IncrScoreV3(ctx context.Context, req *game_rank.IncrScoreRequestV3, rsp *game_rank.IncrScoreResponseV3) error
```
**备注**: 原子性增加用户分数，支持管理员和普通用户两种流程

#### **TopNV2/V3 - 获取排行榜前N名**
```go
func (s *RankHandler) TopNV2(ctx context.Context, req *game_rank.TopNRequestV2, rsp *game_rank.TopnResponse) error
func (s *RankHandler) TopNV3(ctx context.Context, req *game_rank.TopNRequestV3, rsp *game_rank.TopNResponseV3) error
```
**备注**: 支持TopN Key优化，黑名单过滤，最大支持10000名查询

#### **UserRankV2/V3 - 获取用户排名**
```go
func (s *RankHandler) UserRankV2(ctx context.Context, req *game_rank.UserRankRequestV2, rsp *game_rank.UserRankResponse) error
func (s *RankHandler) UserRankV3(ctx context.Context, req *game_rank.UserRankRequestV3, rsp *game_rank.UserRankResponseV3) error
```
**备注**: 前10000名精确排名，其他粗略排名，支持黑名单检查

#### **UserScoreV2/V3 - 获取用户分数**
```go
func (s *RankHandler) UserScoreV2(ctx context.Context, req *game_rank.UserScoreRequestV2, rsp *game_rank.UserScoreResponse) error
func (s *RankHandler) UserScoreV3(ctx context.Context, req *game_rank.UserScoreRequestV3, rsp *game_rank.UserScoreResponseV3) error
```
**备注**: 获取单个用户的分数信息，支持黑名单检查

#### **UserScoreListV2/V3 - 批量获取用户分数**
```go
func (s *RankHandler) UserScoreListV2(ctx context.Context, req *game_rank.UserScoreListRequestV2, rsp *game_rank.UserScoreListResponse) error
func (s *RankHandler) UserScoreListV3(ctx context.Context, req *game_rank.UserScoreListRequestV3, rsp *game_rank.UserScoreListResponseV3) error
```
**备注**: 批量查询用户分数，支持详细信息获取和黑名单过滤

#### **FriendsRankV3 - 好友排行榜**
```go
func (s *RankHandler) FriendsRankV3(ctx context.Context, req *game_rank.FriendsRankRequestV3, rsp *game_rank.FriendsRankResponseV3) error
```
**备注**: 集成好友服务，支持好友排行榜查询和社交功能

---

## 2. Admin Gateway 接口

### 服务：Admin Gateway
### 关键类/接口：RankHandler
### 关键函数原型：

#### **IncrScoreV3Admin - 管理员增加分数**
```go
func (s *RankHandler) IncrScoreV3Admin(ctx context.Context, req *game_rank.IncrScoreV3AdminRequest, rsp *game_rank.IncrScoreV3AdminResponse) error
```
**备注**: 管理员特权操作，直接操作ES，绕过异步队列，支持可选Redis同步

#### **DeleteUserRankV3Admin - 删除用户排名**
```go
func (s *RankHandler) DeleteUserRankV3Admin(ctx context.Context, req *game_rank.DeleteUserRankV3AdminRequest, rsp *game_rank.DeleteUserRankV3AdminResponse) error
```
**备注**: 管理员删除用户排名数据，支持ES和Redis双删除

#### **RankListV3Admin - 管理员查询排行榜**
```go
func (s *RankHandler) RankListV3Admin(ctx context.Context, req *game_rank.RankListV3AdminRequest, rsp *game_rank.RankListV3AdminResponse) error
```
**备注**: 管理员专用排行榜查询，支持复杂条件查询和分页

#### **QueryNewRankModule - 查询排行榜模块**
```go
func (s *RankHandler) QueryNewRankModule(ctx context.Context, req *game_rank.QueryNewRankModuleReq, rsp *game_rank.QueryNewRankModuleResp) error
```
**备注**: 查询排行榜模块配置信息

#### **UpsertNewRankModule - 更新排行榜模块**
```go
func (s *RankHandler) UpsertNewRankModule(ctx context.Context, req *game_rank.UpsertNewRankModuleReq, rsp *game_rank.UpsertNewRankModuleResp) error
```
**备注**: 创建或更新排行榜模块配置

#### **ListNewRankModule - 列出排行榜模块**
```go
func (s *RankHandler) ListNewRankModule(ctx context.Context, req *game_rank.ListNewRankModuleReq, rsp *game_rank.ListNewRankModuleResp) error
```
**备注**: 列出所有可用的排行榜模块配置

---

## 3. ODP 接口

### 服务：ODP (Operations Data Platform)
### 关键类/接口：Admin ES Handler
### 关键函数原型：

#### **rankList - 排行榜数据查询**
```go
func rankList(eventId int32, offset, limit, userID uint64, userName string, rankValue float64, rankNum uint64, rankWeek, scope string) ([]*game_rank.RankListItem, uint64)
```
**备注**: ODP专用数据查询接口，支持多种查询条件和分页

#### **rankListV2 - 排行榜数据查询V2**
```go
func rankListV2(index string, condition *RankEsCondition) ([]*game_rank.RankListItem, uint64, string)
```
**备注**: 增强版数据查询，支持ES滚动查询和复杂条件

#### **esRankListV2 - ES排行榜查询**
```go
func esRankListV2(esIndex string, condition *RankEsCondition) ([]*game_rank.RankListItem, uint64, error)
```
**备注**: 直接ES查询接口，支持复杂查询条件和性能优化

#### **queryRankListByScrollV2 - 滚动查询**
```go
func queryRankListByScrollV2(esIndex string, condition *RankEsCondition) ([]*game_rank.RankListItem, uint64, error)
```
**备注**: 大数据量查询优化，支持ES滚动查询机制

---

## 4. 核心业务类

### 服务：Business Logic Handlers
### 关键类/接口：RankHandler, RankWriter, ESWriter
### 关键函数原型：

#### **CheckParamValid - 参数验证**
```go
func CheckParamValid(appid string, rankId int32, checkCurrentTime, isGrayscale bool) (*RankModuleInfo, *BizError)
```
**备注**: 统一参数验证和模块配置检查

#### **GetCachedNewRankModule - 获取缓存模块**
```go
func GetCachedNewRankModule(appId string, rankId int32, isGrayscale bool) (*RankModuleInfo, error)
```
**备注**: 获取排行榜模块配置，支持本地缓存

#### **QueryFriendsRank - 查询好友排名**
```go
func (s *RankHandler) QueryFriendsRank(ctx context.Context, userIds []uint64, rankId int32, appId string) ([]*RankInnerItem, *BizError)
```
**备注**: 批量查询好友排名数据，支持排序和格式化

#### **GetFriends - 获取好友列表**
```go
func GetFriends(logId, appId, deviceId string, userId uint64, sortType, offset, limit, brief int32, relationTypes []int32, sortAppId []string, sortRelationTypes []int32) (*gameplatform_friend.GetFriendsResponse, error)
```
**备注**: 调用好友服务获取好友列表，支持多种排序和过滤条件

---

## 5. 数据结构

### **RankModuleInfo - 排行榜模块信息**
```go
type RankModuleInfo struct {
    Config     *model.NewRankConfigVersionTab
    Appid      string
    ActivityId uint64
    SlotId     uint64
    StartTime  int64
    EndTime    int64
}
```

### **Rankings - 排行榜数据**
```go
type Rankings struct {
    Scope         string
    RankName      string
    Username      string
    Userid        uint64
    Score         float64
    Timestamp     int64
    RankType      int32
    IsSetScoreReq bool
}
```

### **RankInnerItem - 内部排名项**
```go
type RankInnerItem struct {
    Score     float64
    UserName  string
    UserId    int64
    TimeStamp int64
    Avatar    string
}
```

---

## 6. 关键特性说明

### **性能优化**
- **分桶策略**: 减少Redis热点Key问题
- **TopN Key**: 前10000名精确查询优化
- **异步处理**: Channel + Goroutine异步写入
- **批量操作**: ES批量写入提高性能

### **数据一致性**
- **双写机制**: Redis和ES双写保证一致性
- **分布式锁**: 防止重复任务执行
- **重试机制**: 自动重试保证数据完整性

### **安全特性**
- **黑名单检查**: 集成反作弊系统
- **参数验证**: 统一参数验证和错误处理
- **权限控制**: 管理员和普通用户接口分离
