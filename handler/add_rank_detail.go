package handler

import (
	"fmt"
	"git.garena.com/shopee/game_platform/comm_lib/env"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/comm_lib/utils"
	"git.garena.com/shopee/game_platform/rank_server/constant"
	"time"
)

// env_region_scope_rankName
const IndexBasicFormat = "mkt_%s_%s_%s_%s"
const WriteRankDetail = IndexBasicFormat + "_detail"

const rankDetailEsChanLable = "rankDetailEsChan"

func EsRankDetailIndex(scope, rankName string) string {
	env, region := env.GetEnv(), env.GetCID()
	return fmt.Sprintf(WriteRankDetail, env, region, scope, rankName)
}

var rankDetailEsChan chan Rankings

type EsRankDetail struct {
	Rankings
	EventId string `json:"EventId"`
	Week    string `json:"Week"`
}

func InitAddRankDetailToEs(bufferSize int) {
	rankDetailEsChan = make(chan Rankings, bufferSize*10)
	go ConsumerRankDetail()
}

func ConsumerRankDetail() {
	loops, loop, writeSize := 10, 10, constant.DefaultBufferWriteSize
	buffer := make([]Rankings, 0, writeSize*2)

	for {
		select {
		case rank := <-rankDetailEsChan:
			buffer = append(buffer, rank)
			if len(buffer) >= writeSize {
				loop = loops
				WriteRankDetailToEs(buffer)
				buffer = buffer[:0]
			}
		default:
			if loop > 0 {
				loop = loop - 1
				time.Sleep(time.Millisecond * 50)
			} else {
				loop = loops
				if len(buffer) > 0 {
					WriteRankDetailToEs(buffer)
					buffer = buffer[:0]
				}
			}

		}
	}
}

func WriteRankDetailToEs(buffer []Rankings) {
	var temp []EsRequest
	for key, _ := range buffer {
		rank := buffer[key]
		evenid, err := parseRankName(rank.RankName)
		if err != nil {
			gamelog.Error("WriteRankDetailToEs fail, parse rankName err", map[string]interface{}{
				"info": rank,
				"err":  err,
			})
			continue
		}

		yearAndWeek, err := utils.GetYearAndWeekFromTimestamp(int64(rank.Timestamp))

		if err != nil {
			gamelog.Error("WriteRankDetailToEs fail, parse timestamp err", map[string]interface{}{
				"info": rank,
				"err":  err,
			})
			continue
		}
		index := EsRankDetailIndex(rank.Scope, rank.RankName)
		id := fmt.Sprintf("%d_%d", rank.Userid, rank.Timestamp)
		data := EsRankDetail{rank, fmt.Sprintf("%d", evenid), yearAndWeek}
		esreq := EsRequest{index, id, "default", data}
		temp = append(temp, esreq)
	}
	//函数内清零不管用
	//buffer = buffer[:0]
	currentLimiter.Acquire()
	go sendBulkRequest(temp, rankDetailEsChanLable)
}
