package handler

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestUserIdHashBucketRank(t *testing.T) {
	key, name := userIdHashBucketRank("scope", "testrank", uint64(123456), 20)

	fmt.Printf("name : %s,key : %s \n", name, key)
}

func TestEvent_week_format(t *testing.T) {
	rankname, err := WeekRankName(123456, 44)

	fmt.Printf("name : %s,err : %+v \n", rankname, err)
}

func Test_isRankIndex(t *testing.T) {
	tests := []struct {
		name      string
		giveIndex string
		wantRes   bool
	}{
		{
			name:      "gameabr rank index",
			giveIndex: "mkt_gameabr_test_tw_rank_663_2020.21",
			wantRes:   true,
		},
		{
			name:      "gamear rank index",
			giveIndex: "mkt_games_gamear_test_br_rank_287_36",
			wantRes:   true,
		},
		{
			name:      "games rank retention index",
			giveIndex: "mkt_games_wx6rwzcsloqkiq0yac_test_tw_rank_29_retention",
			wantRes:   true,
		},
		{
			name:      "games rank monthly index",
			giveIndex: "mkt_games_eakjhlkr72p7ftau3k_test_id_rank_1_monthly.2020.6",
			wantRes:   true,
		},
		{
			name:      "games rank daily index",
			giveIndex: "mkt_games_9h2sd3lqjz2acydtzw_uat_id_rank_1024_daily.2020.224",
			wantRes:   true,
		},
		{
			name:      "games rank weekly index",
			giveIndex: "mkt_games_kkyn58ob6k6nln3whp_test_vn_rank_62_2020.29",
			wantRes:   true,
		},
		{
			name:      "not rank index",
			giveIndex: "mkt_games_kkyn58ob6k6nln3whp_test_vn_62_2020.29",
			wantRes:   false,
		},
		{
			name:      "not rank index",
			giveIndex: "mkt_gamessdf_9h2sd3lqjz2acydtzw_uat_id_rank_1024_daily.202",
			wantRes:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			res := isRankIndex(tt.giveIndex)
			assert.Equal(t, tt.wantRes, res)
		})
	}
}
