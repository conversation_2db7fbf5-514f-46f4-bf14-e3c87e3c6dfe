package handler

import "github.com/olivere/elastic"

type EsBulkBuffer interface {
	Len() int
	Reset()
	List() []elastic.BulkableRequest
	Append(index, id , docType string, doc interface{})
}

// EsAddBulkBuffer is for add
type EsAddBulkBuffer struct {
	list []elastic.BulkIndexRequest
}

func (buffer *EsAddBulkBuffer) Len() int {
	return len(buffer.list)
}

// Bytes returns a mutable reference to the underlying byte slice.
func (buffer *EsAddBulkBuffer) List() []elastic.BulkableRequest {
	var res []elastic.BulkableRequest
	for index, _ := range buffer.list {
		res = append(res, &buffer.list[index])
	}
	return res
}

func (buffer *EsAddBulkBuffer) Reset() {
	buffer.list = buffer.list[:0]
}

func (buffer *EsAddBulkBuffer) Append(index, id , docType string, doc interface{})  {
	buffer.list = append(buffer.list, elastic.BulkIndexRequest{})
	buffer.list[buffer.Len() -1].OpType("index").Index(index).Id(id).Type(docType).Doc(doc)
}

// EsDeleteBulkBuffer is for delete
type EsDeleteBulkBuffer struct {
	list []elastic.BulkDeleteRequest
}

func (buffer *EsDeleteBulkBuffer) Len() int {
	return len(buffer.list)
}

// Bytes returns a mutable reference to the underlying byte slice.
func (buffer *EsDeleteBulkBuffer) List() []elastic.BulkableRequest {
	var res []elastic.BulkableRequest
	for index, _ := range buffer.list {
		res = append(res, &buffer.list[index])
	}
	return res
}

func (buffer *EsDeleteBulkBuffer) Reset() {
	buffer.list = buffer.list[:0]
}

func (buffer *EsDeleteBulkBuffer) Append(index, id , docType string, doc interface{})  {
	buffer.list = append(buffer.list, elastic.BulkDeleteRequest{})
	buffer.list[buffer.Len() -1].Index(index).Id(id).Type(docType)
}
