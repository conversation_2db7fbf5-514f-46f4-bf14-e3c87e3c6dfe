package handler

import (
	"git.garena.com/shopee/game_platform/proto/base"
	"github.com/golang/protobuf/proto"
)

type BizError struct {
	Code int32  `json:"code"`
	Msg  string `json:"msg"`
}

var (
	ErrParamInvalid  = &BizError{1000, "invalid param"}
	ErrParamEmpty    = &BizError{1001, "empty param"}
	ErrDB            = &BizError{1002, "db error"}
	ErrDraftNotFound = &BizError{1003, "draft not found"}

	ErrInternal               = &BizError{1010, "internal error"}
	ErrRankModuleTimeNotValid = &BizError{1011, "time not valid to change score"}
	ErrRankModuleNotMatch     = &BizError{1012, "rank module not match appid"}
	ErrRankModuleNotFound     = &BizError{1013, "rank module not found"}
	ErrRPC                    = &BizError{1014, "rpc call error"}
	ErrEsQuery                = &BizError{1015, "es query doc error"}
	ErrEsUpdate               = &BizError{1016, "es update doc error"}
	ErrEsQueryNoFound         = &BizError{1017, "es query userid not found"}
	ErrEsScoreNotEnough       = &BizError{1018, "es score not enough to decrease"}
)

func fillBizError(rsp *base.CommonResponse, err *BizError) {
	rsp.ErrCode = proto.Int32(err.Code)
	rsp.ErrMsg = proto.String(err.Msg)
}
