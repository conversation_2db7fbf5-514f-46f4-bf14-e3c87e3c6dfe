package handler

import (
	"context"
	"time"

	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/proto/base"
	"git.garena.com/shopee/game_platform/proto/game_rank"
	"git.garena.com/shopee/game_platform/rank_server/dao"
	"git.garena.com/shopee/game_platform/rank_server/model"
	"github.com/golang/protobuf/proto"
)

func (s *RankHandler) UpsertNewRankModule(ctx context.Context, req *game_rank.UpsertNewRankModuleReq, rsp *game_rank.UpsertNewRankModuleResp) error {
	moduleId := req.GetRankId()
	cd := req.GetConfigData()

	rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(0), ErrMsg: proto.String("ok")}
	if !isConfigValid(cd) {
		fillBizError(rsp.Resp, ErrParamInvalid)
		return nil
	}

	if moduleId == 0 {
		moduleId, err := createNewRankModule(cd)
		if err != nil {
			fillBizError(rsp.Resp, err)
			return nil
		}

		rsp.RankId = proto.Int32(moduleId)
		return nil
	}

	err := updateNewRankModule(moduleId, cd)
	if err != nil {
		fillBizError(rsp.Resp, err)
		return nil
	}

	rsp.RankId = proto.Int32(moduleId)
	return nil
}

func (s *RankHandler) QueryNewRankModule(ctx context.Context, req *game_rank.QueryNewRankModuleReq, rsp *game_rank.QueryNewRankModuleResp) error {
	moduleId := req.GetRankId()
	rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(0), ErrMsg: proto.String("ok")}

	if moduleId == 0 {
		fillBizError(rsp.Resp, ErrParamInvalid)
		return nil
	}

	var err error
	var draft *model.NewRankConfigVersionTab

	if req.GetIsLiveSettings() {
		// for admin builder v6.0 grayscale feature live configure view
		draft, err = dao.NewRankDao().QueryNewRankConfigLive(moduleId)
	} else {
		draft, err = dao.NewRankDao().QueryNewRankConfigDraft(moduleId)
	}
	if err != nil {
		fillBizError(rsp.Resp, ErrDB)
		return nil
	}

	if draft == nil {
		gamelog.Error("draft not found", gamelog.Fields{"moduleId": moduleId})
		fillBizError(rsp.Resp, ErrDraftNotFound)
		return nil
	}

	cd := game_rank.NewRankModuleConfig(*draft.ConfigData)
	rsp.ConfigData = &cd
	return nil
}

func (s *RankHandler) ListNewRankModule(ctx context.Context, req *game_rank.ListNewRankModuleReq, rsp *game_rank.ListNewRankModuleResp) error {
	moduleIds := req.GetRankId()
	rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(0), ErrMsg: proto.String("ok")}

	if len(moduleIds) == 0 {
		fillBizError(rsp.Resp, ErrParamInvalid)
		return nil
	}

	arr, err := dao.NewRankDao().ListNewRankConfig(moduleIds)
	if err != nil {
		fillBizError(rsp.Resp, ErrDB)
		return nil
	}

	rsp.List = make([]*game_rank.NewRankModuleInfo, 0)
	for _, aa := range arr {
		cd := game_rank.NewRankModuleConfig(*aa.ConfigData)
		rsp.List = append(rsp.List, &game_rank.NewRankModuleInfo{
			RankId:     proto.Int32(aa.ModuleId),
			ConfigData: &cd,
		})
	}

	return nil
}

func createNewRankModule(cd *game_rank.NewRankModuleConfig) (int32, *BizError) {
	now := time.Now().Unix()
	moduleId, err := dao.NewRankDao().CreateNewRank(&model.NewRankTab{
		Ctime: now,
		Mtime: now,
	})

	if err != nil {
		gamelog.Error("createNewRankModule|CreateNewRank error", gamelog.Fields{"err": err})
		return 0, ErrDB
	}

	jd := model.RankJSON(*cd)
	err = dao.NewRankDao().CreateNewRankConfigVersion(&model.NewRankConfigVersionTab{
		ModuleId:   moduleId,
		IsDraft:    1,
		ConfigData: &jd,
		Ctime:      now,
		Mtime:      now,
	})

	if err != nil {
		gamelog.Error("createNewRankModule|CreateNewRankConfigVersion error", gamelog.Fields{"err": err})
		return 0, ErrDB
	}

	return moduleId, nil
}

func updateNewRankModule(moduleId int32, cd *game_rank.NewRankModuleConfig) *BizError {
	draft, err := dao.NewRankDao().QueryNewRankConfigDraft(moduleId)
	if err != nil {
		gamelog.Error("updateNewRankModule|QueryNewRankConfigDraft error", gamelog.Fields{"err": err})
		return ErrDB
	}

	if draft == nil {
		return ErrDraftNotFound
	}

	jd := model.RankJSON(*cd)
	draft.ConfigData = &jd
	err = dao.NewRankDao().UpdateNewRankConfigDraft(draft)

	if err != nil {
		gamelog.Error("updateNewRankModule|UpdateNewRankConfigDraft error", gamelog.Fields{"err": err})
		return ErrDB
	}

	return nil
}

func isConfigValid(cd *game_rank.NewRankModuleConfig) bool {
	if cd == nil {
		return false
	}

	cycle := cd.GetRefreshCycle()
	_, ok := game_rank.Cycle_name[cycle]
	return ok
}
