package handler

import (
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/game_platform/comm_lib/esutil"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/game_platform/comm_lib/env"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/comm_lib/utils"
	cc "git.garena.com/shopee/game_platform/rank_server/config"
	"git.garena.com/shopee/game_platform/rank_server/constant"
	"git.garena.com/shopee/game_platform/rank_server/semaphore"

	es "github.com/olivere/elastic"
)

// explain mkt_gameabr_env_region_rank_eventid_week
// example mkt_gameabr_live_sg_rank_157_44
const WeekRankIndex = "mkt_gameabr_%s_%s_rank_%d_%s"
const DocIdFormat = "%d_%d"

const EsUserCoisChanSize = 1024 * 1024

var RetryTimes = 1
var WaitTime = 2 * time.Second
var RetrierTime = 500 // ms

func EsWeekRankIndex(eventid int32, time string) string {
	env, region := env.GetEnv(), env.GetCID()
	return fmt.Sprintf(WeekRankIndex, env, region, eventid, time)
}

func EsDocId(eventid int32, userid uint64) string {
	return fmt.Sprintf(DocIdFormat, eventid, userid)
}

const eschanLable = "esChan"

var (
	esChan         chan EsChanRankings
	currentLimiter = semaphore.NewSemaphore(constant.DefaultSemaphore)
)

func InitElasticWriter(bufferSize int) error {
	esCfg, err := cc.GetESConfig()
	if err != nil {
		return err
	}

	if err := esutil.ElasticConfigure(esCfg); err != nil {
		gamelog.Error("es configure failed", gamelog.Fields{"err": err})
		return err
	}

	gamelog.Info("elasticsearch client success init", nil)

	esChan = make(chan EsChanRankings, bufferSize)

	InitAddRankDetailToEs(bufferSize)

	// start monitor
	go EsChanSizeMonitor()
	// start comsumer
	go ConsumerEsUserCoins()
	return nil
}

type EsUserCois struct {
	EventId int32   `json:"event_id"`
	UserId  uint64  `json:"userid"`
	Coins   float64 `json:"coins"`
	// "rank_score":rank_score*1000000000000+userid,
	RankScore uint64 `json:"rank_score"`
	Status    int    `json:"status"`
	UserName  string `json:"username"`
	Timestamp int64  `json:"timestamp"`
}

type EsChanRankings struct {
	Rankings
	RankScore uint64 `json:"rank_score"`
}

func addEsRanking(rank EsChanRankings) {
	if shouldWrite := shouldWriteEs(rank); !shouldWrite {
		return
	}
	select {
	case esChan <- rank:
		break
	default:
		gamelog.Error("esChan add rankings fail", map[string]interface{}{
			"info": rank,
		})
		err := reporter.ReportCounter(
			METRIC_CHAN_FULL_TOTAL,
			1,
			reporter.Label{"type", "eschan"})
		HandlerReportErr(err, METRIC_CHAN_FULL_TOTAL)
	}
}

func shouldWriteEs(esItem EsChanRankings) bool {
	if esItem.Scope == GameARScope {
		return false
	}
	return true
}

type esBuffer struct {
	sync.Mutex
	m map[uint64]EsChanRankings
}

type EsRequest struct {
	Index   string      `json:"index"`
	Id      string      `json:"id"`
	DocType string      `json:"doc_type"`
	Doc     interface{} `json:"doc"`
}

func ConsumerEsUserCoins() {
	defer func() {
		if r := recover(); r != nil {
			gamelog.Error("[panic recovery]ConsumerEsUserCoins", gamelog.Fields{"r": r})
			go ConsumerEsUserCoins()
		}
	}()

	loops, loop, writeSize := 10, 10, constant.DefaultBufferWriteSize
	buffer := esBuffer{m: make(map[uint64]EsChanRankings)}

	for {
		select {
		case rank := <-esChan:
			buffer.Lock()
			buffer.m[rank.Userid] = rank
			buffer.Unlock()
			if len(buffer.m) >= writeSize {
				loop = loops
				writeToEs(&buffer)
			}
			break
		default:
			if loop > 0 {
				loop = loop - 1
				time.Sleep(time.Millisecond * 50)
			} else {
				loop = loops
				if len(buffer.m) > 0 {
					writeToEs(&buffer)
				}
			}

		}
	}
}

func writeToEs(buffer *esBuffer) {
	var temp []EsRequest
	buffer.Lock()
	defer buffer.Unlock()
	for key, _ := range buffer.m {
		rank := buffer.m[key]
		evenid, err := parseRankName(rank.RankName)
		if err != nil {
			gamelog.Info("es write fail, parse rankName err", map[string]interface{}{
				"info": rank,
				"err":  err,
			})
			continue
		}

		var yearAndWeek string
		if len(rank.RCRankWeek) != 0 {
			yearAndWeek = rank.RCRankWeek
		} else {
			yearAndWeek, err = utils.GetYearAndWeekFromTimestamp(int64(rank.Timestamp))
			if err != nil {
				gamelog.Error("es write fail, parse timestamp err", map[string]interface{}{
					"info": rank,
					"err":  err,
				})
				continue
			}
		}

		index := EsWeekRankIndex(evenid, yearAndWeek)
		id := EsDocId(evenid, rank.Userid)

		// todo 用户不适用用户id 排序。使用时间戳
		// rankscore := float64(rank.score)*1000000000000 + float64(rank.userid)
		esInfo := EsUserCois{
			UserId:    rank.Userid,
			UserName:  rank.Username,
			Coins:     rank.Score,
			Status:    1,
			EventId:   evenid,
			RankScore: rank.RankScore,
			Timestamp: rank.Timestamp,
		}

		req := EsRequest{index, id, "default", esInfo}
		temp = append(temp, req)
	}
	buffer.m = make(map[uint64]EsChanRankings)

	// add currentLimiter, acquire semaphore
	currentLimiter.Acquire()
	go sendBulkRequest(temp, eschanLable)
}

func parseRankName(rankname string) (int32, error) {
	strs := strings.Split(rankname, "_")
	if len(strs) != 3 {
		return 0, errors.New("rankname format must be rank_%s_%s")
	}
	evenid, err := strconv.ParseInt(strs[1], 10, 32)
	if err != nil {
		return 0, err
	}
	return int32(evenid), nil
}

func sendBulkRequest(temp []EsRequest, lable string) {
	defer func() {
		// release semaphore
		currentLimiter.Release()
		if r := recover(); r != nil {
			gamelog.Error("[panic recovery]sendBulkRequest", gamelog.Fields{"r": r})
		}
	}()

	if len(temp) == 0 {
		gamelog.Info("temp data empty", gamelog.Fields{
			"lable": lable,
		})
		return
	}

	gamelog.Info("es index", map[string]interface{}{
		"index": temp[0].Index,
		"item":  temp[0],
	})

	reportRes := "success"
	start := time.Now()

	for retry := 0; retry <= RetryTimes; retry++ {
		bulkReq := esutil.EsClient.Bulk()
		for k, _ := range temp {
			if err := createNonLiveIndex(temp[k].Index); err != nil {
				continue
			}
			req := es.NewBulkIndexRequest().Index(temp[k].Index).Id(temp[k].Id).Type(temp[k].DocType).Doc(temp[k].Doc)
			bulkReq.Add(req)
		}

		res, err := bulkReq.Do(context.Background())

		// 需要把es 批量写入的结果打印，有可能部分成功，部分失败，需要记录
		if err != nil {
			gamelog.Error("es write fail,send bulk req err", map[string]interface{}{
				"res":  res,
				"err":  err,
				"size": len(temp),
				"bulk": temp,
			})
			reportRes = "req_fail"
			break
		}

		if res.Errors {
			gamelog.Error("es write fail,send bulk res err", map[string]interface{}{
				"res":      res,
				"err":      err,
				"size":     len(temp),
				"bulk":     temp,
				"retry":    retry,
				"maxRetry": RetryTimes,
				"sleep":    WaitTime,
			})
			if retry == RetryTimes {
				reportRes = "res_fail"
				break // reach max retry times
			}
			time.Sleep(WaitTime)
			continue // retry
		}

		break // success

	}
	err := reporter.ReportSummary(
		METRIC_CHAN_COMSUME_STATISTIC,
		float64(time.Since(start).Nanoseconds()/1000),
		reporter.Label{"type", lable},
		reporter.Label{"index", "0"},
		reporter.Label{"res", reportRes})
	HandlerReportErr(err, METRIC_CHAN_COMSUME_STATISTIC)

}
