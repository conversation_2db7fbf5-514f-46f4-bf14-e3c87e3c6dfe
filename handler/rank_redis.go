package handler

import (
	"context"
	"fmt"
	"git.garena.com/shopee/game_platform/rank_server/dao"
	"git.garena.com/shopee/game_platform/rank_server/dao/cache"
	spiCache "git.garena.com/shopee/mts/go-application-server/spi/cache"
	"math/rand"
	"strconv"
	"sync/atomic"
	"time"

	"git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/comm_lib/utils"
	common_utils "git.garena.com/shopee/game_platform/comm_lib/utils"
	"git.garena.com/shopee/game_platform/rank_server/constant"
	"git.garena.com/shopee/game_platform/rank_server/util"
)

const TopNMaxRank = 100
const MaxUserRank = 9999
const UserRankThreshold = TopNMaxRank * 1.5
const RankThreshold = TopNMaxRank * 1.5

const failSleepTime = time.Millisecond * 500

var (
	config     RankConfig
	RankWriter rankWriter
)

const (
	GameRCScope = "gamerc"
	GameARScope = "gamear"
)

type Rankings struct {
	Scope     string  `json:"Scope"`
	RankName  string  `json:"RankName"`
	Username  string  `json:"Username"`
	Userid    uint64  `json:"Userid"`
	Score     float64 `json:"Score"`
	Timestamp int64   `json:"Timestamp"`
	// 考虑以后用于重试
	Fail      bool `json:"-"`
	FailTimes int  `json:"-"`

	// 红包雨索引rankweek，用于recovery
	RCRankWeek string `json:"-"`

	IsSetScoreReq bool

	RankType int32
}

type userinfo struct {
	username string
	userid   uint64

	ranktype int32
}

type RankConfig struct {
	RankBufSize, RankWorkerNum int
	InfoBufSize, InfoWorkerNum int
}

type rankWriter struct {
	//addRankScoreChan chan rankings
	rankChan []chan Rankings
	infoChan []chan userinfo
}

func (r *rankWriter) putRankChan(rank Rankings) {
	index := rank.Userid % uint64(config.RankWorkerNum)
	select {
	case r.rankChan[int(index)] <- rank:
		break
	default:
		gamelog.Error("set rank score fail,buffer full", map[string]interface{}{
			"rank": rank,
		})
		err := reporter.ReportCounter(
			METRIC_CHAN_FULL_TOTAL,
			1,
			reporter.Label{"type", "ranChan"})
		HandlerReportErr(err, METRIC_CHAN_FULL_TOTAL)
	}

}

func (r *rankWriter) putInfoChan(info userinfo) {
	index := info.userid % uint64(config.InfoWorkerNum)
	select {
	case r.infoChan[int(index)] <- info:
		break
	default:
		gamelog.Error("add userinfo fail,buffer full", map[string]interface{}{
			"rank": info,
		})
		err := reporter.ReportCounter(
			METRIC_CHAN_FULL_TOTAL,
			1,
			reporter.Label{"type", "infoChan"})
		HandlerReportErr(err, METRIC_CHAN_FULL_TOTAL)
	}
}

func (r *rankWriter) doAddinfo(index int) {
	lastGoTime := time.Now()
	for {
		info := <-r.infoChan[index]
		r.dealUserInfo(&info, index)
		if len(r.infoChan) > 1000 && time.Since(lastGoTime) > 10*time.Millisecond {
			go r.helpDoAddUserInfo(index)
			lastGoTime = time.Now()
		}
	}
}

func (r *rankWriter) dealUserInfo(info *userinfo, index int) {
	//start := time.Now()
	//key := userInfoKey(info.userid, conf.infoRedisBucket)
	//
	//if _, err := cache.RedisUtilHSet(context.Background(), key, strconv.FormatUint(info.userid, 10), info.username); err != nil {
	//	gamelog.Error("redis add userinfo err", map[string]interface{}{
	//		"err":  err,
	//		"info": info,
	//	})
	//	err := reporter.ReportSummary(
	//		METRIC_CHAN_COMSUME_STATISTIC,
	//		float64(time.Since(start).Nanoseconds()/1000),
	//		reporter.Label{"type", "infoChan"},
	//		reporter.Label{"index", strconv.FormatInt(int64(index), 10)},
	//		reporter.Label{"res", "fail"})
	//	HandlerReportErr(err, METRIC_CHAN_COMSUME_STATISTIC)
	//
	//	//put back and sleep for a while
	//	r.putInfoChan(*info)
	//	time.Sleep(failSleepTime)
	//} else {
	//	err := reporter.ReportSummary(
	//		METRIC_CHAN_COMSUME_STATISTIC,
	//		float64(time.Since(start).Nanoseconds()/1000),
	//		reporter.Label{"type", "infoChan"},
	//		reporter.Label{"index", strconv.FormatInt(int64(index), 10)},
	//		reporter.Label{"res", "success"})
	//	HandlerReportErr(err, METRIC_CHAN_COMSUME_STATISTIC)
	//}
}

func (r *rankWriter) helpDoAddUserInfo(index int) {
	atomic.AddInt64(&helpUserInfoGoroutines, 1)
	for {
		select {
		case info := <-r.infoChan[index]:
			{
				r.dealUserInfo(&info, index)
			}
		default:
			{
				atomic.AddInt64(&helpUserInfoGoroutines, -1)
				return
			}
		}
	}
}

func (r *rankWriter) doSetRankScore(index int) {
	defer func() {
		if e := recover(); e != nil {
			gamelog.Error("[panic recovery]doSetRankScore", gamelog.Fields{"r": e, "index": index})
			go r.doSetRankScore(index)
		}
	}()
	lastGoTime := time.Now()
	for {
		rank := <-r.rankChan[index]
		r.dealRank(&rank, index)
		if len(r.rankChan) > 1000 && time.Since(lastGoTime) > 10*time.Millisecond {
			go r.helpDoSetRankScore(index)
			lastGoTime = time.Now()
		}
	}
}

func (r *rankWriter) helpDoSetRankScore(index int) {
	defer func() {
		if r := recover(); r != nil {
			gamelog.Error("[panic recovery]helpDoSetRankScore", gamelog.Fields{"r": r, "index": index})
		}
	}()

	atomic.AddInt64(&helpRankGoroutines, 1)
	for {
		select {
		case rank := <-r.rankChan[index]:
			{
				r.dealRank(&rank, index)
			}
		default:
			{
				atomic.AddInt64(&helpRankGoroutines, -1)
				return
			}
		}
	}
}

func (r *rankWriter) dealRank(rank *Rankings, index int) {
	defer func() {
		if r := recover(); r != nil {
			gamelog.Error("dealRank panic recovery", gamelog.Fields{"r": r, "rank": rank, "index": index})
		}
	}()

	if !tryRankLock(rank) {
		gamelog.Error("dealRank try rank lock failed", map[string]interface{}{"info": rank})
		rank.Fail = true
		r.putRankChan(*rank)
		return
	}
	defer releaseRankLock(rank)

	start := time.Now()
	key, _ := userIdHashBucketRank(rank.Scope, rank.RankName, rank.Userid, conf.rankRedisBucket)

	var isNeedInitRank bool
	var err, initRankError error

	isNeedInitRank, err = IsNeedInitRetentionRank(rank.RankType, rank.Scope, rank.RankName, false)
	if err != nil {
		gamelog.Error("IsNeedInitRetentionRank failed", map[string]interface{}{"info": rank})
		rank.Fail = true
		r.putRankChan(*rank)
		return
	}

	if isNeedInitRank {
		// lock 新锁, 锁住整个榜单
		tmpLockSuccess := tryRetentionRankInitLock(context.Background(), rank.Scope, rank.RankName)
		if !tmpLockSuccess {
			gamelog.Error("tryRetentionRankInitLock failed", map[string]interface{}{"info": rank})
			rank.Fail = true
			r.putRankChan(*rank)
			return
		}

		_, initRankError = InitRetentionRank(rank.Scope, rank.RankName, false)
		_, initRankError = InitRetentionRank(rank.Scope, rank.RankName, true)
		// 初始化失败，忽略错误，下文也不再双写。
		releaseRetentionRankLock(rank.Scope, rank.RankName)
	}

	var newRedisScore, newGameScore float64
	if rank.IsSetScoreReq {
		newGameScore = rank.Score
		timeStampShouldAdd, err := getTimestampShouldAdd(rank)
		if err != nil {
			gamelog.Error("get next week left time err", map[string]interface{}{
				"err":  err,
				"info": rank,
			})
			rank.Fail = true
			r.putRankChan(*rank)
			time.Sleep(failSleepTime)
			return
		}
		newRedisScore = ConcatFloat32AndTimestamp(newGameScore, timeStampShouldAdd)

	} else {

		newRedisScore, newGameScore, err = r.doAddScoreAndReturn(key, rank)
		if err != nil {
			err := reporter.ReportSummary(
				METRIC_CHAN_COMSUME_STATISTIC,
				float64(time.Since(start).Nanoseconds()/1000),
				reporter.Label{"type", "ranChan"},
				reporter.Label{"index", strconv.FormatInt(int64(index), 10)},
				reporter.Label{"res", "fail"})
			HandlerReportErr(err, METRIC_CHAN_COMSUME_STATISTIC)
			return
		}
	}
	if _, err := cache.RedisUtilZAdd(context.Background(), key, newRedisScore, rank.Userid); err != nil {
		gamelog.Error("redis set rank score err", map[string]interface{}{
			"err":   err,
			"info":  rank,
			"key":   key,
			"score": newRedisScore,
		})
		//put back and sleep for a while
		// todo 失败加监控
		err := reporter.ReportSummary(
			METRIC_CHAN_COMSUME_STATISTIC,
			float64(time.Since(start).Nanoseconds()/1000),
			reporter.Label{"type", "ranChan"},
			reporter.Label{"index", strconv.FormatInt(int64(index), 10)},
			reporter.Label{"res", "fail"})
		HandlerReportErr(err, METRIC_CHAN_COMSUME_STATISTIC)

		rank.Fail = true
		r.putRankChan(*rank)
		time.Sleep(failSleepTime)
		return
	}

	if rank.RankType == constant.RankTypeRetention {
		err = dao.SetUserRankTimestamp(UserRankTimeKey(rank.Scope, rank.RankName, rank.RankType, rank.Userid), rank.Timestamp/1000/1000)
		if err != nil {
			gamelog.Error("SetUserRankTimestamp failed", gamelog.Fields{"err": err, "rank": rank})
		}
	}

	// 双写新Key (如果初始化失败，跳过双写。
	if initRankError == nil {
		DoubleWriteTopNKey(newRedisScore, true, true, rank)
	}

	expireRankKey(key, rank.RankType)

	err = reporter.ReportSummary(
		METRIC_CHAN_COMSUME_STATISTIC,
		float64(time.Since(start).Nanoseconds()/1000),
		reporter.Label{"type", "ranChan"},
		reporter.Label{"index", strconv.FormatInt(int64(index), 10)},
		reporter.Label{"res", "success"})
	HandlerReportErr(err, METRIC_CHAN_COMSUME_STATISTIC)

	if isValidEsScore(newGameScore) {
		// 更新分数
		rank.Score = float64(uint64(newGameScore*100)) / 100
		if isValidEsScore(newRedisScore) {
			gamelog.Debug("send to es chann", gamelog.Fields{"rank": rank})
			if util.IsGameRCScope(rank.Scope) {
				addEsRanking(EsChanRankings{*rank, uint64(newRedisScore)})
			} else {
				RankingsEsWriter.Write(&EsChanRankingsV2{*rank, uint64(newRedisScore)})
			}
		} else {
			gamelog.Info("rank score too large to write rank es", gamelog.Fields{
				"score": newRedisScore, "userId": rank.Userid, "scope": rank.Scope, "rankName": rank.RankName})
		}
	} else {
		gamelog.Info("rank score too large to write detail es", gamelog.Fields{
			"score": rank.Score, "newGameScore": newGameScore, "userId": rank.Userid, "scope": rank.Scope, "rankName": rank.RankName})
	}
}

func InitRankWriter(conf RankConfig) {
	config = conf
	RankWriter = DefaultRankWriter()
}

func DefaultRankWriter() rankWriter {

	if (RankConfig{}) == config {
		panic("please init RankConfig")
	}

	writer := rankWriter{}
	writer.rankChan = make([]chan Rankings, config.RankWorkerNum)
	//writer.infoChan = make([]chan userinfo, config.InfoWorkerNum)
	for i := 0; i < config.RankWorkerNum; i++ {
		writer.rankChan[i] = make(chan Rankings, config.RankBufSize)
		go writer.doSetRankScore(i)
	}

	/*
		for i := 0; i < config.InfoWorkerNum; i++ {
			writer.infoChan[i] = make(chan userinfo, config.InfoBufSize)
			go writer.doAddinfo(i)
		}*/

	//start monitor
	go RedisChanSizeMonitor()
	go HelpGoroutineSizeMonitor()
	go RetryFailedDoubleWriteTopN()
	go RetryFailedDoubleWriteBottomN()

	return writer
}

func getTimestampShouldAdd(rank *Rankings) (uint32, error) {
	ts := rank.Timestamp / 1000000000
	if rank.RankType == constant.RankTypeRetention {
		if constant.DefaultBaseTimestamp >= ts {
			return uint32(constant.DefaultBaseTimestamp - ts), nil
		} else {
			return 0, nil
		}
	}

	return utils.GetNextWeekLeftTimeMilli(rank.Timestamp)
}

func isValidEsScore(score float64) bool {
	if common_utils.IsStressTest(context.Background()) {
		return false
	}

	if uint64(score) < constant.MaxRankEsScore {
		return true
	}
	return false
}

func (r *rankWriter) doAddScoreAndReturn(key string, rank *Rankings) (float64, float64, error) {
	var scoreBeforeAdd float64
	var timeStampBeforeAdd uint32

	redisScore, err := cache.RedisUtilZScore(context.Background(), key, fmt.Sprintf("%d", rank.Userid))
	if err != nil {
		if err != spiCache.RedisNil {
			gamelog.Error("redis get score err", map[string]interface{}{
				"err":  err,
				"info": rank,
				"key":  key,
			})
			rank.Fail = true
			r.putRankChan(*rank)
			time.Sleep(failSleepTime)
			return 0, 0, err
		}
	} else {
		scoreBeforeAdd, timeStampBeforeAdd = SplitFloat32AndTimestamp(redisScore)
	}

	timeStampShouldAdd, err := getTimestampShouldAdd(rank)
	if err != nil {
		gamelog.Error("get next week left time err", map[string]interface{}{
			"err":  err,
			"info": rank,
		})
		rank.Fail = true
		r.putRankChan(*rank)
		time.Sleep(failSleepTime)
		return 0, 0, err
	}

	if timeStampBeforeAdd > 0 && timeStampShouldAdd > timeStampBeforeAdd {
		timeStampShouldAdd = timeStampBeforeAdd
	}
	gameScore := rank.Score + scoreBeforeAdd
	newRedisScore := ConcatFloat32AndTimestamp(gameScore, timeStampShouldAdd)
	return newRedisScore, gameScore, nil
}

func expireRankKey(key string, rankType int32) {
	if common_utils.IsStressTest(context.Background()) {
		cache.RedisUtilExpire(context.Background(), key, time.Duration(constant.RankKeyStressExp+rand.Int63n(10*60))*time.Second)
		return
	}

	if rankType == constant.RankTypeWeek {
		cache.RedisUtilExpire(context.Background(), key, time.Duration(int64(constant.RankKeyWeekExp))*time.Second)
	} else if rankType == constant.RankTypeDaily {
		cache.RedisUtilExpire(context.Background(), key, time.Duration(int64(constant.RankKeyDailyExp))*time.Second)
	} else if rankType == constant.RankTypeMonth {
		cache.RedisUtilExpire(context.Background(), key, time.Duration(int64(constant.RankKeyMonthExp))*time.Second)
	} else {
		cache.RedisUtilExpire(context.Background(), key, time.Duration(int64(constant.RankKeyRetentionExp))*time.Second)
	}
}
