package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/game_platform/comm_lib/esutil"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/proto/game_rank"
	"git.garena.com/shopee/game_platform/rank_server/constant"
	"git.garena.com/shopee/game_platform/rank_server/dao/cache"
	"git.garena.com/shopee/game_platform/rank_server/util"
	"github.com/golang/protobuf/proto"
	"github.com/olivere/elastic"
)

const (
	dailyRankEsExpire     = 86400 * 31 * 1 // 1 month
	weeklyRankEsExpire    = 86400 * 31 * 4 // 4 month
	monthlyRankEsExpire   = 86400 * 31 * 6 // 6 month
	retentionRankEsExpire = 86400 * 31 * 2 // 2 month
	closeEsIndexTaskName  = "close_es_index_task"
)

func AsyncCloseEsIndex() {
	if strings.ToLower(env.GetCID()) != "sg" {
		return
	}
	go func() {
		for {
			select {
			case <-time.After(time.Hour * 24):
				doCloseEsIndex()
			}
		}
	}()
}

func doCloseEsIndex() {
	defer func() {
		if r := recover(); r != nil {
			gamelog.Error("[panic recovery]doCloseEsIndex", gamelog.Fields{"r": r})
		}
	}()

	if esutil.EsClient == nil {
		return
	}

	success, err := tryAcquireTaskLock(closeEsIndexTaskName)
	if err != nil {
		gamelog.Info("tryAcquireTaskLock failed", gamelog.Fields{"err": err})
		return
	}

	if !success {
		return
	}

	gamelog.Info("tryAcquireTaskLock success, job start", nil)
	defer func() {
		if err := delTaskLock(closeEsIndexTaskName); err != nil {
			gamelog.Error("delTaskLock failed", gamelog.Fields{"err": err})
		}
	}()

	var allIndicesResponse []elastic.CatIndicesResponseRow
	// platform game and arcatch rank index
	res, err := getEsIndexInfo("mkt_games_*rank*")
	if err != nil {
		gamelog.Error("CatIndices failed", gamelog.Fields{"err": err})
		return
	}
	allIndicesResponse = append(allIndicesResponse, res...)
	// raining coins rank index
	res, err = getEsIndexInfo("mkt_gameabr_*rank*")
	if err != nil {
		gamelog.Error("CatIndices failed", gamelog.Fields{"err": err})
		return
	}
	allIndicesResponse = append(allIndicesResponse, res...)

	for _, v := range allIndicesResponse {
		if v.Status == "open" {
			ctime := v.CreationDate / 1000
			if needCloseIndex(v.Index, ctime) {
				gamelog.Info("index need close", gamelog.Fields{"index": v.Index, "ctime": ctime})
				_, err := esutil.EsClient.CloseIndex(v.Index).Do(context.Background())
				if err != nil {
					gamelog.Error("close index failed", gamelog.Fields{"err": err, "index": v.Index})
				} else {
					gamelog.Info("close index success", gamelog.Fields{"index": v.Index})
				}
			}
		}
	}
}

func tryAcquireTaskLock(taskName string) (bool, error) {
	key := getTaskLock(taskName)
	val := time.Now().Unix()
	return cache.RedisUtilSetNX(context.Background(), key, val, time.Duration(3600)*time.Second)
}

func delTaskLock(taskName string) error {
	key := getTaskLock(taskName)
	_, err := cache.RedisUtilDelete(context.Background(), key)
	return err
}

func getTaskLock(taskName string) string {
	return fmt.Sprintf("%s_lock", taskName)
}

func isDailyRankIndex(index string) bool {
	if strings.Index(index, constant.RankDaily) > 0 {
		return true
	}
	return false
}

func isMonthlyRankIndex(index string) bool {
	if strings.Index(index, constant.RankMonthly) > 0 {
		return true
	}
	return false
}

func isRetentionRankIndex(index string) bool {
	if strings.Index(index, constant.RankRetention) > 0 {
		return true
	}
	return false
}

func needCloseIndex(index string, ctime int64) bool {
	if !isRankIndex(index) {
		return false
	}
	elapsed := time.Now().Unix() - ctime
	// raining coins and arcatch 的index要特殊处理，并且都只用到了周榜
	if strings.HasPrefix(index, "mkt_gameabr") || strings.HasPrefix(index, "mkt_games_gamear") {
		if elapsed > weeklyRankEsExpire {
			return true
		}
	} else {
		if isRetentionRankIndex(index) && elapsed > retentionRankEsExpire {
			// 只取最后时间插入的数据
			list, total, err := getIndexLastTimeStampDoc(index)
			gamelog.Info("search retention index info", gamelog.Fields{"list": list, "total": total, "index": index})
			if err != nil {
				gamelog.Error("get last insert es date has err", gamelog.Fields{"index": index})
				num, err := countIndexDocNum(index)
				if err != nil {
					return false
				}
				// 去掉index内没有doc的
				return num == 0
			}
			if len(list) == 0 {
				return false
			}
			lastTimestamp := util.ConvertNanoSecond(list[0].GetNewRankTimestamp()).Unix()
			elapsed = time.Now().Unix() - lastTimestamp
			return elapsed > retentionRankEsExpire

		} else if isDailyRankIndex(index) {
			if elapsed > dailyRankEsExpire {
				return true
			}
		} else if isMonthlyRankIndex(index) {
			if elapsed > monthlyRankEsExpire {
				return true
			}
		} else if elapsed > weeklyRankEsExpire {
			return true
		}
	}
	return false
}

func isRankIndex(index string) bool {
	pattern := `mkt_(games_([a-zA-Z0-9]+)|gameabr)_(test|uat|live)_([a-z]{2})_rank_(\w+)_(retention|daily.\d{4}.\d{1,3}|monthly.\d{4}.\d{1,2}|\d{4}.\d{1,2}|\d{1,2})$`
	match, err := regexp.MatchString(pattern, index)
	if err != nil {
		gamelog.Error("regexp match failed", gamelog.Fields{"index": index, "err": err})
		return false
	}
	return match
}

func countIndexDocNum(index string) (int, error) {
	res, err := esutil.EsClient.CatCount().Index(index).Do(context.Background())
	if err != nil {
		gamelog.Error("cat count has err ", gamelog.Fields{"err": err, "index": index})
		return 0, err
	}
	return res[0].Count, nil
}
func getIndexLastTimeStampDoc(esIndex string) ([]*game_rank.RankListItem, uint64, error) {
	// build source body
	source := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []map[string]interface{}{
					0: {"term": map[string]interface{}{
						"status": 1}},
				},
			},
		},
		"sort": map[string]interface{}{
			"timestamp": map[string]interface{}{
				"order": "desc",
			}},
		"from": 0,
		"size": 1,
	}

	res, err := esutil.EsClient.Search(esIndex).Source(source).Do(context.Background())
	if err != nil {
		gamelog.Error("arcatch admin rank list: query list by user id, name or coins err", map[string]interface{}{
			"index": esIndex,
			"err":   err,
		})
		return nil, 0, err
	}
	result := make([]*game_rank.RankListItem, 0, len(res.Hits.Hits))
	for _, v := range res.Hits.Hits {
		var temp EsUserCoinsV2
		if err := json.Unmarshal(*v.Source, &temp); err != nil {
			gamelog.Error("json unmarshal es source err", map[string]interface{}{

				"index": v.Index,
				"err":   err,
			})
			continue
		}
		tempItem := &game_rank.RankListItem{}
		tempItem.UserId = proto.Uint64(temp.UserId)
		tempItem.UserName = proto.String(temp.UserName)
		score, timestamp := SplitFloat32AndTimestamp(float64(temp.RankScore))
		tempItem.RankValue = proto.Float64(score)
		tempItem.RankScore = proto.Uint64(temp.RankScore)
		tempItem.RankTimestamp = proto.Uint64(uint64(timestamp))
		tempItem.NewRankTimestamp = proto.Int64(temp.Timestamp)
		result = append(result, tempItem)
	}
	return result, uint64(res.TotalHits()), nil
}
