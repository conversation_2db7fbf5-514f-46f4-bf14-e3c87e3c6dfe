package handler

import (
	"context"
	"fmt"
	spiCache "git.garena.com/shopee/mts/go-application-server/spi/cache"

	"git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/comm_lib/utils"
	rankConfig "git.garena.com/shopee/game_platform/rank_server/config"
	"git.garena.com/shopee/game_platform/rank_server/constant"
	"git.garena.com/shopee/game_platform/rank_server/dao/cache"
	"github.com/mohae/deepcopy"
	"strconv"
	"time"
)

type RetryModel struct {
	Score float64
	Uid   uint64
	Key   string
}

var newTopNKeyChan chan *Rankings
var newBottomNKeyChan chan *Rankings

func init() {
	newTopNKeyChan = make(chan *Rankings, 1000)
	newBottomNKeyChan = make(chan *Rankings, 1000)
}

// 是否需要是用 Top10k Key
func IsUseTop10kKey(rankType int32) bool {
	if rankType == constant.RankTypeRetention {
		return true
	}

	timeNow, _ := utils.GetTimeStamp()
	dailyRankNewTime := rankConfig.GetNewKeyTimestampDaily()
	weekRankNewTime := rankConfig.GetNewKeyTimestampWeekly()
	monthRankNewTime := rankConfig.GetNewKeyTimestampMonthly()

	if rankType == constant.RankTypeDaily && timeNow > dailyRankNewTime {
		return true
	}
	if rankType == constant.RankTypeWeek && timeNow > weekRankNewTime {
		return true
	}
	if rankType == constant.RankTypeMonth && timeNow > monthRankNewTime {
		return true
	}
	return false
}

func IsNeedInitRetentionRank(rankType int32, scopeName, rankName string, isReverse bool) (bool, error) {
	if rankType != constant.RankTypeRetention {
		return false, nil
	}

	newRankKey := GetRankKeyV2(scopeName, rankName, isReverse)
	isExist, err := cache.RedisUtilExists(context.Background(), newRankKey)
	if err != nil {
		gamelog.Error("dealRank try retention rank lock failed", map[string]interface{}{"newRankKey": newRankKey})
		return false, err
	}
	return isExist == 0, nil
}

// 初始化榜单，仅用于retention 类型榜单
func InitRetentionRank(scopeName, rankName string, isReverse bool) ([]cache.ZsetElem, error) {
	// 获取原来的数据
	keyName := scopeRankName(scopeName, rankName)
	res, err := allbucketZSetRange(keyName, topNRankKeySizeMax, isReverse, conf.rankRedisBucket, false, true)
	if err != nil {
		gamelog.Error("InitRetentionRank zset range err", map[string]interface{}{
			"err":     err,
			"keyName": keyName,
		})
		return nil, err
	}

	if len(res) == 0 {
		return res, nil
	}

	// 打包
	zaddElemList := []*cache.ZsetElem{}
	for _, tmpElem := range res {
		zaddElemList = append(zaddElemList, &cache.ZsetElem{
			Value: tmpElem.Value,
			Score: tmpElem.Score,
		})
	}

	// 写进新Key，如果失败，忽略错误，等下次初始化，日志+监控+告警
	newRankKey := GetRankKeyV2(scopeName, rankName, isReverse)

	_, err = cache.RedisUtilZAddList(context.Background(), newRankKey, zaddElemList)
	if err != nil {
		gamelog.Error("InitRetentionRank ZaddList err", map[string]interface{}{
			"err":     err,
			"keyName": keyName,
		})

		_ = reporter.ReportCounter(METRIC_INIT_TOPN_KEY, 1, reporter.Label{"code", "failed"})
		return res, nil
	}
	expireRankKey(newRankKey, constant.RankTypeRetention)

	// 每个榜单理论只会初始化一次
	gamelog.Info("InitRetentionRank Success", map[string]interface{}{
		"keyName":    keyName,
		"newRankKey": newRankKey,
	})
	_ = reporter.ReportCounter(METRIC_INIT_TOPN_KEY, 1, reporter.Label{"code", "success"})
	return res, nil
}

func DoubleWriteTopNKey(newRedisScore float64, isWriteTopN, isWriteBottomN bool, rank *Rankings) {
	defer func() {
		if r := recover(); r != nil {
			gamelog.Error("[panic recovery]DoubleWriteTopNKey", gamelog.Fields{"r": r})
		}
	}()

	// 如果失败，放入channel， 等待重试
	// 里面已经有日志 + 监控
	if isWriteTopN {
		err := doubleWriteTopNKey(newRedisScore, rank)
		if err != nil {
			tmpRank := deepcopy.Copy(rank).(*Rankings)
			putDWFChan(isWriteTopN, false, tmpRank)
		}
	}

	if isWriteBottomN {
		err := doubleWriteBottomNKey(newRedisScore, rank)
		if err != nil {
			tmpRank := deepcopy.Copy(rank).(*Rankings)
			putDWFChan(false, isWriteBottomN, tmpRank)
		}
	}
}

// 双写到 维护前Top10k 的Key
func doubleWriteTopNKey(newRedisScore float64, rank *Rankings) error {
	if !IsUseTop10kKey(rank.RankType) {
		return nil
	}
	newTopNKey := GetRankKeyV2(rank.Scope, rank.RankName, false)

	// 获取所有信息
	memberCount, totalCount, isBeforeTopN, lastOne, err := GetAllNeedInfoOfRank(false, newTopNKey, rank)
	if err != nil {
		gamelog.Error("GetAllInfoOfRank err", gamelog.Fields{"err": err, "rank": rank})
		return err
	}

	// 如果分数小于最后一名，且整个榜单的用户数量超过max size，无需双写
	if len(lastOne) > 0 && newRedisScore <= lastOne[0].Score && totalCount >= topNRankKeySizeMax {
		// 但如果该用户在此榜单内，那么此时应该去掉
		if isBeforeTopN {
			_, err = cache.RedisUtilBatchZRem(context.Background(), newTopNKey, rank.Userid)
			if err != nil {
				gamelog.Error("doubleWriteTopNKey Zrem err", gamelog.Fields{"err": err})
			}
			if memberCount <= topNRankKeyReInitSize {
				// 榜单成员过少，触发重新初始化, 理论上永远不会触发
				gamelog.Info("Re Init Rank TopN, Why???", gamelog.Fields{"rank": rank, "memberCount": memberCount, "lastOne": lastOne, "newRedisScore": newRedisScore, "totalCount": totalCount})
				ReInitRankTopN(false, rank)
			}
		}
		return nil
	}

	// 走到这里的条件：
	// 1 分数比最后一名大
	// 2 分数比最后一名小，但是总榜单数没填满TopN
	if _, err := cache.RedisUtilZAdd(context.Background(), newTopNKey, newRedisScore, rank.Userid); err != nil {
		// 双写失败，日志 + 监控 + 告警
		gamelog.Error("doubleWriteTopNKey err", map[string]interface{}{
			"err":   err,
			"info":  rank,
			"key":   newTopNKey,
			"score": newRedisScore,
		})
		_ = reporter.ReportSummary(
			METRIC_CHAN_CONSUME_TOPNKEY,
			1,
			reporter.Label{"type", "zadd"},
		)
		return err
	}

	// 如果满员，
	// 有可能该用户，本身就在榜单内，此时不应rem
	// 否则，应该rem 最后一名
	if memberCount > topNRankKeySizeMax && !isBeforeTopN {
		_, err = cache.RedisUtilZRemRangeByRank(context.Background(), newTopNKey, 0, 0)
		if err != nil {
			gamelog.Error("doubleWriteTopNKey ZRemRangeByRank", map[string]interface{}{
				"err":   err,
				"info":  rank,
				"key":   newTopNKey,
				"score": newRedisScore,
			})
		}
		// 忽略错误，超出几个问题不大。
	}

	expireRankKey(newTopNKey, rank.RankType)
	return nil
}

// 双写到 维护后Top10k 的Key
func doubleWriteBottomNKey(newRedisScore float64, rank *Rankings) error {
	if !IsUseTop10kKey(rank.RankType) {
		return nil
	}
	newBottomNKey := GetRankKeyV2(rank.Scope, rank.RankName, true)
	memberCount, totalCount, isBeforeTopN, lastOne, err := GetAllNeedInfoOfRank(true, newBottomNKey, rank)
	if err != nil {
		gamelog.Error("GetAllInfoOfRank err", gamelog.Fields{"err": err, "rank": rank})
		return err
	}

	// 如果分数小于最后一名，且整个榜单的用户数量超过max size，无需双写
	if len(lastOne) > 0 && newRedisScore >= lastOne[0].Score && totalCount >= topNRankKeySizeMax {
		// 但如果该用户在此榜单内，那么此时应该去掉
		if isBeforeTopN {
			_, err = cache.RedisUtilBatchZRem(context.Background(), newBottomNKey, rank.Userid)
			if err != nil {
				gamelog.Error("doubleWriteTopNKey Zrem err", gamelog.Fields{"err": err})
			}
			if memberCount <= topNRankKeyReInitSize {
				// 榜单成员过少，触发重新初始化, 理论上永远不会触发
				gamelog.Info("Re Init Rank TopN, Why???", gamelog.Fields{"rank": rank, "memberCount": memberCount, "lastOne": lastOne, "newRedisScore": newRedisScore, "totalCount": totalCount})
				ReInitRankTopN(true, rank)
			}
		}
		return nil
	}

	// 走到这里的条件：
	// 1 分数比最后一名小
	// 2 分数比最后一名大，但是总榜单数没填满TopN
	if _, err := cache.RedisUtilZAdd(context.Background(), newBottomNKey, newRedisScore, rank.Userid); err != nil {
		gamelog.Error("DoubleWriteTopNRevKey err", map[string]interface{}{
			"err":   err,
			"info":  rank,
			"key":   newBottomNKey,
			"score": newRedisScore,
		})
		_ = reporter.ReportSummary(
			METRIC_CHAN_CONSUME_BOTTOMNKEY,
			1,
			reporter.Label{"type", "zadd"},
		)
		return err
	}

	// 如果满员，
	// 有可能该用户，本身就在榜单内，此时不应rem
	// 否则，应该rem 最后一名
	if memberCount > topNRankKeySizeMax && !isBeforeTopN {
		_, err = cache.RedisUtilZRemRangeByRank(context.Background(), newBottomNKey, -1, -1)
		if err != nil {
			gamelog.Error("DoubleWriteTopNRevKey ZRemRangeByRank", map[string]interface{}{
				"err":   err,
				"info":  rank,
				"key":   newBottomNKey,
				"score": newRedisScore,
			})
		}
		// 忽略错误，超出几个问题不大。
	}

	expireRankKey(newBottomNKey, rank.RankType)
	return nil
}

// 查询TopN
func QueryTopNFromSingleKey(ctx context.Context, scope, rankName string, topN int32, rankType uint32, reverse bool, useCache bool) (res []cache.ZsetElem, err error) {
	newRankNKey := GetRankKeyV2(scope, rankName, reverse)

	// retention 榜单可能需要初始化
	isNeedInitRank, err := IsNeedInitRetentionRank(int32(rankType), scope, rankName, false)
	if err != nil {
		gamelog.Error("IsNeedInitRetentionRank failed", map[string]interface{}{"newRankNKey": newRankNKey})
		return nil, err
	}

	if isNeedInitRank {
		// lock 新锁, 锁住整个榜单
		tmpLockSuccess := tryRetentionRankInitLock(ctx, scope, rankName)
		if !tmpLockSuccess {
			gamelog.Error("tryRetentionRankInitLock failed", map[string]interface{}{"newRankNKey": newRankNKey})
			return nil, err
		}
		//  两个榜单初始化
		InitRetentionRank(scope, rankName, !reverse)
		InitRetentionRank(scope, rankName, reverse)

		releaseRetentionRankLock(scope, rankName)
	}

	res, err = zsetRange(newRankNKey, topN, reverse, useCache)
	if err != nil {
		return []cache.ZsetElem{}, err
	}

	// 无需排序

	if len(res) > 0 {
		gamelog.Info("QueryTopNFromSingleKey Debug", gamelog.Fields{"newRankNKey": newRankNKey, "N": topN, "resL": len(res), "lastOne": res[len(res)-1]})
	}

	if topN > int32(len(res)) {
		return res, nil
	} else {
		return res[:topN], nil
	}
}

// 查询排名, 从 TopN Key
func QueryUserRankV2(ctx context.Context, uid uint64, scope, rankName string, rankType int32, reverse bool) (int64, error) {
	member := strconv.FormatUint(uid, 10)
	var rank int64
	var err error
	if !IsUseTop10kKey(rankType) {
		return 0, spiCache.RedisNil
	}

	newRankNKey := GetRankKeyV2(scope, rankName, reverse)
	// 前后1w名，在此Key中
	if reverse {
		rank, err = cache.RedisUtilZRank(ctx, newRankNKey, member)
	} else {
		rank, err = cache.RedisUtilZRevRank(ctx, newRankNKey, member)
	}

	gamelog.Info("QueryUserRankFromSingleKey debug", gamelog.Fields{"key": newRankNKey, "uid": uid, "rank": rank})
	// 如果新Key中不存在，则排名在1w名之外，但需要判断有无上榜，没上榜会报错
	if err == spiCache.RedisNil {
		oldKey, _ := userIdHashBucketRank(scope, rankName, uid, conf.rankRedisBucket)
		var oldErr error
		if reverse {
			_, oldErr = cache.RedisUtilZRank(ctx, oldKey, member)
		} else {
			_, oldErr = cache.RedisUtilZRevRank(ctx, oldKey, member)
		}

		return MaxUserRank, oldErr
	}

	rank = rank + 1
	if rank > MaxUserRank {
		rank = MaxUserRank
	}
	return rank, nil
}

// 删除Rank
func DeleteUserFromSingleKey(uid uint64, rankType uint32, scope, rankName string, isDeleteKey bool) {
	if !IsUseTop10kKey(int32(rankType)) {
		return
	}
	newTopNKey := GetRankKeyV2(scope, rankName, false)
	newBottomNKey := GetRankKeyV2(scope, rankName, true)

	// 删除整个Key
	if isDeleteKey {
		_, err := cache.RedisUtilDelete(context.Background(), newTopNKey, newBottomNKey)
		if err != nil {
			gamelog.Error("DeleteUserFromSingleKey Delete Key Error", gamelog.Fields{"err": err, "newTopNKey": newTopNKey, "newBottomNKey": newBottomNKey})
		}
		return
	}

	// 删除单个User
	member := strconv.FormatUint(uid, 10)
	_, err := cache.RedisUtilBatchZRem(context.Background(), newTopNKey, member)
	if err != nil {
		gamelog.Error("DeleteUserFromSingleKey Delete newTopNKey Error", gamelog.Fields{"err": err, "newTopNKey": newTopNKey, "uid": uid})
	}

	_, err = cache.RedisUtilBatchZRem(context.Background(), newBottomNKey, member)
	if err != nil {
		gamelog.Error("DeleteUserFromSingleKey Delete newBottomNKey Error", gamelog.Fields{"err": err, "newBottomNKey": newBottomNKey, "uid": uid})
	}
}

func RetryFailedDoubleWriteTopN() {
	defer func() {
		if r := recover(); r != nil {
			gamelog.Error("[panic recovery]RetryFailedDoubleWrite", gamelog.Fields{"r": r})
		}
	}()
	for {
		tmpRank := <-newTopNKeyChan
		// 加锁
		if !tryRankLock(tmpRank) {
			gamelog.Error("dealRank try rank lock failed", gamelog.Fields{"tmpRank": tmpRank})
			// 锁失败，则直接放弃，因为是Zadd，会覆盖这次
			continue
		}

		// 重写
		newRedisScore, err := ReCalScore(tmpRank)
		if err != nil {
			putDWFChan(true, false, tmpRank)
			releaseRankLock(tmpRank)
			continue
		}

		DoubleWriteTopNKey(newRedisScore, true, false, tmpRank)
		releaseRankLock(tmpRank)
	}
}

func RetryFailedDoubleWriteBottomN() {
	defer func() {
		if r := recover(); r != nil {
			gamelog.Error("[panic recovery]RetryFailedDoubleWrite", gamelog.Fields{"r": r})
		}
	}()
	for {
		tmpRank := <-newBottomNKeyChan
		// 加锁
		if !tryRankLock(tmpRank) {
			gamelog.Error("dealRank try rank lock failed", gamelog.Fields{"tmpRank": tmpRank})
			continue
		}
		// 重写
		newRedisScore, err := ReCalScore(tmpRank)
		if err != nil {
			putDWFChan(false, true, tmpRank)
			releaseRankLock(tmpRank)
			continue
		}

		DoubleWriteTopNKey(newRedisScore, false, true, tmpRank)
		releaseRankLock(tmpRank)
	}
}

// put double write failed channel
func putDWFChan(isWriteTopN, isWriteBottomN bool, tmpRank *Rankings) {
	if isWriteTopN {
		select {
		case newTopNKeyChan <- tmpRank:
			break
		default:
			gamelog.Error("set newTopNKeyChan fail,buffer full", gamelog.Fields{"tmpRank": tmpRank})
			_ = reporter.ReportCounter(METRIC_CHAN_CONSUME_TOPNKEY, 1, reporter.Label{"type", "ranChan"})
			time.Sleep(time.Second)
		}
	}

	if isWriteBottomN {
		select {
		case newBottomNKeyChan <- tmpRank:
			break
		default:
			gamelog.Error("set newBottomNKeyChan fail,buffer full", gamelog.Fields{"tmpRank": tmpRank})
			_ = reporter.ReportCounter(METRIC_CHAN_CONSUME_BOTTOMNKEY, 1, reporter.Label{"type", "ranChan"})
			time.Sleep(time.Second)
		}
	}
}

// 从老key中重新计算
func ReCalScore(rank *Rankings) (float64, error) {
	oldKey, _ := userIdHashBucketRank(rank.Scope, rank.RankName, rank.Userid, conf.rankRedisBucket)

	newRedisScore := float64(0)
	if rank.IsSetScoreReq {
		newGameScore := rank.Score
		timeStampShouldAdd, err := getTimestampShouldAdd(rank)
		if err != nil {
			return 0, err
		}
		newRedisScore = ConcatFloat32AndTimestamp(newGameScore, timeStampShouldAdd)

	} else {
		var scoreBeforeAdd float64
		var timeStampBeforeAdd uint32

		redisScore, err := cache.RedisUtilZScore(context.Background(), oldKey, fmt.Sprintf("%d", rank.Userid))
		if err != nil {
			if err != spiCache.RedisNil {
				gamelog.Error("redis get score err", map[string]interface{}{
					"err":  err,
					"info": rank,
					"key":  oldKey,
				})
				return 0, err
			}
		} else {
			scoreBeforeAdd, timeStampBeforeAdd = SplitFloat32AndTimestamp(redisScore)
		}

		timeStampShouldAdd, err := getTimestampShouldAdd(rank)
		if err != nil {
			gamelog.Error("get next week left time err", map[string]interface{}{
				"err":  err,
				"info": rank,
			})
			return 0, err
		}

		if timeStampBeforeAdd > 0 && timeStampShouldAdd > timeStampBeforeAdd {
			timeStampShouldAdd = timeStampBeforeAdd
		}
		gameScore := rank.Score + scoreBeforeAdd
		newRedisScore = ConcatFloat32AndTimestamp(gameScore, timeStampShouldAdd)
	}
	return newRedisScore, nil
}

func GetAllRankUserCount(scopeName, rankName string) (int64, error) {
	count := int64(0)
	for i := 0; i < conf.rankRedisBucket; i++ {
		tmpRedisKey := rankingKey(scopeRankName(scopeName, rankName), strconv.FormatInt(int64(i), 10))
		tmpCount, err := cache.RedisUtilZCard(context.Background(), tmpRedisKey)
		if err != nil {
			gamelog.Error("GetAllRankUserCount zcard err", gamelog.Fields{"tmpRedisKey": tmpRedisKey, "err": err})
			return 0, err
		}
		count += int64(tmpCount)
	}
	return count, nil
}

// ReInitRankTopN  榜单成员过少，触发重新初始化, 理论上永远不会触发
func ReInitRankTopN(isReverse bool, rank *Rankings) {
	// lock 新锁, 锁住整个榜单
	tmpLockSuccess := tryRetentionRankInitLock(context.Background(), rank.Scope, rank.RankName)
	if !tmpLockSuccess {
		gamelog.Error("tryRetentionRankInitLock failed", map[string]interface{}{"info": rank})
	} else {
		InitRetentionRank(rank.Scope, rank.RankName, isReverse)
		releaseRetentionRankLock(rank.Scope, rank.RankName)
	}
}

// GetAllNeedInfoOfRank 返回双写需要的全部信息: TopN目前人数、全部人数、是否在此榜单内、榜单最后一名分数
func GetAllNeedInfoOfRank(isReverse bool, rankKey string, rank *Rankings) (uint64, int64, bool, []cache.ZsetElem, error) {
	// 判断是否存在满员
	memberCount, err := cache.RedisUtilZCard(context.Background(), rankKey)
	if err != nil {
		gamelog.Error("GetAllInfoOfRank Zcard err", map[string]interface{}{
			"err":  err,
			"info": rank,
		})
		return 0, 0, false, nil, err
	}

	// 获得全部榜单人数
	cacheKey := fmt.Sprintf("total_count_%s_%s", rank.Scope, rank.RankName)
	fn := func() (interface{}, error) {
		return GetAllRankUserCount(rank.Scope, rank.RankName)
	}
	totalCountI, err := CacheWrapper.CacheHelper(context.Background(), cacheKey, fn, time.Second*5)
	if err != nil {
		gamelog.Error("GetAllInfoOfRank GetAllRankUserCount err", gamelog.Fields{"err": err, "rank": rank})
		return 0, 0, false, nil, err
	}
	totalCount, _ := totalCountI.(int64)

	// 查询该用户是否在此榜单内
	_, existsErr := cache.RedisUtilZScore(context.Background(), rankKey, fmt.Sprintf("%d", rank.Userid))
	if existsErr != nil && existsErr != spiCache.RedisNil {
		return 0, 0, false, nil, existsErr
	}

	isBeforeTopN := false
	if existsErr == nil {
		isBeforeTopN = true
	}

	// 查询榜单最后一名
	var lastOne []cache.ZsetElem
	if !isReverse {
		lastOne, err = cache.GetCachedZRange(context.Background(), rankKey, 0, 0, time.Minute)
		if err != nil {
			gamelog.Error("GetAllInfoOfRank Zrange err", gamelog.Fields{"err": err, "rank": rank})
			return 0, 0, false, nil, err
		}
	} else {
		lastOne, err = cache.GetCachedZRange(context.Background(), rankKey, -1, -1, time.Minute)
		if err != nil {
			gamelog.Error("DoubleWriteTopNRevKey Zrange err", map[string]interface{}{
				"err":  err,
				"info": rank,
			})
			return 0, 0, false, nil, err
		}
	}

	return uint64(memberCount), totalCount, isBeforeTopN, lastOne, nil
}
