package handler

type Options func(*RankEsWriter)

func InitEsDataAndConvertFunc(opt EsChanDataConvert) Options {
	return func(rsWriter *RankEsWriter) {
		rsWriter.convert = opt
	}
}

func InitEsIndexFormat(opt string) Options {
	return func(rsWriter *RankEsWriter) {
		rsWriter.esIndexFormat = opt
	}
}

func InitEsOperation(opt string) Options {
	return func(rsWriter *RankEsWriter) {
		rsWriter.esOperation = opt
		// important
		rsWriter.bulkEsRequestDataPool = NewPool(rsWriter.bufferSize, rsWriter.esOperation)
	}
}

func InitPrometheusMonitorLabel(opt string) Options {
	return func(rsWriter *RankEsWriter) {
		rsWriter.monitorLabel = opt
	}
}
