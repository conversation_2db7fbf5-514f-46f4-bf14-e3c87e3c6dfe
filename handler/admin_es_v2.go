package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/game_platform/comm_lib/esutil"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/proto/game_rank"
	"git.garena.com/shopee/game_platform/rank_server/constant"
	"github.com/gogo/protobuf/proto"
	"io"
	"strconv"
	"strings"
)

const (
	EsWindowSize               = 10000
	EsDownloadAllScrollIdStart = "-1"
)

func useScroll(condition *RankEsCondition) bool {
	scrollId := condition.GetScrollId()
	return scrollId != ""
}

func rankListV2(index string, condition *RankEsCondition) ([]*game_rank.RankListItem, uint64, string) {
	if rankNum := condition.GetRankNum(); rankNum > 0 {
		result, total, _ := esQueryByRankNumV2(rankNum, index)
		return result, total, ""
	}

	if useScroll(condition) {
		result, total, next, _ := queryRankListByScrollId(index, condition)
		return result, total, next
	}

	//from + size 不能超过window size (10000)
	//如果给了offset，分页获取数据，由于无法获取到scrollId，这里可能会超时
	limit := condition.GetLimit()
	offset := condition.GetOffset()
	if limit+offset > EsWindowSize {
		result, total, _ := queryRankListByScrollV2(index, condition)
		return result, total, ""
	}

	//正常的分页查询（前10000条数据）
	result, total, _ := esRankListV2(index, condition)
	return result, total, ""
}

func esRankListV2(esIndex string, condition *RankEsCondition) ([]*game_rank.RankListItem, uint64, error) {
	source, searchAll := buildEsQuerySource(condition)
	source["from"] = condition.GetOffset()
	source["size"] = condition.GetLimit()
	res, err := esutil.EsClient.Search(esIndex).Source(source).Do(context.Background())
	if err != nil {
		gamelog.Error("esRankListV2 search error", map[string]interface{}{"index": esIndex, "err": err})

		return nil, 0, nil
	}

	total := res.TotalHits()
	if total == 0 {
		return nil, 0, nil
	}

	result := make([]*game_rank.RankListItem, 0, len(res.Hits.Hits))
	rankNum := condition.GetOffset()
	for _, v := range res.Hits.Hits {
		var temp EsUserCoinsV2
		if err := json.Unmarshal(*v.Source, &temp); err != nil {
			gamelog.Error("json unmarshal es source err", map[string]interface{}{
				"index": v.Index,
				"err":   err,
			})
			continue
		}

		if searchAll {
			rankNum++
		} else {
			rankNum = queryRankNumByRankScoreV2(temp.RankScore, temp.UserId, esIndex) + 1
		}

		tempItem := &game_rank.RankListItem{}
		tempItem.UserId = proto.Uint64(temp.UserId)
		tempItem.UserName = proto.String(temp.UserName)
		score, timestamp := SplitFloat32AndTimestamp(float64(temp.RankScore))
		tempItem.RankValue = proto.Float64(score)
		tempItem.RankScore = proto.Uint64(temp.RankScore)
		tempItem.RankNum = proto.Uint64(rankNum)
		tempItem.RankTimestamp = proto.Uint64(uint64(timestamp))
		tempItem.NewRankTimestamp = proto.Int64(temp.Timestamp / 1000 / 1000)
		result = append(result, tempItem)
	}

	return result, uint64(total), nil
}

func queryRankListByScrollId(esIndex string, condition *RankEsCondition) ([]*game_rank.RankListItem, uint64, string, error) {
	source, _ := buildEsQuerySource(condition)
	limit := condition.GetLimit()
	if limit == 0 || limit > EsWindowSize {
		limit = EsWindowSize
	}
	scrollId := condition.GetScrollId()
	if scrollId == EsDownloadAllScrollIdStart {
		scrollId = ""
	}
	result := make([]*game_rank.RankListItem, 0, limit)
	scroll := esutil.EsClient.Scroll(esIndex).Type("default").Body(source).Size(int(limit)).ScrollId(scrollId)
	results, err := scroll.Do(context.Background())
	if err == io.EOF {
		gamelog.Error("queryRankListByScrollId eof returned", gamelog.Fields{"scrollId": scrollId, "index": esIndex})
		return nil, 0, "", nil // all results retrieved
	}
	if err != nil {
		gamelog.Error("queryRankListByScrollId err returned", gamelog.Fields{"scrollId": scrollId, "index": esIndex, "err": err})
		return nil, 0, "", err // something went wrong
	}

	if results.Hits == nil {
		gamelog.Error("queryRankListByScrollId hits nil returned", gamelog.Fields{"scrollId": scrollId, "index": esIndex, "err": err})
		return nil, 0, "", nil
	}

	for _, hit := range results.Hits.Hits {
		var temp EsUserCoinsV2
		err := json.Unmarshal(*hit.Source, &temp)
		if err != nil {
			gamelog.Error("queryRankListByScrollId parse json to EsUserCoinsV2 failed", gamelog.Fields{
				"json": *hit.Source,
				"err":  err,
			})
			continue
		}

		score, _ := SplitFloat32AndTimestamp(float64(temp.RankScore))
		result = append(result, &game_rank.RankListItem{
			UserId:    proto.Uint64(temp.UserId),
			UserName:  proto.String(temp.UserName),
			RankValue: proto.Float64(score),
			RankScore: proto.Uint64(temp.RankScore),

			//scroll查找时，由于可能有过滤机器人id的查询，这里统一由客户端来赋值
			//RankNum:          proto.Uint64(rankNum),
			NewRankTimestamp: proto.Int64(temp.Timestamp / 1000 / 1000),
		})
	}

	return result, uint64(results.TotalHits()), results.ScrollId, nil
}

func buildEsQuerySource(condition *RankEsCondition) (map[string]interface{}, bool) {
	var searchAll bool
	source := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []map[string]interface{}{
					0: {"term": map[string]interface{}{
						"status": 1}},
				},
			},
		},
		"sort": map[string]interface{}{
			"rank_score": map[string]interface{}{
				"order": "desc"},
			"user_id": map[string]interface{}{
				"order": "asc"},
		},
	}

	must := source["query"].(map[string]interface{})["bool"].(map[string]interface{})["must"].([]map[string]interface{})
	if userId := condition.GetUserId(); userId > 0 {
		must = append(must, map[string]interface{}{
			"term": map[string]interface{}{
				"user_id": userId,
			},
		})
		searchAll = false
	}

	if userName := condition.GetUserName(); userName != "" {
		must = append(must, map[string]interface{}{
			"wildcard": map[string]interface{}{
				"user_name.keyword": "*" + userName + "*",
			},
		})
		searchAll = false
	}

	// -1 means no rank value
	if rankValue := condition.GetRankValue(); rankValue > -1 {
		must = append(must, map[string]interface{}{
			"term": map[string]interface{}{
				"coins": rankValue,
			},
		})
		searchAll = false
	}

	if robotId := condition.GetRobotId(); robotId > 0 {
		must = append(must, map[string]interface{}{
			"range": map[string]interface{}{
				"user_id": map[string]interface{}{
					"lt": robotId,
				},
			},
		})
		searchAll = false
	}

	source["query"].(map[string]interface{})["bool"].(map[string]interface{})["must"] = must
	return source, searchAll
}

func queryRankListByScrollV2(esIndex string, condition *RankEsCondition) ([]*game_rank.RankListItem, uint64, error) {
	source, searchAll := buildEsQuerySource(condition)
	res, err := esutil.EsClient.Search(esIndex).Source(source).Do(context.Background())
	if err != nil {
		gamelog.Error("arcatch admin rank list: query list by user id, name or coins err", map[string]interface{}{
			"index": esIndex,
			"err":   err,
		})
		return nil, 0, nil
	}

	// begin scroll query
	result := make([]*game_rank.RankListItem, 0, 10)
	offset := condition.GetOffset()
	limit := condition.GetLimit()
	num := uint64(0)
	rankNum := offset
	// Initialize scroller. Just don't call Do yet.
	scroll := esutil.EsClient.Scroll(esIndex).Type("default").Body(source).Size(int(limit))
	for num <= offset {
		results, err := scroll.Do(context.Background())
		if err == io.EOF {
			return nil, 0, nil // all results retrieved
		}
		if err != nil {
			return nil, 0, err // something went wrong
		}

		for _, hit := range results.Hits.Hits {
			if num >= offset {
				// Deserialize
				var temp EsUserCoinsV2
				err := json.Unmarshal(*hit.Source, &temp)
				if err != nil {
					gamelog.Error("parse json to EsUserCoinsV2 failed", gamelog.Fields{
						"json": *hit.Source,
						"err":  err,
					})
					continue
				}
				if searchAll {
					rankNum++
				} else {
					rankNum = queryRankNumByRankScoreV2(temp.RankScore, temp.UserId, esIndex) + 1
				}
				// add to response rank list
				score, _ := SplitFloat32AndTimestamp(float64(temp.RankScore))
				result = append(result, &game_rank.RankListItem{
					UserId:           proto.Uint64(temp.UserId),
					UserName:         proto.String(temp.UserName),
					RankValue:        proto.Float64(score),
					RankScore:        proto.Uint64(temp.RankScore),
					RankNum:          proto.Uint64(rankNum),
					NewRankTimestamp: proto.Int64(temp.Timestamp / 1000 / 1000),
				})
			}

			num++
		}
	}

	return result, uint64(res.TotalHits()), nil
}

func esQueryByRankNumV2(rankNum uint64, esIndex string) ([]*game_rank.RankListItem, uint64, error) {
	// build source body
	source := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []map[string]interface{}{
					0: {"term": map[string]interface{}{
						"status": 1}},
				},
			},
		},
		"sort": map[string]interface{}{
			"rank_score": map[string]interface{}{
				"order": "desc"},
			"user_id": map[string]interface{}{
				"order": "asc"},
		},
		"from": rankNum - 1,
		"size": 1,
	}

	res, err := esutil.EsClient.Search(esIndex).Source(source).Do(context.Background())
	if err != nil || len(res.Hits.Hits) != 1 {
		gamelog.Error("esQueryByRankNumV2 err", map[string]interface{}{
			"rank_num": rankNum,
			"index":    esIndex,
			"err":      err,
		})
		return nil, 1, nil
	}

	result := make([]*game_rank.RankListItem, len(res.Hits.Hits))
	for index, v := range res.Hits.Hits {
		var temp EsUserCoinsV2
		if err := json.Unmarshal(*v.Source, &temp); err != nil {
			gamelog.Error("json unmarshal es source err", map[string]interface{}{
				"index": index,
				"err":   err,
			})
			continue
		}
		result[index] = &game_rank.RankListItem{}
		result[index].UserId = proto.Uint64(temp.UserId)
		result[index].UserName = proto.String(temp.UserName)
		score, timestamp := SplitFloat32AndTimestamp(float64(temp.RankScore))
		result[index].RankValue = proto.Float64(score)
		result[index].RankScore = proto.Uint64(temp.RankScore)
		result[index].RankNum = proto.Uint64(rankNum)
		result[index].RankTimestamp = proto.Uint64(uint64(timestamp))
		result[index].NewRankTimestamp = proto.Int64(temp.Timestamp / 1000 / 1000)
	}

	return result, 1, nil
}

func esQueryRankListByUsers(index string, userIds []uint64) ([]*game_rank.RankListItem, error) {
	uids := make([]interface{}, 0, len(userIds))
	for _, v := range userIds {
		uids = append(uids, fmt.Sprintf("%d", v))
	}
	// build source body
	source := map[string]interface{}{
		"query": map[string]interface{}{
			"terms": map[string]interface{}{
				"userid": uids,
			},
		},
	}

	res, err := esutil.EsClient.Search(index).Source(source).Do(context.Background())
	if err != nil {
		gamelog.Error("esQueryRankListByUsers failed", map[string]interface{}{
			"index": index,
			"err":   err,
		})
		return nil, err
	}

	result := make([]*game_rank.RankListItem, len(res.Hits.Hits))
	for index, v := range res.Hits.Hits {
		item, err := fillEsItemV1(v.Source)
		if err != nil {
			continue
		}
		result[index] = item
	}

	return result, nil
}

func esQueryRankListV2ByUsers(index string, userIds []uint64) ([]*game_rank.RankListItem, error) {
	uids := make([]interface{}, 0, len(userIds))
	for _, v := range userIds {
		uids = append(uids, fmt.Sprintf("%d", v))
	}
	// build source body
	source := map[string]interface{}{
		"query": map[string]interface{}{
			"terms": map[string]interface{}{
				"user_id": uids,
			},
		},
	}

	res, err := esutil.EsClient.Search(index).Source(source).Do(context.Background())
	if err != nil {
		gamelog.Error("esQueryRankListByUsers failed", map[string]interface{}{
			"index": index,
			"err":   err,
		})
		return nil, err
	}

	result := make([]*game_rank.RankListItem, len(res.Hits.Hits))
	for index, v := range res.Hits.Hits {
		item, err := fillEsItemV2(v.Source)
		if err != nil {
			continue
		}
		result[index] = item
	}

	return result, nil
}

func fillEsItemV1(source *json.RawMessage) (*game_rank.RankListItem, error) {
	var temp EsUserCois
	if err := json.Unmarshal(*source, &temp); err != nil {
		gamelog.Error("json unmarshal es source err", map[string]interface{}{
			"message": *source,
			"err":     err,
		})
		return nil, err
	}
	item := &game_rank.RankListItem{}
	item.UserId = proto.Uint64(temp.UserId)
	item.UserName = proto.String(temp.UserName)
	score, timestamp := SplitFloat32AndTimestamp(float64(temp.RankScore))
	item.RankValue = proto.Float64(score)
	item.RankScore = proto.Uint64(temp.RankScore)
	item.RankTimestamp = proto.Uint64(uint64(timestamp))
	item.NewRankTimestamp = proto.Int64(temp.Timestamp / 1000 / 1000)
	return item, nil
}

func fillEsItemV2(source *json.RawMessage) (*game_rank.RankListItem, error) {
	var temp EsUserCoinsV2
	if err := json.Unmarshal(*source, &temp); err != nil {
		gamelog.Error("json unmarshal es source err", map[string]interface{}{
			"message": *source,
			"err":     err,
		})
		return nil, err
	}
	item := &game_rank.RankListItem{}
	item.UserId = proto.Uint64(temp.UserId)
	item.UserName = proto.String(temp.UserName)
	score, timestamp := SplitFloat32AndTimestamp(float64(temp.RankScore))
	item.RankValue = proto.Float64(score)
	item.RankScore = proto.Uint64(temp.RankScore)
	item.RankTimestamp = proto.Uint64(uint64(timestamp))
	item.NewRankTimestamp = proto.Int64(temp.Timestamp / 1000 / 1000)
	return item, nil
}

func getEsV2Index(scope, rankWeek, rankId string, eventID int64) string {
	env, region := env.GetEnv(), env.GetCID()
	if len(rankId) == 0 {
		rankId = strconv.FormatInt(eventID, 10)
	}
	return strings.ToLower(fmt.Sprintf(constant.WriteRankIndexFormatV2, scope, env, region, rankId, rankWeek))
}
