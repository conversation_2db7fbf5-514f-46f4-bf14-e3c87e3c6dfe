package handler

import (
	"git.garena.com/shopee/game_platform/rank_server/constant"
	"sync"

	"github.com/olivere/elastic"
)

type Pool struct {
	p *sync.Pool
}

// NewPool constructs a new Pool.
func NewPool(size int, operation string) Pool {
	switch operation {
	case constant.EsAdd:
		return NewAddPool(size)
	case constant.EsDelete:
		return NewDeletePool(size)
	default:
		return NewAddPool(size)
	}
}

func NewAddPool(size int) Pool {
	return Pool{p: &sync.Pool{
		New: func() interface{} {
			return &EsAddBulkBuffer{list: make([]elastic.BulkIndexRequest, 0, size)}
		},
	}}
}

func NewDeletePool(size int) Pool {
	return Pool{p: &sync.Pool{
		New: func() interface{} {
			return &EsDeleteBulkBuffer{list: make([]elastic.BulkDeleteRequest, 0, size)}
		},
	}}
}

// Get retrieves a Buffer from the pool, creating one if necessary.
func (p Pool) Get() EsBulkBuffer {
	buf := p.p.Get().(EsBulkBuffer)
	buf.Reset()
	return buf
}

func (p Pool) Put(buf EsBulkBuffer) {
	p.p.Put(buf)
}
