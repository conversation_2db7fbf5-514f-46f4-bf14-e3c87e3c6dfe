package handler

import (
	"context"
	"fmt"
	"git.garena.com/shopee/game_platform/comm_lib/esutil"
	"strings"
	"time"

	"git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/game_platform/comm_lib/env"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/rank_server/constant"
	"git.garena.com/shopee/game_platform/rank_server/semaphore"

	"github.com/olivere/elastic"
)

const (
	IndexClosedException = "index_closed_exception"
)

type RankEsWriter struct {
	esIndexFormat string
	esOperation   string

	rankEs<PERSON>han chan interface{}
	buffer     []interface{}
	bufferSize int

	bulkEsRequestDataPool Pool

	// convert func: es chan data -> es request
	convert EsChanDataConvert

	// monitor label
	monitorLabel string

	// Semaphore
	currentLimiter *semaphore.Semaphore
}

func (rsWriter *RankEsWriter) Init(options ...Options) {
	rsWriter.rankEsChan = make(chan interface{}, constant.DefaultRankEsChanCap)
	rsWriter.bufferSize = constant.DefaultBufferWriteSize
	rsWriter.buffer = make([]interface{}, 0, rsWriter.bufferSize)
	rsWriter.bulkEsRequestDataPool = NewPool(rsWriter.bufferSize, rsWriter.esOperation)

	rsWriter.currentLimiter = semaphore.NewSemaphore(constant.DefaultSemaphore)

	// for self-define values
	for _, opt := range options {
		opt(rsWriter)
	}

	// consumer chan
	go rsWriter.consumerFromEsChan()
}

// Write write is just for outside caller, eg: writer.Write(data)
func (rsWriter *RankEsWriter) Write(data interface{}) {
	rsWriter.produceToEsChan(data)
}

// add rank to chan
func (rsWriter *RankEsWriter) produceToEsChan(rank interface{}) {
	select {
	case rsWriter.rankEsChan <- rank:

	default:
		gamelog.Error("rankEsChan add rankings fail", map[string]interface{}{
			"rank": rank,
		})
		err := reporter.ReportCounter(
			METRIC_CHAN_FULL_TOTAL,
			1,
			reporter.Label{"type", "eschan"})
		HandlerReportErr(err, METRIC_CHAN_FULL_TOTAL)
	}
}

func (rsWriter *RankEsWriter) consumerFromEsChan() {
	defer func() {
		if r := recover(); r != nil {
			gamelog.Error("[panic recovery]consumerFromEsChan", gamelog.Fields{"r": r})
			go rsWriter.consumerFromEsChan()
		}
	}()
	loop := constant.ConsumerRetrys
	for {
		select {
		case rank := <-rsWriter.rankEsChan:
			gamelog.Debug("consumer rank chan", gamelog.Fields{"rank": rank})
			rsWriter.buffer = append(rsWriter.buffer, rank)
			ll := len(rsWriter.buffer)
			if ll > rsWriter.bufferSize {
				gamelog.Debug("consumer rank chan start sync out buffer", gamelog.Fields{"buffer size": ll})
				rsWriter.synWriteToEs()
			}

		default:
			if loop > 0 {
				loop--
				time.Sleep(50 * time.Millisecond)
			} else {
				loop = constant.ConsumerRetrys
				ll := len(rsWriter.buffer)
				if ll > 0 {
					gamelog.Debug("consumer rank chan start sync out timer", gamelog.Fields{"buffer size": ll})
					rsWriter.synWriteToEs()
				}
			}
		}
	}
}

func (rsWriter *RankEsWriter) synWriteToEs() {
	defer func() {
		rsWriter.buffer = rsWriter.buffer[:0]
	}()

	eachTemp := rsWriter.bulkEsRequestDataPool.Get()
	if eachTemp.Len() < len(rsWriter.buffer) {
		rsWriter.bulkEsRequestDataPool = NewPool(rsWriter.bufferSize, rsWriter.esOperation)
		eachTemp = rsWriter.bulkEsRequestDataPool.Get()
	}

	// build bulkRequest
	rsWriter.convert(eachTemp, rsWriter)

	// Semaphore acquire
	rsWriter.currentLimiter.Acquire()
	go rsWriter.sendBulkRequestV2(eachTemp, rsWriter.monitorLabel)
}

func (rsWriter *RankEsWriter) generateEsIndex(scope, rankName, yearAndWeek string) string {
	env, region := env.GetEnv(), env.GetCID()
	// make index lower for es
	return strings.ToLower(fmt.Sprintf(rsWriter.esIndexFormat, scope, env, region, rankName, yearAndWeek))
}

func (rsWriter *RankEsWriter) sendBulkRequestV2(buffer EsBulkBuffer, lable string) {
	defer func() {
		// put back to pool and release semaphore
		rsWriter.bulkEsRequestDataPool.Put(buffer)
		rsWriter.currentLimiter.Release()
		if r := recover(); r != nil {
			gamelog.Error("[panic recovery]synWriteToEs", gamelog.Fields{"r": r})
		}
	}()

	if buffer.Len() == 0 {
		gamelog.Info("temp data empty", gamelog.Fields{
			"lable": lable,
		})
		return
	}

	reportRes := "success"
	start := time.Now()
	for retry := 0; retry <= RetryTimes; retry++ {

		bulkReq := esutil.EsClient.Bulk()

		bulkReq.Add(buffer.List()...)

		res, err := bulkReq.Do(context.Background())

		if err != nil {
			gamelog.Error("es write fail,send bulk req err", map[string]interface{}{
				"res":  res,
				"err":  err,
				"size": buffer.Len(),
				"bulk": buffer.List(),
			})

			reportRes = "req_fail"

			return
		}

		// record bulk operation result if has some errors
		if res.Errors {
			gamelog.Error("es write fail,bulk res err", map[string]interface{}{
				"bulk_size":   len(res.Items),
				"failed_size": len(res.Failed()),
				"failed_info": res.Failed(),
				"retry":       retry,
				"maxRetry":    RetryTimes,
				"sleep":       WaitTime,
			})
			// 重新打开误关的retention类型的index
			reOpenClosedIndex(res.Items)
			if retry == RetryTimes {
				reportRes = "res_fail"
				break //reach max retry times
			}
			time.Sleep(WaitTime)
			continue //retry
		}
		break // success
	}

	err := reporter.ReportSummary(
		METRIC_CHAN_COMSUME_STATISTIC,
		float64(time.Since(start).Nanoseconds()/1000),
		reporter.Label{"type", lable},
		reporter.Label{"index", "0"},
		reporter.Label{"res", reportRes})
	HandlerReportErr(err, METRIC_CHAN_COMSUME_STATISTIC)

}

func getEsIndexInfo(index string) (elastic.CatIndicesResponse, error) {
	res, err := esutil.EsClient.CatIndices().Index(index).Columns("h", "s", "i", "creation.date").Do(context.Background())
	if err != nil {
		gamelog.Error("CatIndices failed", gamelog.Fields{"err": err, "index": index})
	}
	return res, err
}

func createNonLiveIndex(index string) error {
	if strings.ToLower(env.GetEnv()) == "live" {
		return nil
	}

	mapping := `{
	"settings":{
		"number_of_shards":1,
		"number_of_replicas":0
	}
}`
	exists, err := esutil.EsClient.IndexExists(index).Do(context.Background())
	if err != nil {
		gamelog.Error("esutil.EsClient.IndexExists failed", gamelog.Fields{"err": err, "index": index})
		return err
	}
	if exists {
		return nil
	}
	_, err = esutil.EsClient.CreateIndex(index).BodyString(mapping).Do(context.Background())
	if err != nil {
		gamelog.Error("esutil.EsClient.CreateIndex failed", gamelog.Fields{"err": err, "index": index})
		return err
	}
	return nil
}
func reOpenClosedIndex(items []map[string]*elastic.BulkResponseItem) {
	if items == nil || len(items) == 0 {
		return
	}
	openedIndexSet := make(map[string]interface{}, 0)
	for _, item := range items {
		if v, ok := item["index"]; ok {
			if v != nil && v.Error != nil && v.Error.Type == IndexClosedException {
				if _, exists := openedIndexSet[v.Index]; exists {
					continue
				}
				_, err := esutil.EsClient.OpenIndex(v.Index).Do(context.Background())
				if err != nil {
					gamelog.Error("open retention closed index failed", gamelog.Fields{"index": v.Index})
				} else {
					gamelog.Info("open retention closed index success", gamelog.Fields{"index": v.Index})
					openedIndexSet[v.Index] = struct{}{}
				}
			}
		}
	}

}
