package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/game_platform/comm_lib/esutil"
	"git.garena.com/shopee/game_platform/proto/game_rank"
	"github.com/golang/protobuf/proto"
	"io"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.garena.com/shopee/game_platform/comm_lib/env"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/rank_server/constant"

	"github.com/olivere/elastic"
)

func deleteRankAllDataAndIndex(scope, rankName, yearAndWeek string) {
	env, region := env.GetEnv(), env.GetCID()
	index := strings.ToLower(fmt.Sprintf(constant.WriteRankIndexFormatV2, scope, env, region, rankName, yearAndWeek))
	go func() {
		for {
			bulkRes, err := esutil.EsClient.DeleteByQuery(index).Query(elastic.MatchAllQuery{}).Do(context.Background())
			if err != nil {
				gamelog.Error("delete rank all data err", map[string]interface{}{
					"index": index,
					"err":   err,
				})
				break
			}

			if len(bulkRes.Failures) > 0 {
				gamelog.Error("delete rank all data failed, some data delete err", map[string]interface{}{
					"index":       index,
					"failed_size": len(bulkRes.Failures),
				})

				time.Sleep(50 * time.Millisecond)
			} else {
				break
			}
		}

		for {
			bulkRes, err := esutil.EsClient.DeleteIndex(index).Do(context.Background())
			if err != nil {
				gamelog.Error("delete rank index err", map[string]interface{}{
					"err": err,
				})
				break
			}
			if bulkRes.Acknowledged {
				break
			} else {
				time.Sleep(50 * time.Millisecond)
			}
		}
	}()
}

func rankList(eventId int32, offset, limit, userID uint64, userName string, rankValue float64, rankNum uint64, rankWeek, scope string) ([]*game_rank.RankListItem, uint64) {
	var result []*game_rank.RankListItem
	var totalNum uint64
	if rankNum > 0 {
		result, totalNum, _ = esQueryByRankNum(eventId, rankNum, rankWeek, scope)
		return result, totalNum
	}

	// All Query
	if userName == "" && userID == 0 && rankValue == -1 {
		if offset == 0 && limit == 0 {
			result, totalNum, _ = queryAllRankListByScroll(eventId, offset, 1000, rankWeek, scope)
			return result, totalNum
		}
		if offset > constant.EsScrollLimit {
			result, totalNum, _ = queryAllRankListByScroll(eventId, offset, 10, rankWeek, scope)
			return result, totalNum
		}
	}

	//  Conditional Query
	if offset > constant.EsScrollLimit {
		result, totalNum, _ = queryRankListByScroll(eventId, offset, userID, userName, rankValue, rankWeek, scope, 10)
		return result, totalNum
	}
	result, totalNum, _ = esRankList(eventId, offset, limit, userID, userName, rankValue, rankWeek, scope)
	return result, totalNum
}

func esQueryByRankNum(eventID int32, rankNum uint64, rankWeek, scope string) ([]*game_rank.RankListItem, uint64, error) {
	env, region := env.GetEnv(), env.GetCID()
	index := strings.ToLower(fmt.Sprintf(constant.WriteRankIndexFormatV2, scope, env, region, strconv.FormatInt(int64(eventID), 10), rankWeek))

	// build source body
	source := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []map[string]interface{}{
					0: {"term": map[string]interface{}{
						"rank_name": strconv.FormatInt(int64(eventID), 10)}},
					1: {"term": map[string]interface{}{
						"status": 1}},
				},
			},
		},
		"sort": map[string]interface{}{
			"rank_score": map[string]interface{}{
				"order": "desc"},
		},
		"from": rankNum - 1,
		"size": 1,
	}

	res, err := esutil.EsClient.Search(index).Source(source).Do(context.Background())
	if err != nil || len(res.Hits.Hits) != 1 {
		gamelog.Error("arcatch admin rank list: query list by rank num err", map[string]interface{}{
			"event_id":  eventID,
			"rank_num":  rankNum,
			"rank_week": rankWeek,
			"index":     index,
			"err":       err,
		})
		return nil, 1, nil
	}

	result := make([]*game_rank.RankListItem, len(res.Hits.Hits))
	for index, v := range res.Hits.Hits {
		var temp EsUserCoinsV2
		if err := json.Unmarshal(*v.Source, &temp); err != nil {
			gamelog.Error("json unmarshal es source err", map[string]interface{}{
				"event_id":  eventID,
				"rank_week": rankWeek,
				"index":     index,
				"err":       err,
			})
			continue
		}
		result[index] = &game_rank.RankListItem{}
		result[index].UserId = proto.Uint64(temp.UserId)
		result[index].UserName = proto.String(temp.UserName)
		score, timestamp := SplitFloat32AndTimestamp(float64(temp.RankScore))
		result[index].RankValue = proto.Float64(score)
		result[index].RankScore = proto.Uint64(temp.RankScore)
		result[index].RankNum = proto.Uint64(rankNum)
		result[index].RankTimestamp = proto.Uint64(uint64(timestamp))
		result[index].NewRankTimestamp = proto.Int64(temp.Timestamp)
	}

	return result, 1, nil
}

func esRankList(eventID int32, offset, limit, userId uint64, userName string, rankValue float64, rankWeek, scope string) ([]*game_rank.RankListItem, uint64, error) {
	env, region := env.GetEnv(), env.GetCID()
	esIndex := strings.ToLower(fmt.Sprintf(constant.WriteRankIndexFormatV2, scope, env, region, strconv.FormatInt(int64(eventID), 10), rankWeek))

	// build source body
	source := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []map[string]interface{}{
					0: {"term": map[string]interface{}{
						"rank_name": strconv.FormatInt(int64(eventID), 10)}},
					1: {"term": map[string]interface{}{
						"status": 1}},
				},
			},
		},
		"sort": map[string]interface{}{
			"rank_score": map[string]interface{}{
				"order": "desc"},
		},
		"from": offset,
		"size": limit,
	}

	must := source["query"].(map[string]interface{})["bool"].(map[string]interface{})["must"].([]map[string]interface{})
	if userId > 0 {
		must = append(must, map[string]interface{}{
			"term": map[string]interface{}{
				"user_id": userId,
			},
		})
	}

	if userName != "" {
		must = append(must, map[string]interface{}{
			"wildcard": map[string]interface{}{
				"user_name": "*" + strings.ToLower(userName) + "*",
			},
		})
	}

	// -1 means no rank value
	if rankValue > -1 {
		must = append(must, map[string]interface{}{
			"term": map[string]interface{}{
				"coins": rankValue,
			},
		})
	}

	source["query"].(map[string]interface{})["bool"].(map[string]interface{})["must"] = must
	res, err := esutil.EsClient.Search(esIndex).Source(source).Do(context.Background())
	if err != nil {
		gamelog.Error("arcatch admin rank list: query list by user id, name or coins err", map[string]interface{}{
			"event_id":  eventID,
			"rank_week": rankWeek,
			"index":     esIndex,
			"err":       err,
		})
		return nil, 0, nil
	}

	result := make([]*game_rank.RankListItem, len(res.Hits.Hits))
	var rankNum uint64
	for index, v := range res.Hits.Hits {
		var temp EsUserCoinsV2
		if err := json.Unmarshal(*v.Source, &temp); err != nil {
			gamelog.Error("json unmarshal es source err", map[string]interface{}{
				"event_id":  eventID,
				"rank_week": rankWeek,
				"index":     index,
				"err":       err,
			})
			continue
		}
		// query rank num
		rankNum = queryRankNumByRankScore(temp.RankScore, esIndex) + 1

		result[index] = &game_rank.RankListItem{}
		result[index].UserId = proto.Uint64(temp.UserId)
		result[index].UserName = proto.String(temp.UserName)
		score, timestamp := SplitFloat32AndTimestamp(float64(temp.RankScore))
		result[index].RankValue = proto.Float64(score)
		result[index].RankScore = proto.Uint64(temp.RankScore)
		result[index].RankNum = proto.Uint64(rankNum)
		result[index].RankTimestamp = proto.Uint64(uint64(timestamp))
		result[index].NewRankTimestamp = proto.Int64(temp.Timestamp)
	}

	return result, uint64(res.TotalHits()), nil
}

func queryRankNumByRankScore(rankScore uint64, index string) uint64 {
	res, err := esutil.EsClient.Search(index).Query(elastic.NewRangeQuery("rank_score").Gt(rankScore)).Do(context.Background())
	if err != nil {
		gamelog.Error("query rank num err", map[string]interface{}{
			"index":      index,
			"rank_score": rankScore,
			"err":        err,
		})
	}

	return uint64(res.TotalHits())
}

func queryRankNumByRankScoreV2(rankScore, uid uint64, index string) uint64 {
	// build source body
	source := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"should": []map[string]interface{}{
					{
						"bool": map[string]interface{}{
							"must": []map[string]interface{}{
								{
									"range": map[string]interface{}{
										"rank_score": map[string]interface{}{
											"gt": rankScore,
										},
									},
								},
							},
						},
					},
					{
						"bool": map[string]interface{}{
							"must": []map[string]interface{}{
								{
									"match": map[string]interface{}{
										"rank_score": rankScore,
									},
								},
								{
									"range": map[string]interface{}{
										"user_id": map[string]interface{}{
											"lt": uid,
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	res, err := esutil.EsClient.Search(index).Source(source).Do(context.Background())
	if err != nil {
		gamelog.Error("query rank num err", map[string]interface{}{
			"index":      index,
			"rank_score": rankScore,
			"uid":        uid,
			"err":        err,
		})
		return 0
	}

	return uint64(res.TotalHits())
}

func queryAllRankListByScroll(eventID int32, offset uint64, scrollStep int, rankWeek, scope string) ([]*game_rank.RankListItem, uint64, error) {
	env, region := env.GetEnv(), env.GetCID()
	esIndex := strings.ToLower(fmt.Sprintf(constant.WriteRankIndexFormatV2, scope, env, region, strconv.FormatInt(int64(eventID), 10), rankWeek))

	var result []*game_rank.RankListItem
	res, err := esutil.EsClient.CatCount().Index(esIndex).Do(context.Background())
	if err != nil || len(res) != 1 {
		gamelog.Error("cat count for index err", map[string]interface{}{
			"index": esIndex,
			"err":   err,
		})
		return nil, 0, nil
	}

	if offset == 0 {
		result = make([]*game_rank.RankListItem, 0, res[0].Count)
	} else {
		result = make([]*game_rank.RankListItem, 0, 10)
	}

	group := sync.WaitGroup{}

	rankNum := uint64(1)
	// goroutine sends individual hits to channel.
	type EsJson struct {
		message json.RawMessage
		rankNum uint64
	}
	hits := make(chan *EsJson, 10240)

	group.Add(1)
	go func(ctx context.Context) error {
		defer func() {
			close(hits)
			group.Done()
		}()
		// Initialize scroller. Just don't call Do yet.
		scroll := esutil.EsClient.Scroll(esIndex).Type("default").Sort("rank_score", false).Size(scrollStep)
		for offset == 0 || rankNum-1 <= offset {
			results, err := scroll.Do(context.Background())
			if err == io.EOF {
				return nil // all results retrieved
			}
			if err != nil {
				return err // something went wrong
			}

			// Send the hits to the hits channel
			for _, hit := range results.Hits.Hits {
				if rankNum-1 >= offset {
					select {
					case hits <- &EsJson{*hit.Source, rankNum}:
					case <-ctx.Done():
						return ctx.Err()
					}
				}

				rankNum++
			}
		}
		return nil
	}(context.Background())

	// goroutine receives hits and deserializes them.
	//
	// If you want, setup a number of goroutines handling deserialization in parallel.
	group.Add(10)
	listLock := sync.Mutex{}
	for i := 0; i < 10; i++ {
		go func(ctx context.Context) error {
			defer group.Done()

			for hit := range hits {
				// Deserialize
				var temp EsUserCoinsV2
				err := json.Unmarshal(hit.message, &temp)
				if err != nil {
					return err
				}

				// Do something with the product here, e.g. send it to another channel
				// for further processing.
				score, timestamp := SplitFloat32AndTimestamp(float64(temp.RankScore))
				rankListItem := &game_rank.RankListItem{
					UserId:        proto.Uint64(temp.UserId),
					UserName:      proto.String(temp.UserName),
					RankValue:     proto.Float64(score),
					RankScore:     proto.Uint64(temp.RankScore),
					RankNum:       proto.Uint64(hit.rankNum),
					RankTimestamp: proto.Uint64(uint64(timestamp)),
					NewRankTimestamp: proto.Int64(temp.Timestamp),
				}
				listLock.Lock()
				result = insertSort(result, rankListItem)
				listLock.Unlock()

				// Terminate early?
				select {
				default:
				case <-ctx.Done():
					return ctx.Err()
				}
			}
			return nil
		}(context.Background())
	}

	// Wait goroutines end.
	group.Wait()

	return result, uint64(res[0].Count), nil
}

func queryRankListByScroll(eventID int32, offset, userId uint64, userName string, rankValue float64, rankWeek, scope string, scrollStep int) ([]*game_rank.RankListItem, uint64, error) {
	env, region := env.GetEnv(), env.GetCID()
	esIndex := strings.ToLower(fmt.Sprintf(constant.WriteRankIndexFormatV2, scope, env, region, strconv.FormatInt(int64(eventID), 10), rankWeek))

	// build source body
	source := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []map[string]interface{}{
					0: {"term": map[string]interface{}{
						"rank_name": strconv.FormatInt(int64(eventID), 10)}},
					1: {"term": map[string]interface{}{
						"status": 1}},
				},
			},
		},
		"sort": map[string]interface{}{
			"rank_score": map[string]interface{}{
				"order": "desc"},
		},
	}

	must := source["query"].(map[string]interface{})["bool"].(map[string]interface{})["must"].([]map[string]interface{})
	if userId > 0 {
		must = append(must, map[string]interface{}{
			"term": map[string]interface{}{
				"user_id": userId,
			},
		})
	}

	if userName != "" {
		must = append(must, map[string]interface{}{
			"wildcard": map[string]interface{}{
				"user_name": "*" + strings.ToLower(userName) + "*",
			},
		})
	}

	// -1 means no rank value
	if rankValue > -1 {
		must = append(must, map[string]interface{}{
			"term": map[string]interface{}{
				"coins": rankValue,
			},
		})
	}

	source["query"].(map[string]interface{})["bool"].(map[string]interface{})["must"] = must

	res, err := esutil.EsClient.Search(esIndex).Source(source).Do(context.Background())
	if err != nil {
		gamelog.Error("arcatch admin rank list: query list by user id, name or coins err", map[string]interface{}{
			"event_id":  eventID,
			"rank_week": rankWeek,
			"index":     esIndex,
			"err":       err,
		})
		return nil, 0, nil
	}

	// begin scroll query
	result := make([]*game_rank.RankListItem, 0, 10)

	num := uint64(0)
	// Initialize scroller. Just don't call Do yet.
	scroll := esutil.EsClient.Scroll(esIndex).Type("default").Body(source).Size(scrollStep)
	for num <= offset {
		results, err := scroll.Do(context.Background())
		if err == io.EOF {
			return nil, 0, nil // all results retrieved
		}
		if err != nil {
			return nil, 0, err // something went wrong
		}

		// handle result hits
		for _, hit := range results.Hits.Hits {
			if num >= offset {
				// Deserialize
				var temp EsUserCoinsV2
				err := json.Unmarshal(*hit.Source, &temp)
				if err != nil {
					gamelog.Error("parse json to EsUserCoinsV2 failed", gamelog.Fields{
						"json": *hit.Source,
						"err":  err,
					})
					continue
				}
				rankNum := queryRankNumByRankScore(temp.RankScore, esIndex)
				// add to response rank list
				score, _ := SplitFloat32AndTimestamp(float64(temp.RankScore))
				rankListItem := &game_rank.RankListItem{
					UserId:    proto.Uint64(temp.UserId),
					UserName:  proto.String(temp.UserName),
					RankValue: proto.Float64(score),
					RankScore: proto.Uint64(temp.RankScore),
					RankNum:   proto.Uint64(rankNum),
					NewRankTimestamp: proto.Int64(temp.Timestamp),
				}
				result = insertSort(result, rankListItem)
			}

			num++
		}
	}

	return result, uint64(res.TotalHits()), nil
}

func insertSort(result []*game_rank.RankListItem, element *game_rank.RankListItem) []*game_rank.RankListItem {
	if len(result) == 0 {
		result = append(result, element)
		return result
	}
	l := len(result) - 1
	for l >= 0 {
		if *result[l].RankNum > *element.RankNum {
			l--
			continue
		} else {
			result = sliceBackMove(result, l+1)
			result[l+1] = element
			return result
		}
	}

	if l < 0 {
		result = sliceBackMove(result, 0)
		result[0] = element
	}
	return result
}

func sliceBackMove(s []*game_rank.RankListItem, from int) []*game_rank.RankListItem {
	i := len(s) - 1
	s = append(s, s[i])
	for i >= from {
		s[i+1] = s[i]
		i--
	}

	return s
}
