package handler

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/proto/gameplatform_game"
	"git.garena.com/shopee/game_platform/rank_server/dao/cache"
	"github.com/golang/protobuf/proto"
)

const BatchLimit = 2000
const lockCleanRankJob = "LockTopNClean.%s"

func BatchGetBlacklistUserIdMap(ctx context.Context, scope string, users []uint64) map[uint64]struct{} {
	m := make(map[uint64]struct{}, 0)
	length := len(users)

	if length == 0 || len(scope) == 0 {
		return m
	}

	count := length / BatchLimit
	if length%BatchLimit != 0 {
		count++
	}

	cli := gameplatform_game.GetGrpcClient()

	for i := 0; i < count; i++ {
		begin := i * BatchLimit
		end := (i + 1) * BatchLimit

		if end > length {
			end = length
		}

		req := &gameplatform_game.GetBatchBlacklistRequest{
			AppId:  proto.String(scope),
			IdList: convertInt64(users)[begin:end],
		}

		var resp *gameplatform_game.GetBatchBlacklistResponse
		var err error

		newCtx, cancel := context.WithTimeout(ctx, 200*time.Millisecond)
		defer cancel()

		resp, err = cli.GetBatchBlacklist(newCtx, req)
		gamelog.Info("debug info GetBatchBlacklist", gamelog.Fields{"req": req, "resp": resp, "err": err})

		if err != nil {
			gamelog.ErrorWithContext(ctx, "GetBatchBlacklist error", gamelog.Fields{"err": err, "req": req})
			return m
		}

		if len(resp.GetBlackUserIdList()) != 0 {
			gamelog.InfoWithContext(ctx, "BatchGetBlacklistUserId result", gamelog.Fields{"arr": resp.GetBlackUserIdList(), "scope": scope})

			for _, u := range resp.GetBlackUserIdList() {
				m[uint64(u)] = struct{}{}
			}
		}
	}

	return m
}

func convertInt64(list []uint64) []int64 {
	result := make([]int64, 0, len(list))
	for _, id := range list {
		result = append(result, int64(id))
	}

	return result
}

func doCleanRankJob(ctx context.Context, scopeRankName string, bucketSize int, scope string) {
	expire := int64(30 * 60)
	lockKey := fmt.Sprintf(lockCleanRankJob, scopeRankName)

	isOK, err := cache.RedisUtilSetNX(ctx, lockKey, 1, time.Duration(expire)*time.Second)
	defer cache.RedisUtilDelete(ctx, lockKey)

	if err != nil || !isOK {
		gamelog.InfoWithContext(ctx, "doCleanRankJob failed, lock failed", gamelog.Fields{
			"error":         err,
			"lockKey":       lockKey,
			"scopeRankName": scopeRankName,
		})
		return
	} else {
		gamelog.InfoWithContext(ctx, "doCleanRankJob start, lock success", gamelog.Fields{"scopeRankName": scopeRankName, "lockKey": lockKey})
	}

	keyList := make([]string, 0)
	if bucketSize == 1 {
		keyList = append(keyList, rankingKey(scopeRankName, nobucket))
	} else {
		for i := 0; i < bucketSize; i++ {
			keyList = append(keyList, rankingKey(scopeRankName, strconv.FormatInt(int64(i), 10)))
		}
	}

	gamelog.InfoWithContext(ctx, "doCleanRankJob keys detail", gamelog.Fields{"keylist": keyList})

	cleaned := 0
	var card uint64
	for _, k := range keyList {
		c1, c2 := cleanRankByKey(ctx, k, scope)
		card += c1
		cleaned += c2
	}

	gamelog.InfoWithContext(ctx, "doCleanRankJob finish", gamelog.Fields{"scopeRankName": scopeRankName, "cleanedLen": cleaned, "card": card})
}

// cleanRankByKey return clean success size
func cleanRankByKey(ctx context.Context, key, scope string) (uint64, int) {
	card, _ := cache.RedisUtilZCard(ctx, key)

	gamelog.InfoWithContext(ctx, "start to cleanRankBy key", gamelog.Fields{"key": key, "card": card})

	cleaned := 0

	// 每次扫描BatchLimit条，这一批数据中的黑名单组成一个切片
	// 扫描N次就是二维切片
	toCleanUid := make([][]uint64, 0)

	// redis zrange 左闭右闭
	begin := int64(0)
	end := int64(BatchLimit) - 1

	for {
		res, err := cache.RedisUtilZRange(ctx, key, begin, end)
		if err != nil {
			gamelog.ErrorWithContext(ctx, "redisutil zrange error", gamelog.Fields{"err": err})
			return card, cleaned
		}

		begin += BatchLimit
		end += BatchLimit

		if len(res) == 0 {
			break
		}

		uids := transZElem2UserIds(res)
		blackMap := BatchGetBlacklistUserIdMap(ctx, scope, uids)

		if len(blackMap) != 0 {
			arr := make([]uint64, 0)
			for k := range blackMap {
				arr = append(arr, k)
			}

			toCleanUid = append(toCleanUid, arr)
		}
	}

	for _, arr := range toCleanUid {
		brr := make([]interface{}, 0)
		for _, u := range arr {
			brr = append(brr, strconv.FormatUint(u, 10))
		}

		r, e := cache.RedisUtilBatchZRem(ctx, key, brr)
		if e != nil {
			gamelog.ErrorWithContext(ctx, "redisutil ZremBatch error", gamelog.Fields{"err": e, "toCleanUid": toCleanUid, "scope": scope})
		}

		gamelog.InfoWithContext(ctx, "redisutil ZremBatch success", gamelog.Fields{"result": r, "toCleanUid": toCleanUid, "scope": scope})
		cleaned += int(r)
	}

	return card, cleaned
}

func cleanBlackResult(list []cache.ZsetElem, blackMap map[uint64]struct{}) []cache.ZsetElem {
	res := make([]cache.ZsetElem, 0, cap(list))

	for _, elem := range list {
		id, err := strconv.ParseUint(elem.Value, 10, 64)
		if err != nil {
			gamelog.Error("transZElem2Map parse userid failed", gamelog.Fields{"err": err, "value": elem.Value})
			continue
		}

		if _, ok := blackMap[id]; !ok {
			res = append(res, elem)
		}
	}

	return res
}
