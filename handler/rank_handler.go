package handler

import (
	"context"
	"git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/comm_lib/utils"
	"git.garena.com/shopee/game_platform/proto/game_rank"
	rankConfig "git.garena.com/shopee/game_platform/rank_server/config"
	"git.garena.com/shopee/game_platform/rank_server/constant"
	"git.garena.com/shopee/game_platform/rank_server/dao/cache"
	spiCache "git.garena.com/shopee/mts/go-application-server/spi/cache"
	"github.com/golang/protobuf/proto"
	"sort"
	"strconv"
	"time"
)

// RankHandler implements all rpc
type RankHandler struct {
}

// SetScore async set member score
func (s *RankHandler) SetScore(ctx context.Context, req *game_rank.SetScoreRequest, rsp *game_rank.CommonResponse) error {
	return nil
}

// IncrScore async add member score
func (s *RankHandler) IncrScore(ctx context.Context, req *game_rank.IncrScoreRequest, rsp *game_rank.CommonResponse) error {
	// implement here
	timestamp, err := utils.GetTimeStampMilli()
	if err != nil {
		rsp.ErrCode = proto.Int32(100)
		rsp.ErrMsg = proto.String("server get timestamp error")
		return nil
	}
	RankWriter.putRankChan(
		Rankings{
			Scope:         req.GetScope(),
			RankName:      req.GetRankName(),
			Username:      req.GetUserName(),
			Userid:        req.GetUserId(),
			Score:         req.GetScore(),
			Timestamp:     timestamp,
			RankType:      constant.RankTypeWeek,
			IsSetScoreReq: false,
		})
	//RankWriter.putInfoChan(userinfo{req.GetUserName(), req.GetUserId(), 0})
	rsp.ErrCode = proto.Int32(0)
	rsp.ErrMsg = proto.String("ok")
	return nil
}

// Topn return topn users
func (s *RankHandler) Topn(ctx context.Context, req *game_rank.TopnRequest, rsp *game_rank.TopnResponse) error {
	// implement here
	var res []cache.ZsetElem
	var err error

	defer func() {
		topXItems := []*game_rank.RankItem{}
		for index, tmpItem := range rsp.GetItems() {
			if index > 100 {
				break
			}
			topXItems = append(topXItems, tmpItem)
		}

		gamelog.InfoWithContext(ctx, "Topn Access", gamelog.Fields{
			"req":       req,
			"resp.len":  len(rsp.GetItems()),
			"topXItems": topXItems,
			"err":       err,
		})
	}()

	if req.GetN() > MaxUserRank+1 && rankConfig.IsTopNReturnErr() {
		nString := strconv.FormatInt(int64(req.GetN()), 10)
		_ = reporter.ReportCounter(METRIC_API_N_TOO_BIG, 1, reporter.Label{"n", nString})

		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(100),
			ErrMsg:  proto.String("N > 10000"),
		}
		return nil
	}

	if IsUseTop10kKey(constant.RankTypeWeek) {
		res, err = QueryTopNFromSingleKey(ctx, req.GetScope(), req.GetRankName(), req.GetN(), constant.RankTypeWeek, req.GetReverse(), true)
	} else {
		nameWithoutBucket := scopeRankName(req.GetScope(), req.GetRankName())
		res, err = allbucketZSetRange(nameWithoutBucket, req.GetN(), req.GetReverse(), conf.rankRedisBucket, true, true)
	}

	if err != nil {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(100),
			ErrMsg:  proto.String(err.Error()),
		}
		return nil
	}

	rsp.Resp = &game_rank.CommonResponse{
		ErrCode: proto.Int32(0),
		ErrMsg:  proto.String("ok"),
	}

	uids := transZElem2UserIds(res)
	mapUserInfo, err := batchGetUserBaseInfo(ctx, uids)
	if err != nil {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(100),
			ErrMsg:  proto.String(err.Error()),
		}
		return nil
	}

	rsp.Items = []*game_rank.RankItem{}

	// TODO 是否使用fail fast，还是处理失败的数据返回空
	for i, _ := range res {

		if res[i].Value == "" {
			continue
		}

		userId, err := strconv.ParseUint(res[i].Value, 10, 64)
		if err != nil {
			rsp.Resp = &game_rank.CommonResponse{
				ErrCode: proto.Int32(100),
				ErrMsg:  proto.String(err.Error()),
			}
			return nil
		}
		userName := ""
		avatar := ""
		user, ok := mapUserInfo[userId]
		if !ok || user == nil {
			//gamelog.Info("username not found", gamelog.Fields{"uid": userId})
		} else {
			userName = user.GetUserName()
			avatar = user.GetPortrait()
		}

		score, _ := SplitFloat32AndTimestamp(res[i].Score)

		rsp.Items = append(rsp.Items, &game_rank.RankItem{
			Score:    proto.Float64(score),
			UserName: proto.String(userName),
			UserId:   proto.Uint64(userId),
			Avatar:   proto.String(avatar),
		})
	}
	rsp.Items = rsp.Items[:len(rsp.Items)]

	return nil
}

// DownLoadTopn return topn users
func (s *RankHandler) DownLoadTopn(ctx context.Context, req *game_rank.TopnRequest, rsp *game_rank.TopnResponse) error {
	// implement here
	var res []cache.ZsetElem
	var err error
	if IsUseTop10kKey(constant.RankTypeWeek) {
		res, err = QueryTopNFromSingleKey(ctx, req.GetScope(), req.GetRankName(), req.GetN(), constant.RankTypeWeek, req.GetReverse(), true)
	} else {
		nameWithoutBucket := scopeRankName(req.GetScope(), req.GetRankName())
		res, err = allbucketZSetRange(nameWithoutBucket, req.GetN(), req.GetReverse(), conf.rankRedisBucket, false, true)
	}
	if err != nil {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(100),
			ErrMsg:  proto.String(err.Error()),
		}
		return nil
	}

	rsp.Resp = &game_rank.CommonResponse{
		ErrCode: proto.Int32(0),
		ErrMsg:  proto.String("ok"),
	}

	uids := transZElem2UserIds(res)
	mapUserInfo, err := batchGetUserBaseInfo(ctx, uids)
	if err != nil {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(100),
			ErrMsg:  proto.String(err.Error()),
		}
		return nil
	}
	rsp.Items = []*game_rank.RankItem{}
	// TODO 是否使用fail fast，还是处理失败的数据返回空
	for i, _ := range res {

		if res[i].Value == "" {
			continue
		}

		userId, err := strconv.ParseUint(res[i].Value, 10, 64)
		if err != nil {
			rsp.Resp = &game_rank.CommonResponse{
				ErrCode: proto.Int32(100),
				ErrMsg:  proto.String(err.Error()),
			}
			return nil
		}

		userName := ""
		avatar := ""
		user, ok := mapUserInfo[userId]
		if !ok || user == nil {
			//gamelog.Info("username not found", gamelog.Fields{"uid": userId})
		} else {
			userName = user.GetUserName()
			avatar = user.GetPortrait()
		}

		score, weekTimestamp := SplitFloat32AndTimestamp(res[i].Score)

		rsp.Items = append(rsp.Items, &game_rank.RankItem{
			Score:         proto.Float64(score),
			UserName:      proto.String(userName),
			UserId:        proto.Uint64(userId),
			WeekTimeStamp: proto.Uint64(uint64(weekTimestamp)),
			Avatar:        proto.String(avatar),
		})
	}
	rsp.Items = rsp.Items[:len(rsp.Items)]
	return nil
}

func allbucketZSetRange(name string, topn int32, reverse bool, bucketSize int, useCache bool, needCut bool) (res []cache.ZsetElem, err error) {
	if bucketSize == 1 {
		rankKey := rankingKey(name, nobucket)
		temp, err := zsetRange(rankKey, topn, reverse, useCache)
		if err != nil {
			return []cache.ZsetElem{}, err
		}
		res = append(res, temp...)
	} else {
		for i := 0; i < bucketSize; i++ {
			rankKey := rankingKey(name, strconv.FormatInt(int64(i), 10))
			temp, err := zsetRange(rankKey, topn, reverse, useCache)
			if err != nil {
				return []cache.ZsetElem{}, err
			}
			res = append(res, temp...)
		}
	}

	var wrapper ZsetElemSortWrapper
	if reverse {
		wrapper = ZsetElemSortWrapper{res[:len(res)], OrderbyAsc}
	} else {
		wrapper = ZsetElemSortWrapper{res[:len(res)], Orderby}
	}

	sort.Sort(wrapper)

	if !needCut {
		return wrapper.Data, nil
	}

	if topn > int32(len(wrapper.Data)) {
		return wrapper.Data, nil
	} else {
		return wrapper.Data[:topn], nil
	}
}

func zsetRange(rankKey string, topn int32, reverse, useCache bool) (res []cache.ZsetElem, err error) {
	// reverse 默认是false 从高到低排序
	if useCache {
		if reverse {
			res, err = cache.GetCachedZRange(context.Background(), rankKey, 0, int64(topn-1), time.Second)
		} else {
			res, err = cache.GetCachedZRevRange(context.Background(), rankKey, 0, int64(topn-1), time.Second)
		}
	} else {
		if reverse {
			res, err = cache.RedisUtilZRange(context.Background(), rankKey, 0, int64(topn-1))
		} else {
			res, err = cache.RedisUtilZRevRange(context.Background(), rankKey, 0, int64(topn-1))
		}
	}

	if err != nil {
		gamelog.Error("redis GetCachedZsetRevRange call error", map[string]interface{}{
			"rankKey": rankKey,
			"topn":    topn,
			"reverse": reverse,
			"err":     err,
		})
		return
	}

	return
}

// UserRank get rank for a member
func (s *RankHandler) UserRank(ctx context.Context, req *game_rank.UserRankRequest, rsp *game_rank.UserRankResponse) error {
	if IsUseTop10kKey(constant.RankTypeWeek) {
		rank, err := QueryUserRankV2(ctx, req.GetUserId(), req.GetScope(), req.GetRankName(), constant.RankTypeWeek, req.GetReverse())
		if err == spiCache.RedisNil {
			rsp.Resp = &game_rank.CommonResponse{
				ErrCode: proto.Int32(101),
				ErrMsg:  proto.String("not found"),
			}
			return nil
		}

		if err != nil {
			rsp.Resp = &game_rank.CommonResponse{
				ErrCode: proto.Int32(100),
				ErrMsg:  proto.String(err.Error()),
			}
			return nil
		}
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(0),
			ErrMsg:  proto.String("ok"),
		}
		rsp.Rank = proto.Int32(int32(rank))
		return nil
	}

	// implement here
	key, name := userIdHashBucketRank(req.GetScope(), req.GetRankName(), req.GetUserId(), conf.rankRedisBucket)

	var rank int64
	var err error
	member := strconv.FormatUint(req.GetUserId(), 10)

	//get rank
	if req.GetReverse() {
		rank, err = cache.RedisUtilZRank(ctx, key, member)
	} else {
		rank, err = cache.RedisUtilZRevRank(ctx, key, member)
	}
	if err == spiCache.RedisNil {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(101),
			ErrMsg:  proto.String("not found"),
		}
		return nil
	}

	if err != nil {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(100),
			ErrMsg:  proto.String(err.Error()),
		}
		return nil
	}

	if rank < UserRankThreshold {
		//preciseRank
		res, err := allbucketZSetRange(name, TopNMaxRank, req.GetReverse(), conf.rankRedisBucket, true, true)
		if err != nil {
			rsp.Resp = &game_rank.CommonResponse{
				ErrCode: proto.Int32(100),
				ErrMsg:  proto.String(err.Error()),
			}
			return nil
		}

		//get score
		redisScore, err := cache.RedisUtilZScore(ctx, key, member)
		if err == spiCache.RedisNil {
			rsp.Resp = &game_rank.CommonResponse{
				ErrCode: proto.Int32(101),
				ErrMsg:  proto.String("not found"),
			}
			return nil
		}

		if err != nil {
			rsp.Resp = &game_rank.CommonResponse{
				ErrCode: proto.Int32(100),
				ErrMsg:  proto.String(err.Error()),
			}
			return nil
		}

		last := res[len(res)-1]

		if (redisScore < last.Score && !req.GetReverse()) || (redisScore > last.Score && req.GetReverse()) {
			// score not in TopNMaxRank, degrade to roughlyRank
			rank = rank * int64(conf.rankRedisBucket)
			if rank <= TopNMaxRank {
				rank = TopNMaxRank + 1
			}
		} else {
			if req.GetReverse() {
				for index, _ := range res {
					//改为精确匹配
					//if res[index].Score >= redisScore {
					if res[index].Value == member {
						rank = int64(index)
						break
					}
				}
			} else {
				for index, _ := range res {
					if res[index].Value == member {
						rank = int64(index)
						break
					}
				}
			}
			// todo what if not equal
			//if res[rank].Score != score{
			//
			//}
			rank = rank + 1
		}

	} else {
		// roughlyRank
		rank = rank * int64(conf.rankRedisBucket)
	}

	// 游戏默认排名
	if rank > MaxUserRank {
		rank = MaxUserRank
	}

	rsp.Resp = &game_rank.CommonResponse{
		ErrCode: proto.Int32(0),
		ErrMsg:  proto.String("ok"),
	}
	rsp.Rank = proto.Int32(int32(rank))
	return nil
}

// UserScore get score for a member
func (s *RankHandler) UserScore(ctx context.Context, req *game_rank.UserScoreRequest, rsp *game_rank.UserScoreResponse) error {
	// implement here
	var redisScore float64
	var err error

	key, _ := userIdHashBucketRank(req.GetScope(), req.GetRankName(), req.GetUserId(), conf.rankRedisBucket)
	member := strconv.FormatUint(req.GetUserId(), 10)

	redisScore, err = cache.RedisUtilZScore(ctx, key, member)

	if err == spiCache.RedisNil {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(101),
			ErrMsg:  proto.String("not found"),
		}
		return nil
	}

	if err != nil {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(100),
			ErrMsg:  proto.String(err.Error()),
		}
		return nil
	}

	rsp.Resp = &game_rank.CommonResponse{
		ErrCode: proto.Int32(0),
		ErrMsg:  proto.String("ok"),
	}
	score, _ := SplitFloat32AndTimestamp(redisScore)
	rsp.Score = proto.Float64(score)
	return nil
}

// DeleteUserRank delete User score
func (s *RankHandler) DeleteUserRank(ctx context.Context, req *game_rank.DeleteUserRankRequest, rsp *game_rank.CommonResponse) error {
	// implement here
	key, _ := userIdHashBucketRank(req.GetScope(), req.GetRankName(), req.GetUserId(), conf.rankRedisBucket)
	member := strconv.FormatUint(req.GetUserId(), 10)

	find := false
	zscore, err := cache.RedisUtilZScore(ctx, key, member)
	if err != nil && err != spiCache.RedisNil {
		gamelog.Error("zscore failed", gamelog.Fields{"err": err, "uid": member, "key": key})
		return err
	}
	if err == nil {
		find = true
		gamelog.Info("[v1]zscore success", gamelog.Fields{"req": req, "score": zscore})
	}

	_, err = cache.RedisUtilBatchZRem(ctx, key, member)

	if err != nil && err != spiCache.RedisNil {
		rsp.ErrCode = proto.Int32(100)
		rsp.ErrMsg = proto.String(err.Error())
		return nil
	}

	// 双删新Key
	DeleteUserFromSingleKey(req.GetUserId(), constant.RankTypeWeek, req.GetScope(), req.GetRankName(), false)

	if err := SaveUserRecoveryData(req.GetScope(), req.GetRankName(),
		req.GetRankWeek(), "", constant.RankTypeWeek, req.GetUserId(), uint64(zscore), find == false); err != nil {
		gamelog.Error("SaveUserRecoveryData failed", gamelog.Fields{"err": err, "req": req})
	}
	rsp.ErrCode = proto.Int32(0)
	rsp.ErrMsg = proto.String("ok")
	return nil
}

// DeleteRank delete rank
func (s *RankHandler) DeleteRank(ctx context.Context, req *game_rank.DeleteRankRequest, rsp *game_rank.CommonResponse) error {
	// implement here
	nameWithoutBucket := scopeRankName(req.GetScope(), req.GetRankName())
	if conf.rankRedisBucket <= 1 {
		rankKey := rankingKey(nameWithoutBucket, nobucket)
		_, err := cache.RedisUtilDelete(ctx, rankKey)
		if err != nil && err != spiCache.RedisNil {
			rsp.ErrCode = proto.Int32(100)
			rsp.ErrMsg = proto.String(err.Error())
			return nil
		}
	} else {
		for i := 0; i < conf.rankRedisBucket; i++ {
			rankKey := rankingKey(nameWithoutBucket, strconv.FormatInt(int64(i), 10))
			_, err := cache.RedisUtilDelete(ctx, rankKey)
			if err != nil && err != spiCache.RedisNil {
				rsp.ErrCode = proto.Int32(100)
				rsp.ErrMsg = proto.String(err.Error())
				return nil
			}
		}
	}

	// 双删新Key
	DeleteUserFromSingleKey(0, constant.RankTypeWeek, req.GetScope(), req.GetRankName(), true)

	rsp.ErrCode = proto.Int32(0)
	rsp.ErrMsg = proto.String("ok")
	return nil
}

func (s *RankHandler) UserScoreList(ctx context.Context, req *game_rank.UserScoreListRequest, rsp *game_rank.UserScoreListResponse) error {
	// implement here
	nameWithoutBucket := scopeRankName(req.GetScope(), req.GetRankName())
	var scores []*game_rank.RankItem
	for _, v := range req.GetUserIds() {
		item := game_rank.RankItem{}
		rankKey := rankingKey(nameWithoutBucket, userIdHashBucket(v, conf.rankRedisBucket))
		member := strconv.FormatUint(v, 10)
		redisScore, err := cache.RedisUtilZScore(ctx, rankKey, member)
		if err != nil && err != spiCache.RedisNil {
			rsp.Resp = &game_rank.CommonResponse{
				ErrCode: proto.Int32(100),
				ErrMsg:  proto.String(err.Error()),
			}
			rsp.Items = scores
			return nil
		}

		if err == spiCache.RedisNil {
			continue
		} else {
			score, _ := SplitFloat32AndTimestamp(redisScore)
			item.Score = proto.Float64(score)
		}
		item.UserId = proto.Uint64(v)
		scores = append(scores, &item)
	}

	mapUserInfo, err := batchGetUserBaseInfo(ctx, req.GetUserIds())
	if err != nil {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(100),
			ErrMsg:  proto.String(err.Error()),
		}
		return nil
	}

	if req.GetDetail() {
		for i, _ := range scores {
			userId := scores[i].GetUserId()

			user, ok := mapUserInfo[userId]
			if !ok || user == nil {
				//gamelog.Info("username not found", gamelog.Fields{"uid": userId})
			} else {
				scores[i].UserName = proto.String(user.GetUserName())
				scores[i].Avatar = proto.String(user.GetPortrait())
			}
		}
	}
	rsp.Resp = &game_rank.CommonResponse{
		ErrCode: proto.Int32(0),
		ErrMsg:  proto.String("ok"),
	}
	rsp.Items = scores
	return nil
}
