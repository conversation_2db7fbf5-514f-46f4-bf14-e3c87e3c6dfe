package handler

import (
	"errors"
	"fmt"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/comm_lib/utils"
	"git.garena.com/shopee/game_platform/rank_server/constant"
	"github.com/golang/protobuf/proto"
	"strconv"
	"strings"
)

type EsChanRankingsV2 struct {
	Rankings
	rankScore uint64
}

type EsUserCoinsV2 struct {
	RankName string  `json:"rank_name"`
	UserId   uint64  `json:"user_id"`
	Coins    float64 `json:"coins"`
	//"rank_score":rank_score*1000000000000+user_id,
	RankScore uint64 `json:"rank_score"`
	Status    int    `json:"status"`
	UserName  string `json:"user_name"`
	Timestamp int64  `json:"timestamp"`
}

type EsRankDetailV2 struct {
	Rankings
	RankName string `json:"rank_name"`
	Week     string `json:"week"`
}

var (
	RankKeyMap = map[uint32]GetRankKeyByRankType{
		constant.RankTypeRetention: GetRetentionTypeRankKey,
		constant.RankTypeWeek:      GetWeekTypeRankKey,
		constant.RankTypeDaily:     GetDailyTypeRankKey,
		constant.RankTypeMonth:     GetMonthTypeRankKey,
	}
	RankTypePrefix = map[uint32]string{
		constant.RankTypeDaily: constant.RankDaily,
		constant.RankTypeMonth: constant.RankMonthly,
	}
)

// data convert function
type EsChanDataConvert func(buffer EsBulkBuffer, rsWriter *RankEsWriter)

// get the rankkey by ranktype
type GetRankKeyByRankType func() (*string, error)

func GetRetentionTypeRankKey() (*string, error) {
	return proto.String(constant.RankRetention), nil
}

func GetDailyTypeRankKey() (*string, error) {
	yearDay, err := utils.GetYearAndDayFromCurrent()
	if err != nil {
		gamelog.Error("get current year and day error", map[string]interface{}{
			"err": err,
		})
		return nil, err
	}
	v := proto.String(fmt.Sprintf("%s.%s", constant.RankDaily, yearDay))
	return v, nil
}

func GetWeekTypeRankKey() (*string, error) {
	week, err := utils.GetYearAndWeekFromCurrent()
	if err != nil {
		gamelog.Error("get current year and week error", map[string]interface{}{
			"err": err,
		})
		return nil, err
	}
	return &week, nil
}

func GetMonthTypeRankKey() (*string, error) {
	month, err := utils.GetYearAndMonthFromCurrent()
	if err != nil {
		gamelog.Error("get current year and month error", map[string]interface{}{
			"err": err,
		})
		return nil, err
	}
	v := proto.String(fmt.Sprintf("%s.%s", constant.RankMonthly, month))
	return v, nil
}

// golang do not has paradigm, so just do this change. Caller must validate data type
func ConvertToEsChanRankings(esChanData interface{}) *EsChanRankingsV2 {
	return esChanData.(*EsChanRankingsV2)
}

// golang do not has paradigm, so just do this change. Caller must validate data type
func ConvertToEsChanRankDetail(esChanData interface{}) *Rankings {
	return esChanData.(*Rankings)
}

func EsChanDeleteRank(esChanData interface{}) *Rankings {
	return esChanData.(*Rankings)
}

func HandleEachEsDataRankings(buffer EsBulkBuffer, rsWriter *RankEsWriter) {
	for _, v := range rsWriter.buffer {
		esRank := ConvertToEsChanRankings(v)
		gamelog.Debug("HandleEachEsDataRankings debug", gamelog.Fields{"esRank": esRank})
		rankName, yearAndWeek, err := parseRankingsRankName(esRank.RankName)
		if err != nil {
			gamelog.Info("es write fail, parse rankName err", map[string]interface{}{
				"rankings": esRank.RankName,
				"err":      err,
			})
			continue
		}

		// build each bulkIndexRequest
		index := rsWriter.generateEsIndex(esRank.Scope, rankName, yearAndWeek)
		if err := createNonLiveIndex(index); err != nil {
			continue
		}
		buffer.Append(
			index,
			strconv.FormatUint(esRank.Userid, 10),
			"default",
			&EsUserCoinsV2{
				RankName:  rankName,
				UserId:    esRank.Userid,
				Coins:     esRank.Score,
				RankScore: esRank.rankScore,
				Status:    1,
				UserName:  esRank.Username,
				Timestamp: esRank.Timestamp,
			})
	}
}

func HandleEachEsDataRankDetail(buffer EsBulkBuffer, rsWriter *RankEsWriter) {
	for _, v := range rsWriter.buffer {
		esRank := ConvertToEsChanRankDetail(v)
		rankName, yearAndWeek, err := parseRankingsRankName(esRank.RankName)
		if err != nil {
			gamelog.Info("es write fail, parse rankName err", map[string]interface{}{
				"rankings": esRank.RankName,
				"err":      err,
			})

		}

		// build each bulkIndexRequest
		esDocId := fmt.Sprintf("%d_%d", esRank.Userid, esRank.Timestamp)
		buffer.Append(
			rsWriter.generateEsIndex(esRank.Scope, rankName, yearAndWeek),
			esDocId,
			"default",
			EsRankDetailV2{
				Rankings: *esRank,
				RankName: rankName,
				Week:     yearAndWeek,
			})
	}
}

func HandleDelete(buffer EsBulkBuffer, rsWriter *RankEsWriter) {
	for _, v := range rsWriter.buffer {
		esRank := EsChanDeleteRank(v)
		rankName, yearAndWeek, err := parseRankingsRankName(esRank.RankName)
		if err != nil {
			gamelog.Info("es write fail, parse rankName err", map[string]interface{}{
				"rankings": esRank.RankName,
				"err":      err,
			})

		}

		// build each bulkRequest
		buffer.Append(
			rsWriter.generateEsIndex(esRank.Scope, rankName, yearAndWeek),
			strconv.FormatUint(esRank.Userid, 10),
			"default",
			"")
	}
}

func parseRankingsRankName(rankName string) (string, string, error) {
	temp := strings.Split(rankName, "_")
	if len(temp) != 3 {
		return "", "", errors.New("redis rankings rankName invalid")
	}

	return temp[1], temp[2], nil
}
