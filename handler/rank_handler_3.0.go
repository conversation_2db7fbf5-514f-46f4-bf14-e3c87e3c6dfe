package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"git.garena.com/shopee/game_platform/comm_lib/env"
	"git.garena.com/shopee/game_platform/comm_lib/esutil"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/proto/base"
	"git.garena.com/shopee/game_platform/proto/game_rank"
	"git.garena.com/shopee/game_platform/rank_server/constant"
	"git.garena.com/shopee/game_platform/rank_server/dao/cache"
	"github.com/golang/protobuf/proto"
	"github.com/olivere/elastic"
)

func (s *RankHandler) IncrScoreV3(ctx context.Context, req *game_rank.IncrScoreRequestV3, rsp *game_rank.IncrScoreResponseV3) error {
	module, biz := CheckParamValid(req.GetBase().GetAppId(), req.GetRankId(), true, req.GetIsGrayscale())
	if biz != nil {
		rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(biz.Code), ErrMsg: proto.String(biz.Msg)}
		return nil
	}

	v2Rsp := &game_rank.CommonResponse{}
	err := s.IncrScoreV2(ctx, &game_rank.IncrScoreRequestV2{
		Req: &game_rank.RankV2CommonRequest{
			Scope:    proto.String(req.GetBase().GetAppId()),
			RankName: proto.String(getNewRankName(req.GetRankId())),
			RankType: proto.Uint32(module.GetRankType()),

			// FlowType: proto.String(req.GetBase().GetFlowType()),
			// RankWeek: nil,
		},
		Score:    req.Score,
		UserName: req.UserName,
		UserId:   req.UserId,
	}, v2Rsp)

	rsp.Resp = &base.CommonResponse{ErrCode: v2Rsp.ErrCode, ErrMsg: v2Rsp.ErrMsg}
	return err
}

func (s *RankHandler) SetScoreV3(ctx context.Context, req *game_rank.SetScoreRequestV3, rsp *game_rank.SetScoreResponseV3) error {
	module, biz := CheckParamValid(req.GetBase().GetAppId(), req.GetRankId(), true, req.GetIsGrayscale())
	if biz != nil {
		rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(biz.Code), ErrMsg: proto.String(biz.Msg)}
		return nil
	}

	v2Rsp := &game_rank.CommonResponse{}
	err := s.SetScoreV2(ctx, &game_rank.SetScoreRequestV2{
		Req: &game_rank.RankV2CommonRequest{
			Scope:    proto.String(req.GetBase().GetAppId()),
			RankName: proto.String(getNewRankName(req.GetRankId())),
			RankType: proto.Uint32(module.GetRankType()),

			// FlowType: proto.String(req.GetBase().GetFlowType()),
			// RankWeek: nil,
		},
		Score:    req.Score,
		UserName: req.UserName,
		UserId:   req.UserId,
	}, v2Rsp)

	rsp.Resp = &base.CommonResponse{ErrCode: v2Rsp.ErrCode, ErrMsg: v2Rsp.ErrMsg}
	return err
}

func (s *RankHandler) TopNV3(ctx context.Context, req *game_rank.TopNRequestV3, rsp *game_rank.TopNResponseV3) error {
	module, biz := CheckParamValid(req.GetBase().GetAppId(), req.GetRankId(), false, req.GetIsGrayscale())
	if biz != nil {
		rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(biz.Code), ErrMsg: proto.String(biz.Msg)}
		return nil
	}

	v2Rsp := &game_rank.TopnResponse{}
	err := s.TopNV2(ctx, &game_rank.TopNRequestV2{
		Req: &game_rank.RankV2CommonRequest{
			Scope:    proto.String(req.GetBase().GetAppId()),
			RankName: proto.String(getNewRankName(req.GetRankId())),
			RankType: proto.Uint32(module.GetRankType()),

			// RankWeek: proto.String(req.GetCycle()),
			// FlowType: proto.String(req.GetBase().GetFlowType()),
		},
		N:                 req.N,
		Reverse:           req.Reverse,
		NeedRankTimestamp: req.NeedRankTimestamp,
	}, v2Rsp)

	rsp.Resp = &base.CommonResponse{ErrCode: v2Rsp.Resp.ErrCode, ErrMsg: v2Rsp.Resp.ErrMsg}
	rsp.Items = v2Rsp.Items
	return err
}

func (s *RankHandler) UserRankV3(ctx context.Context, req *game_rank.UserRankRequestV3, rsp *game_rank.UserRankResponseV3) error {
	module, biz := CheckParamValid(req.GetBase().GetAppId(), req.GetRankId(), false, req.GetIsGrayscale())
	if biz != nil {
		rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(biz.Code), ErrMsg: proto.String(biz.Msg)}
		return nil
	}

	v2Rsp := &game_rank.UserRankResponse{}
	err := s.UserRankV2(ctx, &game_rank.UserRankRequestV2{
		Req: &game_rank.RankV2CommonRequest{
			Scope:    proto.String(req.GetBase().GetAppId()),
			RankName: proto.String(getNewRankName(req.GetRankId())),
			RankType: proto.Uint32(module.GetRankType()),

			// RankWeek: proto.String(req.GetCycle()),
			// FlowType: proto.String(req.GetBase().GetFlowType()),
		},
		Reverse: req.Reverse,
		// UserName: req.UserName,
		UserId: req.UserId,
	}, v2Rsp)

	rsp.Resp = &base.CommonResponse{ErrCode: v2Rsp.Resp.ErrCode, ErrMsg: v2Rsp.Resp.ErrMsg}
	rsp.Rank = v2Rsp.Rank
	rsp.RawRank = v2Rsp.RawRank
	return err
}

func (s *RankHandler) UserScoreV3(ctx context.Context, req *game_rank.UserScoreRequestV3, rsp *game_rank.UserScoreResponseV3) error {
	module, biz := CheckParamValid(req.GetBase().GetAppId(), req.GetRankId(), false, req.GetIsGrayscale())
	if biz != nil {
		rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(biz.Code), ErrMsg: proto.String(biz.Msg)}
		return nil
	}

	v2Rsp := &game_rank.UserScoreResponse{}
	err := s.UserScoreV2(ctx, &game_rank.UserScoreRequestV2{
		Req: &game_rank.RankV2CommonRequest{
			Scope:    proto.String(req.GetBase().GetAppId()),
			RankName: proto.String(getNewRankName(req.GetRankId())),
			RankType: proto.Uint32(module.GetRankType()),

			// RankWeek: proto.String(req.GetCycle()),
			// FlowType: proto.String(req.GetBase().GetFlowType()),
		},
		// UserName: req.UserName,
		UserId: req.UserId,
	}, v2Rsp)

	rsp.Resp = &base.CommonResponse{ErrCode: v2Rsp.Resp.ErrCode, ErrMsg: v2Rsp.Resp.ErrMsg}
	rsp.Score = v2Rsp.Score
	return err
}

func (s *RankHandler) UserScoreListV3(ctx context.Context, req *game_rank.UserScoreListRequestV3, rsp *game_rank.UserScoreListResponseV3) error {
	module, biz := CheckParamValid(req.GetBase().GetAppId(), req.GetRankId(), false, req.GetIsGrayscale())
	if biz != nil {
		rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(biz.Code), ErrMsg: proto.String(biz.Msg)}
		return nil
	}

	v2Rsp := &game_rank.UserScoreListResponse{}
	err := s.UserScoreListV2(ctx, &game_rank.UserScoreListRequestV2{
		Req: &game_rank.RankV2CommonRequest{
			Scope:    proto.String(req.GetBase().GetAppId()),
			RankName: proto.String(getNewRankName(req.GetRankId())),
			RankType: proto.Uint32(module.GetRankType()),

			// RankWeek: proto.String(req.GetCycle()),
			// FlowType: proto.String(req.GetBase().GetFlowType()),
		},
		UserIds: req.UserIds,
		Detail:  req.Detail,
	}, v2Rsp)

	rsp.Resp = &base.CommonResponse{ErrCode: v2Rsp.Resp.ErrCode, ErrMsg: v2Rsp.Resp.ErrMsg}
	rsp.Items = v2Rsp.Items
	return err
}

func (s *RankHandler) DeleteUserRankV3Admin(ctx context.Context, req *game_rank.DeleteUserRankV3AdminRequest, rsp *game_rank.DeleteUserRankV3AdminResponse) error {
	module, biz := CheckParamValid(req.GetBase().GetAppId(), req.GetRankId(), false, false)
	if biz != nil {
		rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(biz.Code), ErrMsg: proto.String(biz.Msg)}
		return nil
	}

	v2Rsp := &game_rank.CommonResponse{}
	err := s.DeleteUserRankV2(ctx, &game_rank.DeleteUserRankRequestV2{
		Req: &game_rank.RankV2CommonRequest{
			Scope:    proto.String(req.GetBase().GetAppId()),
			RankName: proto.String(getNewRankName(req.GetRankId())),
			RankType: proto.Uint32(module.GetRankType()),
			RankWeek: proto.String(req.GetCycle()),
			FlowType: proto.String(req.GetBase().GetFlowType()),
		},
		UserId: req.UserId,
	}, v2Rsp)

	rsp.Resp = &base.CommonResponse{ErrCode: v2Rsp.ErrCode, ErrMsg: v2Rsp.ErrMsg}
	return err
}

func (s *RankHandler) IncrScoreV3Admin(ctx context.Context, req *game_rank.IncrScoreV3AdminRequest, rsp *game_rank.IncrScoreV3AdminResponse) error {
	userId := req.GetUserId()
	add := req.GetAdd()
	rankId := req.GetRankId()
	// cycle := req.GetCycle()
	appid := req.GetBase().GetAppId()
	updateCodis := req.GetUpdateCodis()

	// retention类型前端cycle为空
	if userId == 0 || add == 0 || rankId == 0 /*|| cycle == ""*/ || appid == "" {
		rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(ErrParamEmpty.Code), ErrMsg: proto.String(ErrParamEmpty.Msg)}
		return nil
	}

	module, biz := CheckParamValid(appid, rankId, false, false)
	if biz != nil {
		rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(biz.Code), ErrMsg: proto.String(biz.Msg)}
		return nil
	}

	rankName := getNewRankName(req.GetRankId())
	rankWeek := AdminCheckAndFillRankWeek(req.GetBase().GetAppId(), module.GetRankType(), req.GetCycle())
	index := jointEsIndexForNewRank(req.GetBase().GetAppId(), rankName, rankWeek)
	gamelog.Info("IncrScoreV3Admin|jointEsIndexForNewRank result", gamelog.Fields{"index": index, "req": req})
	if err := checkOpenIndex(index); err != nil {
		if elastic.IsNotFound(err) {
			gamelog.Error("IncrScoreV3Admin|es index not found", gamelog.Fields{"err": err, "req": req})
			rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(101), ErrMsg: proto.String("index not found")}
			return nil
		}

		gamelog.Error("IncrScoreV3Admin|checkOpenIndex error", gamelog.Fields{"err": err, "req": req})
		rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(ErrEsQuery.Code), ErrMsg: proto.String(ErrEsQuery.Msg)}
		return nil
	}

	// use es client get service find a doc
	res, err := esutil.EsClient.Get().Index(index).Id(fmt.Sprintf("%d", userId)).Do(ctx)
	if err != nil {
		if elastic.IsNotFound(err) {
			gamelog.Error("IncrScoreV3Admin|es query not found", gamelog.Fields{"err": err, "req": req})
			rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(ErrEsQueryNoFound.Code), ErrMsg: proto.String(ErrEsQueryNoFound.Msg)}
			return nil
		}

		gamelog.Error("IncrScoreV3Admin|es query error", gamelog.Fields{"err": err, "req": req})
		rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(ErrEsQuery.Code), ErrMsg: proto.String(ErrEsQuery.Msg)}
		return nil
	}

	var esUser EsUserCoinsV2
	if err = json.Unmarshal(*res.Source, &esUser); err != nil {
		gamelog.Error("IncrScoreV3Admin|json.Unmarshal", gamelog.Fields{"err": err, "req": req})
		rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(ErrInternal.Code), ErrMsg: proto.String(ErrInternal.Msg)}
		return nil
	}

	coin, timestamp := SplitFloat32AndTimestamp(float64(esUser.RankScore))
	if coin+add < 0 {
		rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(ErrEsScoreNotEnough.Code), ErrMsg: proto.String(ErrEsScoreNotEnough.Msg)}
		return nil
	}

	esUser.Coins += add
	esUser.RankScore = uint64(ConcatFloat32AndTimestamp(esUser.Coins, timestamp))
	gamelog.Info("IncrScoreV3Admin|update es info", gamelog.Fields{"index": index, "esUser": esUser, "req": req})
	_, err = esutil.EsClient.Update().Index(index).Type("_doc").Id(fmt.Sprintf("%d", userId)).Doc(esUser).Do(ctx)
	if err != nil {
		gamelog.Error("IncrScoreV3Admin|es.Update error", gamelog.Fields{"err": err, "req": req})
		rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(ErrEsUpdate.Code), ErrMsg: proto.String(ErrEsUpdate.Msg)}
		return nil
	}

	if updateCodis {
		redisRankName := concatRankNameAndRankWeek(rankName, rankWeek)
		key, _ := userIdHashBucketRank(appid, redisRankName, userId, conf.rankRedisBucket)
		gamelog.Info("IncrScoreV3Admin|update codis info", gamelog.Fields{"key": key, "score": esUser.RankScore, "req": req})

		if _, err = cache.RedisUtilZAdd(ctx, key, float64(esUser.RankScore), userId); err != nil {
			gamelog.Error("IncrScoreV3Admin|redis.Zadd err", gamelog.Fields{"err": err, "req": req})
			return nil
		}

		// 双写
		DoubleWriteTopNKey(float64(esUser.RankScore), true, true, &Rankings{
			Userid:   userId,
			RankType: int32(module.GetRankType()),
			Score:    float64(esUser.RankScore),
			Scope:    appid,
			RankName: redisRankName,
		})
	}

	return nil
}

func jointEsIndexForNewRank(appid string, rankName string, cycle string) string {
	e, r := env.GetEnv(), env.GetCID()
	return strings.ToLower(fmt.Sprintf(constant.WriteRankIndexFormatV2, appid, e, r, rankName, cycle))
}

func (s *RankHandler) RankListV3Admin(ctx context.Context, req *game_rank.RankListV3AdminRequest, rsp *game_rank.RankListV3AdminResponse) error {
	module, biz := CheckParamValid(req.GetBase().GetAppId(), req.GetRankId(), false, false)
	if biz != nil {
		rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(biz.Code), ErrMsg: proto.String(biz.Msg)}
		return nil
	}

	v2Rsp := &game_rank.RankListResponseV2{}
	err := s.RankListV2(ctx, &game_rank.RankListRequestV2{
		// EventId:   nil,
		Offset:    proto.Uint64(req.GetOffset()),
		Limit:     proto.Uint64(req.GetLimit()),
		UserId:    proto.Uint64(req.GetUserId()),
		UserName:  proto.String(req.GetUserName()),
		RankValue: proto.Float32(req.GetRankValue()),
		RankNum:   proto.Uint64(req.GetRankNum()),
		RankWeek:  proto.String(req.GetCycle()),
		RankType:  proto.Uint32(module.GetRankType()),
		Scope:     proto.String(req.GetBase().GetAppId()),
		RankId:    proto.String(getNewRankName(req.GetRankId())),
		ScrollId:  proto.String(req.GetScrollId()),
		RobotId:   proto.Int64(req.GetRobotId()),
	}, v2Rsp)

	rsp.Resp = &base.CommonResponse{ErrCode: v2Rsp.Resp.ErrCode, ErrMsg: v2Rsp.Resp.ErrMsg}
	rsp.Items = v2Rsp.Items
	rsp.TotalNum = v2Rsp.TotalNum
	rsp.NextScrollId = v2Rsp.NextScrollId
	return err
}

func CheckParamValid(appid string, rankId int32, checkCurrentTime, isGrayscale bool) (*RankModuleInfo, *BizError) {
	if len(appid) == 0 || rankId <= 0 {
		return nil, ErrParamEmpty
	}

	module, err := GetCachedNewRankModule(appid, rankId, isGrayscale)
	if err != nil {
		return nil, ErrInternal
	}

	if module.Config == nil {
		return nil, ErrRankModuleNotFound
	}

	if len(module.Appid) == 0 {
		return nil, ErrRankModuleNotMatch
	}

	if checkCurrentTime && !module.IsTimeValid() {
		return nil, ErrRankModuleTimeNotValid
	}

	return module, nil
}
