package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/game_platform/comm_lib/esutil"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/proto/game_rank"
	"git.garena.com/shopee/game_platform/rank_server/constant"
	"github.com/golang/protobuf/proto"
	"github.com/olivere/elastic"
	"io"
	"sync"
	"testing"
	"time"
)

func TestReOpenClosedIndex(t *testing.T) {
	InitElasticWriter(2011)
	var items = []map[string]*elastic.BulkResponseItem{
		{
			"index": {
				//已经打开的
				Error: &elastic.ErrorDetails{Type: IndexClosedException},
				Index: "mkt_games_ia1qiazm30gqjycccw_test_co_rank_2_monthly.2021.11",
				Id:"182_723152620_opened",
			},
		},
		{
			"index": {
				//存在可以打开的
				Error: &elastic.ErrorDetails{Type: IndexClosedException},
				Index: "mkt_games_ia1qiazm30gqjycccw_test_co_rank_2_daily.2021.317",
				Id:"182_723152620_co_can_open",
			},
		},
		{
			"index": {
				//取不到error字段

			},
		},
		{
			//取不到index
			"err": {

			},
		},
		{
			"index": {
				//错误类型为空
				Error: &elastic.ErrorDetails{Type: " "},
			},
		},
		{
			"index": {
				//不存在的index
				Error: &elastic.ErrorDetails{Type: IndexClosedException},
				Index: "mkt_games_ia1qiazm30gqjycccw_test_co_rank_3_retention",
				Id:"182_723152620_no_existent",
			},
		},
		{
			"index": {
				//可关闭index
				Error: &elastic.ErrorDetails{Type: IndexClosedException},
				Index: "mkt_games_ia1qiazm30gqjycccw_test_co_rank_2_retention",
				Id:"182_723152620_closed",
			},
		},
		{
			"index": {
				//数组重复已经打开过的
				Error: &elastic.ErrorDetails{Type: IndexClosedException},
				Index: "mkt_games_ia1qiazm30gqjycccw_test_co_rank_2_daily.2021.317",
				Id:"182_723152620_co_can_open",
			},
		},

	}
	reOpenClosedIndex(items)
}

func TestRankEsWriter_Write(t *testing.T) {
	writer := &RankEsWriter{}
	writer.Init(InitEsIndexFormat(constant.WriteRankIndexFormatV2))
	writer.Init(InitEsDataAndConvertFunc(HandleEachEsDataRankings),
		InitEsOperation(constant.EsAdd),
		InitPrometheusMonitorLabel(constant.RankingsEsChanLabel))

	rank := &EsChanRankingsV2{
		Rankings: Rankings{
			Scope:     constant.GameARScope,
			RankName:  "rank_999_9",
			Username:  "<EMAIL>",
			Userid:    9999,
			Score:     10000000.0,
			Timestamp: 1566478301,
		},
		rankScore: 0,
	}
	writer.Write(rank)

	//writer.Init(InitEsDataAndConvertFunc(HandleDelete),
	//	InitEsOperation(constant.EsDelete),
	//	InitPrometheusMonitorLabel(constant.DeleteRankingsEsChanLabel))
	//
	//deleteRank := &Rankings{
	//	Scope:     "ar_catch",
	//	RankName:  "999_9",
	//	Username:  "<EMAIL>",
	//	Userid:    9999,
	//	Score:     10000000.0,
	//	Timestamp: 1566478301,
	//}
	//
	//writer.Write(deleteRank)

	time.Sleep(time.Hour)
}

func TestEsDelete(t *testing.T) {
	rankDetailWriter := &RankEsWriter{}
	rankDetailWriter.Init(InitEsIndexFormat(constant.WriteRankIndexFormatV2))
	rankDetailWriter.Init(InitEsDataAndConvertFunc(HandleDelete),
		InitEsOperation(constant.EsDelete),
		InitPrometheusMonitorLabel(constant.DeleteRankingsEsChanLabel))

	rankDetail := &Rankings{
		Scope:     constant.GameARScope,
		RankName:  "rank_999_9",
		Username:  "<EMAIL>",
		Userid:    9999,
		Score:     10000000.0,
		Timestamp: 1566478301,
	}

	rankDetailWriter.Write(rankDetail)

	time.Sleep(time.Hour)
}

func TestEsChanRankings(t *testing.T) {
	//writer := &RankEsWriter{}
	//writer.Init(InitEsIndexFormat(constant.WriteRankIndexFormatV2))
	//writer.Init(InitEsDataAndConvertFunc(HandleEachEsDataRankings),
	//	InitEsOperation(constant.EsAdd),
	//	InitPrometheusMonitorLabel(constant.RankingsEsChanLabel))
	//
	//f := func (userID uint64) *EsChanRankingsV2 {
	//	return &EsChanRankingsV2{
	//		Rankings:  handler.Rankings{
	//			Scope: "ar_catch",
	//			RankName: "999_9",
	//			Username: "<EMAIL>",
	//			Userid:    userID,
	//			Score:     10000000.0,
	//			Timestamp: 1566478301,
	//		},
	//		rankScore: 0,
	//	}
	//}
	//for i:=0; i < 1024 * 10; i++ {
	//	go writer.Write(f(uint64(i)))
	//}

	deleteWriter := &RankEsWriter{}
	deleteWriter.Init(InitEsIndexFormat(constant.WriteRankIndexFormatV2))
	deleteWriter.Init(InitEsDataAndConvertFunc(HandleDelete),
		InitEsOperation(constant.EsDelete),
		InitPrometheusMonitorLabel(constant.DeleteRankingsEsChanLabel))

	fd := func(userID uint64) *Rankings {
		return &Rankings{
			Scope:     constant.GameARScope,
			RankName:  "rank_999_9",
			Username:  "<EMAIL>",
			Userid:    userID,
			Score:     10000000.0,
			Timestamp: 1566478301,
		}
	}
	for i := 0; i < 1024*10; i++ {
		go deleteWriter.Write(fd(uint64(i)))
	}

	time.Sleep(time.Hour)
}

func TestAdminEs(t *testing.T) {
	InitElasticWriter(EsUserCoisChanSize)
	//res_1, n := rankList(999, 0, 1, 0, "hao", -1, 0, "9")
	//fmt.Println(res_1, n)
	//
	//res_2, n := rankList(999, 0, 10, 78676767, "hao", -1, 0, "9")
	//fmt.Println(res_2, n)
	//
	//res_3, n := rankList(999, 0, 0, 0, "", -1, 0, "9")
	//fmt.Println(res_3, n)

	res_4, n := rankList(30, 11800, 0, 0, "", -1, 0, "35", constant.GameARScope)
	fmt.Println(res_4, n)
}

func TestEs(t *testing.T) {
	InitElasticWriter(EsUserCoisChanSize)
	esIndex := "mkt_games_gamear_dev_local_rank_999_9"
	res, err := esutil.EsClient.CatCount().Index(esIndex).Do(context.Background())
	if err != nil || len(res) != 1 {
		gamelog.Error("cat count for index err", map[string]interface{}{
			"index": esIndex,
			"err":   err,
		})
	}
	result := make([]*game_rank.RankListItem, 0, res[0].Count)
	group := sync.WaitGroup{}

	rankNum := uint64(0)
	// 1st goroutine sends individual hits to channel.
	type EsJson struct {
		message json.RawMessage
		rankNum uint64
	}
	hits := make(chan *EsJson, 1024)

	group.Add(1)
	go func(ctx context.Context) error {
		defer func() {
			close(hits)
			group.Done()
		}()
		// Initialize scroller. Just don't call Do yet.
		scroll := esutil.EsClient.Scroll(esIndex).Type("default").Size(10)
		for {
			results, err := scroll.Do(context.Background())
			if err == io.EOF {
				return nil // all results retrieved
			}
			if err != nil {
				return err // something went wrong
			}

			// Send the hits to the hits channel
			for _, hit := range results.Hits.Hits {
				select {
				case hits <- &EsJson{*hit.Source, rankNum}:
					rankNum++
				case <-ctx.Done():
					return ctx.Err()
				}
			}
		}
	}(context.Background())

	// 2nd goroutine receives hits and deserializes them.
	//
	// If you want, setup a number of goroutines handling deserialization in parallel.
	group.Add(10)
	for i := 0; i < 10; i++ {
		go func(ctx context.Context) error {
			defer group.Done()

			for hit := range hits {
				// Deserialize
				var temp EsUserCoinsV2
				err := json.Unmarshal(hit.message, &temp)
				if err != nil {
					return err
				}

				// Do something with the product here, e.g. send it to another channel
				// for further processing.
				var rankListItem = &game_rank.RankListItem{
					UserId:    proto.Uint64(temp.UserId),
					UserName:  proto.String(temp.UserName),
					RankValue: proto.Float64(temp.Coins),
					RankScore: proto.Uint64(temp.RankScore),
					RankNum:   proto.Uint64(hit.rankNum),
				}
				result = append(result, rankListItem)

				// Terminate early?
				select {
				default:
				case <-ctx.Done():
					return ctx.Err()
				}
			}
			return nil
		}(context.Background())
	}

	// Wait 2 goroutines end.
	group.Wait()

	fmt.Println(result)
}

func TestInsert(t *testing.T) {
	a := [5]*game_rank.RankListItem{
		{
			RankNum: proto.Uint64(1),
		},
		{
			RankNum: proto.Uint64(2),
		},
		{
			RankNum: proto.Uint64(5),
		},
		{
			RankNum: proto.Uint64(6),
		},
	}

	e := &game_rank.RankListItem{
		UserId:    nil,
		UserName:  nil,
		RankValue: nil,
		RankNum:   proto.Uint64(7),
	}
	fmt.Println(testInsertSort(a[:4], e))
}
func testInsertSort(result []*game_rank.RankListItem, element *game_rank.RankListItem) []*game_rank.RankListItem {
	if len(result) == 0 {
		result = append(result, element)
		return result
	}
	l := len(result) - 1
	for l >= 0 {
		if *result[l].RankNum > *element.RankNum {
			l--
			continue
		} else {
			result = testSliceBackMove(result, l+1)
			result[l+1] = element
			return result
		}
	}

	if l < 0 {
		result = testSliceBackMove(result, 0)
		result[0] = element
	}
	return result
}

func testSliceBackMove(s []*game_rank.RankListItem, from int) []*game_rank.RankListItem {
	i := len(s) - 1
	s = append(s, s[i])
	for i >= from {
		s[i+1] = s[i]
		i--
	}

	return s
}
