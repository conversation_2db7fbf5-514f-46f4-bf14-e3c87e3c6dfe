package handler

import (
	"context"
	"math/rand"

	"git.garena.com/shopee/feed/microkit"
	"git.garena.com/shopee/game/grpc4spex"
	"git.garena.com/shopee/game_platform/comm_lib/utils"
	"git.garena.com/shopee/game_platform/proto/base"
	"git.garena.com/shopee/game_platform/proto/game_rank"

	"testing"
	"time"

	"github.com/golang/protobuf/proto"
)

func Init() {
	InitElasticWriter(1024 * 1024)
}

func TestGetCacheConfig(t *testing.T) {
	Init()
	tab, err := GetCachedNewRankModule("YnoH4kjXThXXcDgMbR", 32, false)
	if err != nil {
		t.Error(err)
	}

	tab, err = GetCachedNewRankModule("YnoH4kjXThXXcDgMbR", 32, false)

	t.Log(tab)
}

func TestTime(t *testing.T) {
	now := time.Unix(1686495422, 0)
	t.Log(now)
	t.Log(utils.GetYearAndWeekFromTimestamp(1686495422))
}

func TestIncrScoreV3(t *testing.T) {
	microkit.Init()
	cli := game_rank.NewClient()
	/*rsp, err := cli.IncrScoreV3(context.TODO(), &game_rank.IncrScoreRequestV3{
		Base: &base.CommonRequest{
			//TraceId:  nil,
			//FlowType: nil,
			AppId: proto.String("YnoH4kjXThXXcDgMbR"),
		},
		RankId:   proto.Int32(36),
		Score:    proto.Float64(float64(rand.Int31n(5000))),
		UserName: proto.String(generateRandomString(6)),
		UserId:   proto.Uint64(1225200059),
	})

	if err != nil || rsp.GetResp().GetErrCode() != 0 {
		t.Log("incr error", err, rsp)
		return
	}*/
	for i := 10000; i < 1200000; i++ {
		rsp, err := cli.IncrScoreV3(context.TODO(), &game_rank.IncrScoreRequestV3{
			Base: &base.CommonRequest{
				//TraceId:  nil,
				//FlowType: nil,
				AppId: proto.String("GVS1I8Xs5pJ7JzfYlg"),
			},
			RankId:   proto.Int32(72),
			Score:    proto.Float64(float64(rand.Int31n(2000000))),
			UserName: proto.String(generateRandomString(6)),
			UserId:   proto.Uint64(uint64(1225200060 + i)),
		})

		if err != nil || rsp.GetResp().GetErrCode() != 0 {
			t.Log("incr error", err, rsp)
			return
		}

		if i%10000 == 0 {
			t.Log("incr 1000000 success", i)
		}
	}

}

func generateRandomString(length int) string {
	rand.Seed(time.Now().UnixNano())

	// 可取值的字符集
	charset := "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

	// 生成随机字符
	result := make([]byte, length)
	for i := 0; i < length; i++ {
		randomIndex := rand.Intn(len(charset))
		result[i] = charset[randomIndex]
	}

	return string(result)
}

func TestSetScoreV3(t *testing.T) {
	microkit.Init()
	cli := game_rank.NewClient()
	rsp, err := cli.SetScoreV3(context.TODO(), &game_rank.SetScoreRequestV3{
		Base: &base.CommonRequest{
			//TraceId:  nil,
			//FlowType: nil,
			AppId: proto.String("nLpX6u3PKm2m7Lxd90"),
		},
		RankId:   proto.Int32(4),
		Score:    proto.Float64(99),
		UserName: proto.String("ss2"),
		UserId:   proto.Uint64(123457),
	})

	t.Log(err)
	t.Log(rsp.GetResp().GetErrCode())
}

func TestTopNV3(t *testing.T) {
	microkit.Init()
	cli := game_rank.NewClient()
	rsp, err := cli.TopNV3(context.TODO(), &game_rank.TopNRequestV3{
		Base: &base.CommonRequest{
			//TraceId:  nil,
			//FlowType: nil,
			AppId: proto.String("nLpX6u3PKm2m7Lxd90"),
		},
		RankId: proto.Int32(4),
		//Cycle:             proto.String("2023.6"),
		N: proto.Int32(100),
		//Reverse:           nil,
		NeedRankTimestamp: proto.Int32(1),
	})

	t.Log(err)
	t.Log(rsp.GetResp().GetErrCode())
}

func TestUserRankV3(t *testing.T) {
	microkit.Init()
	cli := game_rank.NewClient()
	rsp, err := cli.UserRankV3(context.TODO(), &game_rank.UserRankRequestV3{
		Base: &base.CommonRequest{
			//TraceId:  nil,
			//FlowType: nil,
			AppId: proto.String("nLpX6u3PKm2m7Lxd90"),
		},
		RankId: proto.Int32(3),
		//Cycle:    nil,
		//Reverse:  nil,
		//UserName: nil,
		UserId: proto.Uint64(123457),
	})

	t.Log(err)
	t.Log(rsp)
}

func TestUserScoreV3(t *testing.T) {
	Init()
	h := RankHandler{}
	/*	microkit.Init()
		cli := game_rank.NewClient()*/
	rsp := &game_rank.UserScoreResponseV3{}
	err := h.UserScoreV3(context.TODO(), &game_rank.UserScoreRequestV3{
		Base: &base.CommonRequest{
			//TraceId:  nil,
			//FlowType: nil,
			AppId: proto.String("GVS1I8Xs5pJ7JzfYlg"),
		},
		RankId: proto.Int32(6),
		//Cycle:    nil,
		//UserName: nil,
		UserId: proto.Uint64(1225200147),
	}, rsp)

	t.Log(err)
	t.Log(rsp)
}

func TestUserScoreListV3(t *testing.T) {
	microkit.Init()
	cli := game_rank.NewClient()
	rsp, err := cli.UserScoreListV3(context.TODO(), &game_rank.UserScoreListRequestV3{
		Base: &base.CommonRequest{
			//TraceId:  nil,
			//FlowType: nil,
			AppId: proto.String("nLpX6u3PKm2m7Lxd90"),
		},
		RankId: proto.Int32(4),
		//Cycle:   nil,
		UserIds: []uint64{123456, 123457, 123},
		Detail:  proto.Bool(true),
	})

	t.Log(err)
	t.Log(rsp)
}

func TestDeleteUserRankV3(t *testing.T) {
	microkit.Init()
	cli := game_rank.NewClient()
	rsp, err := cli.DeleteUserRankV3Admin(context.TODO(), &game_rank.DeleteUserRankV3AdminRequest{
		Base: &base.CommonRequest{
			//TraceId:  nil,
			//FlowType: nil,
			AppId: proto.String("nLpX6u3PKm2m7Lxd90"),
		},
		RankId: proto.Int32(4),
		//Cycle:  proto.String("2023.163"),
		UserId: proto.Uint64(123457),
	})

	t.Log(err)
	t.Log(rsp)
}

func TestRankListV3Admin(t *testing.T) {
	//microkit.Init()
	//cli := game_rank.NewClient()
	Init()
	h := RankHandler{}
	ret := make([]*game_rank.RankListItem, 0)
	scrollId := "-1"
	for {
		rsp := &game_rank.RankListV3AdminResponse{}
		err := h.RankListV3Admin(context.TODO(), &game_rank.RankListV3AdminRequest{
			Base: &base.CommonRequest{
				//TraceId:  nil,
				//FlowType: nil,
				AppId: proto.String("GVS1I8Xs5pJ7JzfYlg"),
			},
			RankId: proto.Int32(72),
			Cycle:  proto.String("2024.58"),
			//Offset: proto.Uint64(10000),
			Limit: proto.Uint64(10000),
			//UserId:    nil,
			//UserName:  nil,
			RankValue: proto.Float32(-1),
			//RankNum:   nil,
			ScrollId: proto.String(scrollId),
		}, rsp)

		if err != nil {
			t.Log(err)
			return
		}

		items := rsp.GetItems()
		ret = append(ret, items...)
		if len(items) < 10000 {
			break
		}

		scrollId = rsp.GetNextScrollId()
	}

	//5634+138190
	//138190
	t.Log(len(ret))
}

func TestIncrScoreV3Admin(t *testing.T) {
	Init()
	h := RankHandler{}
	req := &game_rank.IncrScoreV3AdminRequest{
		Base: &base.CommonRequest{
			//TraceId:  nil,
			//FlowType: nil,
			AppId: proto.String("GVS1I8Xs5pJ7JzfYlg"),
		},
		RankId:      proto.Int32(6),
		Cycle:       proto.String("2023.43"),
		Add:         proto.Float64(81),
		UserId:      proto.Uint64(122520008111),
		UpdateCodis: proto.Bool(true),
	}
	resp := &game_rank.IncrScoreV3AdminResponse{}

	h.IncrScoreV3Admin(context.TODO(), req, resp)
}

func TestDeleteUserRank(t *testing.T) {
	// 测试删除rank, 写入Mysql
	grpc4spex.Init(grpc4spex.WithSpexConfig("game.rank", "ef63eebc9852132608d79b088bbed329", "3d3864fae38757af72729ead665f8799"))
	conn, err := grpc4spex.GetConn() // get a connection from gprc4spex

	if err != nil {
		t.Error(err)
	}
	defer grpc4spex.ReleaseConn(conn)

	gameRankClient := game_rank.NewRankClient(conn)
	resp, err := gameRankClient.DeleteUserRankV2(context.Background(), &game_rank.DeleteUserRankRequestV2{
		Req: &game_rank.RankV2CommonRequest{
			Scope:    proto.String(""),
			RankName: proto.String(""),
			RankType: proto.Uint32(0),
			RankWeek: proto.String(""),
			FlowType: proto.String(""),
		},
		UserId: proto.Uint64(0),
	})
	if err != nil {
		t.Error(err)
	}
	_ = resp
}
