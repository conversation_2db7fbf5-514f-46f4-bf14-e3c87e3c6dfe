package handler

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	spiCache "git.garena.com/shopee/mts/go-application-server/spi/cache"
	"strings"
	"time"

	"git.garena.com/shopee/game_platform/comm_lib/env"
	"git.garena.com/shopee/game_platform/comm_lib/esutil"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/proto/game_rank"
	"git.garena.com/shopee/game_platform/rank_server/constant"
	"git.garena.com/shopee/game_platform/rank_server/dao"
	"git.garena.com/shopee/game_platform/rank_server/dao/cache"
	"git.garena.com/shopee/game_platform/rank_server/model"
	"github.com/golang/protobuf/proto"
)

var (
	recordNotFound = errors.New("record not found")
)

func (s *RankHandler) RecoverUserRank(ctx context.Context, req *game_rank.RecoverUserRankRequest, rsp *game_rank.RecoverUserRankResponse) error {
	if len(req.GetReq().GetScope()) == 0 ||
		len(req.GetReq().GetRankName()) == 0 || len(req.GetReq().GetRankWeek()) == 0 {
		gamelog.Error("param error(empty field)", gamelog.Fields{"req": req})
		rsp.Resp = &game_rank.CommonResponse{ErrCode: proto.Int32(100), ErrMsg: proto.String("param error")}
		return nil
	}

	if req.GetUidType() == 0 || req.GetRecoverType() == 0 || req.GetActionType() == 0 {
		gamelog.Error("param error(type=0)", gamelog.Fields{"req": req})
		rsp.Resp = &game_rank.CommonResponse{ErrCode: proto.Int32(100), ErrMsg: proto.String("param error")}
		return nil
	}

	if req.GetUidType() == 1 && len(req.GetSpecUid()) == 0 {
		gamelog.Error("param error(no spec uid)", gamelog.Fields{"req": req})
		rsp.Resp = &game_rank.CommonResponse{ErrCode: proto.Int32(100), ErrMsg: proto.String("param error")}
		return nil
	}

	recoveryList, err := dao.SelectUserRankRecoveryData(req.GetReq().GetScope(), req.GetReq().GetRankName(),
		req.GetReq().GetRankWeek(), int32(req.GetReq().GetRankType()), req.GetSpecUid())
	if err != nil {
		gamelog.Error("SelectUserRankRecoveryData failed", gamelog.Fields{"err": err, "req": req})
		return err
	}

	recoveryUidMap := make(map[uint64]interface{})
	for _, v := range recoveryList {
		if req.GetRecoverType() == 1 {
			if _, ok := recoveryUidMap[v.UserId]; ok {
				gamelog.Info("filter user recovery by multi record rule", gamelog.Fields{"uid": v.UserId, "id": v.ID})
				continue
			}
		}
		recoveryUidMap[v.UserId] = true
		rankName := v.RankName
		if v.Scope != constant.GameRCScope {
			rankName = concatRankNameAndRankWeek(v.RankName, v.RankWeek)
		}

		key, _ := userIdHashBucketRank(v.Scope, rankName, v.UserId, conf.rankRedisBucket)
		gameScore, _ := SplitFloat32AndTimestamp(float64(v.Score))
		newRedisScore := float64(v.Score)
		if req.GetActionType() == 1 {
			// incr
			redisScore, err := cache.RedisUtilZScore(ctx, key, fmt.Sprintf("%d", v.UserId))
			if err != nil && err != spiCache.RedisNil {
				gamelog.Error("zscore failed", gamelog.Fields{"err": err, "key": key, "id": v.ID})
				rsp.FailedResultInfo = append(rsp.FailedResultInfo, &game_rank.RecoverResultInfo{
					Id:  proto.Uint64(v.ID),
					Uid: proto.Uint64(v.UserId),
					Msg: proto.String("zscore failed"),
				})
				continue
			}

			if err == nil {
				scoreBeforeAdd, timeStampBeforeAdd := SplitFloat32AndTimestamp(redisScore)
				gameScore = gameScore + scoreBeforeAdd
				newRedisScore = ConcatFloat32AndTimestamp(gameScore, timeStampBeforeAdd)
			}
		}

		if _, err := cache.RedisUtilZAdd(ctx, key, newRedisScore, v.UserId); err != nil {
			gamelog.Error("zadd failed", gamelog.Fields{"err": err, "key": key, "id": v.ID, "newscore": newRedisScore})
			rsp.FailedResultInfo = append(rsp.FailedResultInfo, &game_rank.RecoverResultInfo{
				Id:  proto.Uint64(v.ID),
				Uid: proto.Uint64(v.UserId),
				Msg: proto.String("zadd failed"),
			})
			continue
		}

		expireRankKey(key, int32(v.RankType))

		// 双写
		DoubleWriteTopNKey(newRedisScore, true, true, &Rankings{
			Userid:   v.UserId,
			RankType: int32(v.RankType),
			Score:    newRedisScore,
			Scope:    v.Scope,
			RankName: rankName,
		})

		rankInfo := &Rankings{
			Scope:    v.Scope,
			RankName: rankName,
			Userid:   v.UserId,
			Score:    float64(uint64(gameScore*100)) / 100,
			Username: v.Username,
		}
		if v.Scope == constant.GameRCScope {
			rankInfo.RCRankWeek = v.RankWeek
			addEsRanking(EsChanRankings{*rankInfo, uint64(newRedisScore)})
		} else {
			RankingsEsWriter.Write(&EsChanRankingsV2{*rankInfo, uint64(newRedisScore)})
		}

		v.RecoverTime = time.Now().Unix()
		v.Operator = req.GetOperator()
		v.Recover = 1
		if err := dao.UpdateUserRankRecoveryData(v); err != nil {
			gamelog.Error("UpdateUserRankRecoveryData failed", gamelog.Fields{
				"err": err,
				"id":  v.ID,
				"uid": v.UserId,
			})
			rsp.FailedResultInfo = append(rsp.FailedResultInfo, &game_rank.RecoverResultInfo{
				Id:  proto.Uint64(v.ID),
				Uid: proto.Uint64(v.UserId),
				Msg: proto.String("update DB failed"),
			})
		} else {
			rsp.SuccessResultInfo = append(rsp.SuccessResultInfo, &game_rank.RecoverResultInfo{
				Id:  proto.Uint64(v.ID),
				Uid: proto.Uint64(v.UserId),
				Msg: proto.String("success"),
			})
		}
	}
	return nil
}

func SaveUserRecoveryData(scope, rankName, rankWeek, operator string,
	rankType uint32, userId, score uint64, useEsScore bool) error {
	var userName string
	if scope == constant.GameRCScope {
		eventId, err := parseRankName(rankName)
		if err != nil {
			gamelog.Info("es write fail, parse rankName err", map[string]interface{}{
				"info": rankName,
				"err":  err,
			})
			return fmt.Errorf("check rc rank week failed")
		}
		esIndex := EsWeekRankIndex(eventId, rankWeek)
		gamelog.Info("rc index", gamelog.Fields{"index": esIndex, "uid": userId})

		esInfo, err := getUserScoreFromEsV1(esIndex, userId)
		if err != nil {
			gamelog.Error("getUserScoreFromEsV1 failed", gamelog.Fields{"err": err, "index": esIndex, "uid": userId})
			return err
		}
		gamelog.Info("get es score v1", gamelog.Fields{"score": esInfo.RankScore, "name": esInfo.UserName, "index": esIndex, "uid": userId})
		if useEsScore {
			score = esInfo.RankScore
		}
		userName = esInfo.UserName
	} else {
		esIndex := strings.ToLower(fmt.Sprintf(constant.WriteRankIndexFormatV2, scope,
			env.GetEnv(), env.GetCID(), rankName, rankWeek))
		gamelog.Info("gp/ar index", gamelog.Fields{"index": esIndex, "uid": userId})

		esInfo, err := getUserScoreFromEsV2(esIndex, rankName, userId)
		if err != nil {
			gamelog.Error("getUserScoreFromEsV2 failed", gamelog.Fields{"err": err, "index": esIndex, "uid": userId})
			return err
		}
		gamelog.Info("get es score v2", gamelog.Fields{"score": esInfo.RankScore, "name": esInfo.UserName, "index": esIndex, "uid": userId})
		if useEsScore {
			score = esInfo.RankScore
		}
		userName = esInfo.UserName
	}

	return dao.InsertUserRankRecoveryData(&model.UserRankRecovery{
		Scope:      scope,
		RankName:   rankName,
		RankWeek:   rankWeek,
		RankType:   rankType,
		UserId:     userId,
		Username:   userName,
		Score:      score,
		Recover:    0,
		Operator:   operator,
		CreateTime: time.Now().Unix(),
	})
}

func getUserScoreFromEsV1(index string, userId uint64) (*EsUserCois, error) {
	// build source body
	source := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []map[string]interface{}{
					0: {"term": map[string]interface{}{
						"userid": userId}},
					1: {"term": map[string]interface{}{
						"status": 1}},
				},
			},
		},
		"size": 1,
	}

	res, err := esutil.EsClient.Search(index).Source(source).Do(context.Background())
	if err != nil {
		gamelog.Error("getUserScoreFromEsV1 failed", gamelog.Fields{"err": err, "uid": userId, "index": index})
		return nil, err
	}

	for index, v := range res.Hits.Hits {
		var temp EsUserCois
		if err := json.Unmarshal(*v.Source, &temp); err != nil {
			gamelog.Error("json unmarshal es source err", map[string]interface{}{
				"index":  index,
				"userId": userId,
				"err":    err,
			})
			continue
		}

		return &temp, nil
	}

	return nil, recordNotFound

}

func getUserScoreFromEsV2(index, rankName string, userId uint64) (*EsUserCoinsV2, error) {
	// build source body
	source := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []map[string]interface{}{
					0: {"term": map[string]interface{}{
						"rank_name": rankName}},
					1: {"term": map[string]interface{}{
						"status": 1}},
					2: {"term": map[string]interface{}{
						"user_id": userId}},
				},
			},
		},
		"size": 1,
	}

	res, err := esutil.EsClient.Search(index).Source(source).Do(context.Background())
	if err != nil {
		gamelog.Error("getUserScoreFromEsV2 failed", gamelog.Fields{"err": err, "uid": userId, "index": index})
		return nil, err
	}

	for index, v := range res.Hits.Hits {
		var temp EsUserCoinsV2
		if err := json.Unmarshal(*v.Source, &temp); err != nil {
			gamelog.Error("json unmarshal es source err", map[string]interface{}{
				"index":    index,
				"rankName": rankName,
				"userId":   userId,
				"err":      err,
			})
			continue
		}

		return &temp, nil
	}

	return nil, recordNotFound
}
