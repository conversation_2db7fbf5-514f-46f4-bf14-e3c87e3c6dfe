package handler

import (
	"git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"strconv"
	"sync/atomic"
	"time"
)

const (
	METRIC_API_N_TOO_BIG = "game_rank_n_too_big"

	METRIC_CHAN_FULL_TOTAL         = "game_rank_chan_full_total"
	METRIC_CHAN_COMSUME_STATISTIC  = "game_rank_chan_comsume_statistic"
	METRIC_CHAN_CONSUME_TOPNKEY    = "game_rank_chan_consume_topNKey"
	METRIC_CHAN_CONSUME_BOTTOMNKEY = "game_rank_chan_consume_bottomNKey"
	METRIC_CHANN_SIZE              = "game_rank_chan_size"
	METRIC_HELP_GOROUTINE_SIZE     = "game_rank_help_goroutine_size"

	METRIC_INIT_TOPN_KEY = "game_rank_init_topn_key"
)

const monitorSleepTime = time.Second * 5

var (
	helpRankGoroutines     = int64(0)
	helpUserInfoGoroutines = int64(0)
)

func RedisChanSizeMonitor() {
	defer func() {
		if r := recover(); r != nil {
			gamelog.Error("[panic recovery]RedisChanSizeMonitor", gamelog.Fields{"r": r})
		}
	}()

	for {
		for i := 0; i < len(RankWriter.rankChan); i++ {
			length := len(RankWriter.rankChan[i])
			err := reporter.ReportGauge(
				METRIC_CHANN_SIZE,
				float64(length),
				reporter.Label{"index", strconv.FormatInt(int64(i), 10)},
				reporter.Label{"type", "ranChan"})
			HandlerReportErr(err, METRIC_CHANN_SIZE)
		}

		/*
			for i := 0; i < len(RankWriter.infoChan); i++ {
				length := len(RankWriter.infoChan[i])
				err := reporter.ReportGauge(
					METRIC_CHANN_SIZE,
					float64(length),
					reporter.Label{"index", strconv.FormatInt(int64(i), 10)},
					reporter.Label{"type", "infoChan"})
				HandlerReportErr(err, METRIC_CHANN_SIZE)
			}*/
		time.Sleep(monitorSleepTime)
	}
}

func EsChanSizeMonitor() {
	defer func() {
		if r := recover(); r != nil {
			gamelog.Error("[panic recovery]EsChanSizeMonitor", gamelog.Fields{"r": r})
		}
	}()

	for {
		length := len(esChan)
		err := reporter.ReportGauge(
			METRIC_CHANN_SIZE,
			float64(length),
			reporter.Label{"index", "0"},
			reporter.Label{"type", "esChan"})
		HandlerReportErr(err, METRIC_CHANN_SIZE)
		time.Sleep(monitorSleepTime)
	}
}

func HelpGoroutineSizeMonitor() {
	defer func() {
		if r := recover(); r != nil {
			gamelog.Error("[panic recovery]HelpGoroutineSizeMonitor", gamelog.Fields{"r": r})
		}
	}()

	for {
		select {
		case <-time.After(monitorSleepTime):
			_ = reporter.ReportGauge(METRIC_HELP_GOROUTINE_SIZE, float64(atomic.LoadInt64(&helpRankGoroutines)),
				reporter.Label{Key: "type", Val: "rank"})

			//_ = reporter.ReportGauge(METRIC_HELP_GOROUTINE_SIZE, float64(atomic.LoadInt64(&helpUserInfoGoroutines)),
			//	reporter.Label{Key: "type", Val: "userInfo"})
		}
	}
}

func HandlerReportErr(err error, metric string) {
	if err != nil {
		gamelog.Error("monitor report err", map[string]interface{}{
			"metric": metric,
			"err":    err,
		})
	}
}
