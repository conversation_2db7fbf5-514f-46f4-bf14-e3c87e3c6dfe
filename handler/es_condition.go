package handler

type RankEsCondition struct {
	Offset    uint64
	Limit     uint64
	UserId    uint64
	UserName  string
	RankValue float32
	RankNum   uint64
	ScrollId  string
	RobotId   int64
}

func (r *RankEsCondition) GetOffset() uint64 {
	if r != nil {
		return r.Offset
	}
	return 0
}

func (r *RankEsCondition) GetLimit() uint64 {
	if r != nil {
		return r.Limit
	}
	return 0
}

func (r *RankEsCondition) GetUserId() uint64 {
	if r != nil {
		return r.UserId
	}
	return 0
}

func (r *RankEsCondition) GetUserName() string {
	if r != nil {
		return r.UserName
	}
	return ""
}

func (r *RankEsCondition) GetRankValue() float32 {
	if r != nil {
		return r.RankValue
	}
	return 0
}

func (r *RankEsCondition) GetRankNum() uint64 {
	if r != nil {
		return r.RankNum
	}
	return 0
}

func (r *RankEsCondition) GetScrollId() string {
	if r != nil {
		return r.ScrollId
	}
	return ""
}

func (r *RankEsCondition) GetRobotId() int64 {
	if r != nil {
		return r.RobotId
	}
	return 0
}
