package handler

import (
	"context"
	"git.garena.com/shopee/feed/microkit"
	"git.garena.com/shopee/game_platform/proto/base"
	"git.garena.com/shopee/game_platform/proto/game_rank"
	"github.com/golang/protobuf/proto"
	"testing"
)

func TestRankHandler_DeleteUserRankV2(t *testing.T) {
	microkit.Init()
	cli := game_rank.NewClient()

	r1, err := cli.IncrScoreV3(context.Background(), &game_rank.IncrScoreRequestV3{
		Base: &base.CommonRequest{
			AppId: proto.String("j6gOxnBmZhMIyQ1Yea"),
			//RankName:             proto.String("103837"),
			//RankType:             proto.Uint32(1),
		},
		RankId:   proto.Int32(1),
		Score:    proto.Float64(100),
		UserName: proto.String("fs"),
		UserId:   proto.Uint64(10086),
	})

	if err != nil {
		t.Error(err)
	}

	t.Log(r1)
}

func TestAA(t *testing.T) {
	microkit.Init()
	batchGetUserBaseInfo(context.Background(), []uint64{1222772381})
}
