package handler

import (
	"context"
	"testing"

	"git.garena.com/shopee/game/grpc4spex"
	"git.garena.com/shopee/game_platform/proto/base"
	rank_server "git.garena.com/shopee/game_platform/proto/game_rank"
	"github.com/golang/protobuf/proto"
)

func TestRankSpex(t *testing.T) {
	grpc4spex.Init(grpc4spex.WithSpexConfig("game.rank", "ef63eebc9852132608d79b088bbed329", "3d3864fae38757af72729ead665f8799"))
	conn, err := grpc4spex.GetConn() // get a connection from gprc4spex
	if err != nil {
		panic(err)
	}
	client := rank_server.NewRankClient(conn.ClientConn) // create a grpc client

	appId := "GVS1I8Xs5pJ7JzfYlg"
	userId := uint64(757397502)
	req := &rank_server.SetScoreRequestV3{
		Base: &base.CommonRequest{
			AppId: proto.String(appId),
		},
		RankId: proto.Int32(1),
		UserId: proto.Uint64(userId),
	}

	resp, err := client.SetScoreV3(context.Background(), req)
	if err != nil {
		t.Log(err)
		return
	}
	t.Log(resp)
}
