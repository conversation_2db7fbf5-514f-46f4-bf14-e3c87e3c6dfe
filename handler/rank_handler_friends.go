package handler

import (
	"context"
	"sort"

	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/proto/base"
	"git.garena.com/shopee/game_platform/proto/game_rank"
	"git.garena.com/shopee/game_platform/proto/gameplatform_friend"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
)

const (
	maxContactsLength = 3000
	defaultTopN       = 100
)

type UserData struct {
	UserId   int64
	NickName string
}

type RankInnerItem struct {
	Score     float64
	UserName  string
	UserId    int64
	TimeStamp uint64
	Avatar    string
}

func (s *RankHandler) FriendsRankV3(ctx context.Context, req *game_rank.FriendsRankRequestV3, rsp *game_rank.FriendsRankResponseV3) error {
	appId := req.GetBase().GetAppId()
	rankId := req.GetRankId()
	topN := req.GetN()
	deviceId := req.GetDeviceId()
	userId := req.GetUserId()
	friendType := req.GetFriendType()

	if topN <= 0 {
		topN = defaultTopN
	}

	rsp.Resp = &base.CommonResponse{ErrCode: proto.Int32(0), ErrMsg: proto.String("success")}
	if len(appId) == 0 || rankId == 0 {
		fillBizError(rsp.Resp, ErrParamInvalid)
		return nil
	}

	var userIds []uint64
	friends := getContactFriends(userId, deviceId, appId, uuid.NewString(), friendType)
	friendsMap := make(map[int64]*UserData)
	hasSelf := false
	for ix := range friends {
		item := friends[ix]
		if item.UserId == userId {
			hasSelf = true
		}

		userIds = append(userIds, uint64(item.UserId))
		friendsMap[item.UserId] = friends[ix]
	}

	if !hasSelf {
		userIds = append(userIds, uint64(userId))
	}

	data, err2 := s.QueryFriendsRank(ctx, userIds, rankId, appId)
	if err2 != nil {
		fillBizError(rsp.Resp, err2)
		return nil
	}

	if len(data) == 0 {
		// 无排行榜，说明自己也没分数，设置username和avatar
		mapUserInfo, err := batchGetUserBaseInfo(ctx, []uint64{uint64(userId)})
		if err != nil {
			fillBizError(rsp.Resp, ErrRPC)
			return nil
		}

		if u, ok := mapUserInfo[uint64(userId)]; ok {
			rsp.MyAvatar = proto.String(u.GetPortrait())
			rsp.MyUserName = proto.String(u.GetUserName())
		}

		return nil
	}

	var list []*game_rank.FriendRankInfo

	reach := false
	foundMe := false
	for ix, item := range data {
		if ix >= int(topN) {
			if foundMe {
				break
			}

			reach = true
		}

		if item.UserId == userId {
			foundMe = true

			rsp.MyRank = proto.Uint64(uint64(ix + 1))
			rsp.MyRawRank = proto.Uint64(uint64(ix + 1))
			rsp.MyScore = proto.Float64(item.Score)
			rsp.MyAvatar = proto.String(item.Avatar)
			rsp.MyUserName = proto.String(item.UserName)
		}

		if reach {
			continue
		}

		nickname := ""
		if entry, ok := friendsMap[item.UserId]; ok {
			nickname = entry.NickName
		}

		list = append(list, &game_rank.FriendRankInfo{
			RankNum:  proto.Uint64(uint64(ix + 1)),
			Score:    proto.Float64(item.Score),
			UserName: proto.String(item.UserName),
			NickName: proto.String(nickname),
			Avatar:   proto.String(item.Avatar),
			UserId:   proto.Uint64(uint64(item.UserId)),
		})
	}

	rsp.RankList = list
	return nil
}

func (s *RankHandler) QueryFriendsRank(ctx context.Context, userIds []uint64, rankId int32, appId string) ([]*RankInnerItem, *BizError) {
	var items []*RankInnerItem
	if len(userIds) == 0 {
		return items, nil
	}

	req := &game_rank.UserScoreListRequestV3{
		Base: &base.CommonRequest{
			AppId: proto.String(appId),
		},
		RankId:  proto.Int32(rankId),
		UserIds: userIds,
		Detail:  proto.Bool(true),
	}
	rsp := &game_rank.UserScoreListResponseV3{}
	if err := s.UserScoreListV3(ctx, req, rsp); err != nil {
		gamelog.Error("QueryFriendsRank|UserScoreListV3 error", gamelog.Fields{"error": err, "rankId": rankId, "appId": appId})
		return nil, ErrRPC
	}
	if c := rsp.GetResp().GetErrCode(); c != 0 {
		gamelog.Error("QueryMyRank|UserScoreV3 failed", gamelog.Fields{"appId": appId, "rankId": rankId, "rsp": rsp})
		return nil, &BizError{c, rsp.GetResp().GetErrMsg()}
	}

	for _, item := range rsp.GetItems() {
		items = append(items, &RankInnerItem{
			Score:     item.GetScore(),
			UserName:  item.GetUserName(),
			UserId:    int64(item.GetUserId()),
			TimeStamp: item.GetWeekTimeStamp(),
			Avatar:    item.GetAvatar(),
		})
	}

	sort.Slice(items, func(i, j int) bool {
		if items[i].Score == items[j].Score {
			return items[i].TimeStamp > items[j].TimeStamp
		} else {
			return items[i].Score > items[j].Score
		}

	})

	return items, nil
}

func getContactFriends(userId int64, deviceId, appId, logId string, friendType game_rank.FriendType) []*UserData {
	offset := int32(0)
	limit := int32(1000)

	rt := make([]int32, 0)
	switch friendType {
	case game_rank.FriendType_Unspecified:
		rt = append(rt, int32(gameplatform_friend.RelationType_RELATION_UNSPECIFIED))
	case game_rank.FriendType_RecentChat:
		rt = append(rt, int32(gameplatform_friend.RelationType_RELATION_RECENT_CHAT))
	case game_rank.FriendType_All:
		// no add filter
	default:
		rt = append(rt, int32(gameplatform_friend.RelationType_RELATION_USER_CONTACT))
	}

	brief := int32(0)
	var friendsInfo []*UserData
	for {
		resp, err := GetFriends(logId, appId, deviceId, uint64(userId),
			int32(gameplatform_friend.SortType_SORT_USERNAME_ASC_ENG_LANG), offset, limit, brief,
			rt, nil, nil)

		if err != nil || resp.GetBase().GetErrCode() != 0 {
			gamelog.Error("getContactFriends failed", gamelog.Fields{"err": err, "userId": userId, "offset": offset, "resp": resp})
			break
		}

		gamelog.Info("getContactFriends|GetFriends result", gamelog.Fields{"userId": userId, "deviceId": deviceId, "resp": resp, "offset": offset})
		for _, v := range resp.GetFriends() {
			friendsInfo = append(friendsInfo, &UserData{
				UserId:   int64(v.GetUserId()),
				NickName: v.GetContactName(),
			})
		}

		if int32(len(friendsInfo)) >= resp.GetTotal() || len(resp.GetFriends()) == 0 {
			break
		}

		// in case of large contact list
		if int32(len(friendsInfo)) >= maxContactsLength {
			gamelog.Info("large contact list", gamelog.Fields{"uid": userId, "total": resp.GetTotal()})
			break
		}

		offset = offset + limit
	}

	return friendsInfo
}

func GetFriends(logId, appId, deviceId string, userId uint64, sortType, offset, limit, brief int32, relationTypes []int32, sortAppId []string, sortRelationTypes []int32) (*gameplatform_friend.GetFriendsResponse, error) {
	req := &gameplatform_friend.GetFriendsRequest{
		Base: &base.CommonRequest{
			AppId:   proto.String(appId),
			TraceId: proto.String(logId),
		},
		UserId:            proto.Uint64(userId),
		SortType:          proto.Int32(sortType),
		DeviceId:          []byte(deviceId),
		Offset:            proto.Int32(offset),
		Limit:             proto.Int32(limit),
		RelationTypes:     relationTypes,
		Brief:             proto.Int32(brief),
		SortAppId:         sortAppId,
		SortRelationTypes: sortRelationTypes,
	}

	cli := gameplatform_friend.GetGrpcClient()

	resp, err := cli.GetFriends(context.Background(), req)
	gamelog.Info("debug info GetFriends", gamelog.Fields{"req": req, "resp": resp, "err": err})

	if err != nil {
		gamelog.Error("GetFriends rpc failed", gamelog.Fields{"err": err})
		return nil, err
	}
	return resp, nil
}
