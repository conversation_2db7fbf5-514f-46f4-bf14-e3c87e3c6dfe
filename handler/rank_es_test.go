package handler

import (
	"fmt"
	"git.garena.com/shopee/feed/comm_lib/reporter"
	"testing"
)

func TestEsWeekRankIndex(t *testing.T) {
	index := EsWeekRankIndex(123456, "2019.22")
	fmt.Printf("index : %s \n", index)
}

func TestDesIDs(t *testing.T)  {
	err := reporter.ReportCounter("chenhao", 1, reporter.Label{
		Key: "one",
		Val: "1",
	}, reporter.Label{
		Key: "two",
		Val: "2",
	})

	fmt.Println(err)

	err = reporter.ReportCounter("chenhao", 1, reporter.Label{
		Key: "three",
		Val: "3",
	}, reporter.Label{
		Key: "four",
		Val: "4",
	})

	fmt.Println(err)

}

func TestCreateExistIndex(t *testing.T)  {
	err := InitElasticWriter(EsUserCoisChanSize)
	if err != nil {
		return
	}
	createNonLiveIndex("mkt_games_1mwdotgpn7tlnpe2eu_test_th_rank_new.76_2023.7")
}
