package handler

import (
	"git.garena.com/shopee/game_platform/rank_server/dao/cache"
	"sort"
)

type ZsetElemSortWrapper struct {
	Data     []cache.ZsetElem
	LessFunc ZsetElemOrderBy
}

type ZsetElemOrderBy func(e1, e2 *cache.ZsetElem) bool

func (wrapper ZsetElemSortWrapper) Len() int {
	return len(wrapper.Data)
}

func (wrapper ZsetElemSortWrapper) Swap(i, j int) {
	wrapper.Data[i], wrapper.Data[j] = wrapper.Data[j], wrapper.Data[i]
}

func (wrapper ZsetElemSortWrapper) Less(i, j int) bool {
	return wrapper.LessFunc(&wrapper.Data[i], &wrapper.Data[j])
}

func sortZsetElem(data []cache.ZsetElem, less ZsetElemOrderBy) {
	sort.Sort(ZsetElemSortWrapper{data, less})
}

func SortZsetElemAsc(data []cache.ZsetElem) {
	sortZset<PERSON>lem(data, OrderbyAsc)
}

func SortZsetElem(data []cache.ZsetElem) {
	sortZsetElem(data, Orderby)
}

func Orderby(e1, e2 *cache.ZsetElem) bool {
	return e1.Score > e2.Score
}

func OrderbyAsc(e1, e2 *cache.ZsetElem) bool {
	return e1.Score < e2.Score
}
