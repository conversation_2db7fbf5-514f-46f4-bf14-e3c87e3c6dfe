package handler

import (
	"context"
	"git.garena.com/shopee/game_platform/proto/game_rank"
	"testing"
)

func TestListNewRankModule(t *testing.T) {
	h := RankHandler{}
	rsp := &game_rank.ListNewRankModuleResp{}
	err := h.ListNewRankModule(context.TODO(), &game_rank.ListNewRankModuleReq{
		//Base:   nil,
		RankId: []int32{1, 2, 3, 4},
	}, rsp)

	if err != nil {
		t.Log(err)
		return
	}

	t.Log(rsp)
}
