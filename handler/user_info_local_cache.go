package handler

import (
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/proto/gameplatform_friend"
	rankConfig "git.garena.com/shopee/game_platform/rank_server/config"
	"sync"
	"time"
)

var (
	localCachedUserInfoMap sync.Map
	noExistUserMap         sync.Map
)

type CustomUserInfo struct {
	t        int64
	userInfo *gameplatform_friend.UserBaseInfo
}

func GetUserInfoFromLocalCache(uids []uint64) ([]*gameplatform_friend.UserBaseInfo, []uint64) {
	cachedUsers := make([]*gameplatform_friend.UserBaseInfo, 0, len(uids)/2)
	missCachedUsers := make([]uint64, 0, len(uids)/2)

	for _, tmpUid := range uids {
		tmpUserInfoI, ok := localCachedUserInfoMap.Load(tmpUid)
		if ok {
			tmpInfo, okI := tmpUserInfoI.(*CustomUserInfo)
			if okI {
				cachedUsers = append(cachedUsers, tmpInfo.userInfo)
			}
			continue
		}

		_, ok = noExistUserMap.Load(tmpUid)
		if !ok {
			// 没有标记过不存在
			missCachedUsers = append(missCachedUsers, tmpUid)
			continue
		}
	}

	gamelog.Debug("GetUserInfoFromLocalCache end", gamelog.Fields{"cached": len(cachedUsers), "missed": len(missCachedUsers)})
	return cachedUsers, missCachedUsers
}

func SetUserInfoToLocalCache(missCachedUsers []uint64, existMap map[uint64]*gameplatform_friend.UserBaseInfo) {
	timeNow := time.Now().Unix()
	for _, tmpUid := range missCachedUsers {
		tmpValue, ok := existMap[tmpUid]
		if !ok || tmpValue == nil || len(tmpValue.GetUserName()) == 0 {
			// 头像可能没有
			tmpInfo := &CustomUserInfo{
				t: timeNow,
			}
			noExistUserMap.Store(tmpUid, tmpInfo)
		} else {
			tmpInfo := &CustomUserInfo{
				t:        timeNow,
				userInfo: tmpValue,
			}
			localCachedUserInfoMap.Store(tmpUid, tmpInfo)
		}
	}
}

func RunCronOfUserInfoLocalCache() {
	timer := time.NewTicker(time.Second * time.Duration(rankConfig.GetLocalCacheDelCronTimeTicker()))
	for {
		select {
		case <-timer.C:
			ClearUserInfoLocalCache()
		}
	}
}

// 定时删除
func ClearUserInfoLocalCache() {
	var deleteUserCount, deleteNoExistCount int

	timeNow := time.Now().Unix()
	localCachedUserInfoMap.Range(func(key, value interface{}) bool {
		tmpInfo, ok := value.(*CustomUserInfo)
		if !ok {
			gamelog.Error("tmpInfo is not CustomUserInfo???", gamelog.Fields{"value": value})
			return true
		}
		if timeNow > tmpInfo.t+rankConfig.GetLocalCacheExpireMinutes()*60 {
			localCachedUserInfoMap.Delete(key)
			deleteUserCount++
		}
		return true
	})

	noExistUserMap.Range(func(key, value interface{}) bool {
		tmpInfo, ok := value.(*CustomUserInfo)
		if !ok {
			gamelog.Error("tmpInfo is not CustomUserInfo???", gamelog.Fields{"value": value})
			return true
		}
		if timeNow > tmpInfo.t+rankConfig.GetLocalCacheExpireMinutes()*60 {
			noExistUserMap.Delete(key)
			deleteNoExistCount++
		}
		return true
	})

	gamelog.Info("ClearUserInfoLocalCache End", gamelog.Fields{"deleteUserCount": deleteUserCount, "deleteNoExistCount": deleteNoExistCount})
}
