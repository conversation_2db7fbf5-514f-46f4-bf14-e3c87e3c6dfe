package handler

import (
	"context"
	"fmt"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/comm_lib/metrics"
	"git.garena.com/shopee/game_platform/rank_server/dao/cache"

	"strconv"
	"time"
)

const (
	RetentionRankInitLockFmt = "RR_init_lock_%s_%s"
	RankLockFmt              = "rank_lock_%s_%s_%d_%d"
	RankLockExpireSecond     = 10

	LockRetryInterval = 50
	LockRetryTime     = 5

	MetricRankLockFailTotal              = "rank_lock_fail_total"
	MetricRetentionRankInitLockFailTotal = "RR_init_lock_fail_total"
)

func tryRankLock(rank *Rankings) bool {
	appId := rank.Scope
	rankName := rank.RankName
	rankType := rank.RankType
	userId := rank.Userid

	key := fmt.Sprintf(RankLockFmt, appId, rankName, rankType, userId)

	for i := 0; i < LockRetryTime; i++ {
		ok, err := cache.RedisUtilSetNX(context.Background(), key, 1, time.Duration(RankLockExpireSecond)*time.Second)
		if err != nil {
			gamelog.Error("tryRankLock error", gamelog.Fields{"err": err, "rank": rank})
			return false
		}

		if ok {
			return true
		}

		metrics.ReportCounterWithLabels(MetricRankLockFailTotal, map[string]string{"app_id": appId, "rank_name": rankName, "retry": strconv.Itoa(i + 1)})
		time.Sleep(time.Millisecond * time.Duration(LockRetryInterval))
	}

	return false
}

func releaseRankLock(rank *Rankings) {
	appId := rank.Scope
	rankName := rank.RankName
	rankType := rank.RankType
	userId := rank.Userid

	key := fmt.Sprintf(RankLockFmt, appId, rankName, rankType, userId)
	_, err := cache.RedisUtilDelete(context.Background(), key)
	if err != nil {
		gamelog.Error("releaseRankLock error", gamelog.Fields{"err": err, "app_id": appId, "rank_name": rankName})
	}
}

func tryRetentionRankInitLock(ctx context.Context, scope, rankName string) bool {
	key := fmt.Sprintf(RetentionRankInitLockFmt, scope, rankName)
	for i := 0; i < LockRetryTime; i++ {
		ok, err := cache.RedisUtilSetNX(ctx, key, 1, time.Duration(RankLockExpireSecond)*time.Second)
		if err != nil {
			gamelog.Error("tryRetentionRankInitLock error", gamelog.Fields{"err": err, "key": key})
			return false
		}

		if ok {
			return true
		}

		metrics.ReportCounterWithLabels(MetricRetentionRankInitLockFailTotal, map[string]string{"scope": scope, "rank_name": rankName, "retry": strconv.Itoa(i + 1)})
		time.Sleep(time.Millisecond * time.Duration(LockRetryInterval))
	}

	return false
}

func releaseRetentionRankLock(scope, rankName string) {
	key := fmt.Sprintf(RetentionRankInitLockFmt, scope, rankName)
	_, err := cache.RedisUtilDelete(context.Background(), key)
	if err != nil {
		gamelog.Error("releaseRetentionRankLock error", gamelog.Fields{"err": err, "scope": scope, "rank_name": rankName})
	}
}
