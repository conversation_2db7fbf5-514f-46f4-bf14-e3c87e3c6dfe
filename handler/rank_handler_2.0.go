package handler

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/game_platform/comm_lib/anticheat_rpc"
	"git.garena.com/shopee/game_platform/comm_lib/esutil"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/comm_lib/metrics"
	"git.garena.com/shopee/game_platform/comm_lib/utils"
	"git.garena.com/shopee/game_platform/proto/game_rank"
	"git.garena.com/shopee/game_platform/proto/gameplatform_friend"
	"git.garena.com/shopee/game_platform/proto/gameplatform_game"
	rankConfig "git.garena.com/shopee/game_platform/rank_server/config"
	"git.garena.com/shopee/game_platform/rank_server/constant"
	"git.garena.com/shopee/game_platform/rank_server/dao"
	"git.garena.com/shopee/game_platform/rank_server/dao/cache"
	"git.garena.com/shopee/game_platform/rank_server/util"
	spiCache "git.garena.com/shopee/mts/go-application-server/spi/cache"
	"github.com/golang/protobuf/proto"
	"github.com/olivere/elastic"
)

var (
	CacheWrapper = utils.NewCacheWrapper(5*time.Minute, 24*time.Hour, 1*time.Second)
)

type tempUserScoreListV2Struct struct {
	Uid      uint64
	ScoreCmd spiCache.FloatCmd
}

// SetScore async set member score
func (s *RankHandler) SetScoreV2(ctx context.Context, req *game_rank.SetScoreRequestV2, rsp *game_rank.CommonResponse) error {
	if util.CheckUserInBlacklist(ctx, int64(req.GetUserId()), req.GetReq().GetScope(), rsp) {
		return nil
	}

	if err := checkRankWeekOrFill(req.GetReq()); err != nil {
		rsp.ErrCode = proto.Int32(100)
		rsp.ErrMsg = proto.String("rank week err or rank type not support")
		return nil
	}

	timestamp, err := utils.GetTimeStampMilli()
	if err != nil {
		rsp.ErrCode = proto.Int32(100)
		rsp.ErrMsg = proto.String("server get timestamp error")
		return nil
	}

	// build rank redis rankName
	redisRankName := concatRankNameAndRankWeek(req.GetReq().GetRankName(), req.GetReq().GetRankWeek())
	gamelog.Debug("SetScoreV2 debug", gamelog.Fields{"redisRankName": redisRankName, "req": req})
	// put into rank chan
	RankWriter.putRankChan(
		Rankings{
			Scope:         req.GetReq().GetScope(),
			RankName:      redisRankName,
			Username:      fillUserName(ctx, req.GetUserName(), req.GetUserId()),
			Userid:        req.GetUserId(),
			Score:         req.GetScore(),
			Timestamp:     timestamp,
			RankType:      int32(req.GetReq().GetRankType()),
			IsSetScoreReq: true,
		})
	// put into info chan
	// RankWriter.putInfoChan(userinfo{req.GetUserName(), req.GetUserId(), int32(req.GetReq().GetRankType())})

	rsp.ErrCode = proto.Int32(0)
	rsp.ErrMsg = proto.String("ok")
	return nil

}

// IncrScore async add member score
func (s *RankHandler) IncrScoreV2(ctx context.Context, req *game_rank.IncrScoreRequestV2, rsp *game_rank.CommonResponse) error {
	if req.GetReq().GetFlowType() == constant.FlowTypeAdmin {
		rankWeek := AdminCheckAndFillRankWeek(req.GetReq().GetScope(), req.GetReq().GetRankType(), req.GetReq().GetRankWeek())
		if len(rankWeek) == 0 {
			rsp.ErrCode = proto.Int32(102)
			rsp.ErrMsg = proto.String("fill rank_week failed")
			return nil
		}
		req.GetReq().RankWeek = proto.String(rankWeek)
	} else {
		if err := checkRankWeekOrFill(req.GetReq()); err != nil {
			rsp.ErrCode = proto.Int32(100)
			rsp.ErrMsg = proto.String("rank week err or rank type not support")
			return nil
		}

		if util.CheckUserInBlacklist(ctx, int64(req.GetUserId()), req.GetReq().GetScope(), rsp) {
			return nil
		}
	}

	timestamp, err := utils.GetTimeStampMilli()
	if err != nil {
		rsp.ErrCode = proto.Int32(100)
		rsp.ErrMsg = proto.String("server get timestamp error")
		return nil
	}
	// build rank redis rankName
	redisRankName := concatRankNameAndRankWeek(req.GetReq().GetRankName(), req.GetReq().GetRankWeek())
	gamelog.Debug("IncrScoreV2 debug", gamelog.Fields{"redisRankName": redisRankName, "req": req})

	// put into rank chan
	RankWriter.putRankChan(
		Rankings{
			Scope:         req.GetReq().GetScope(),
			RankName:      redisRankName,
			Username:      fillUserName(ctx, req.GetUserName(), req.GetUserId()),
			Userid:        req.GetUserId(),
			Score:         req.GetScore(),
			Timestamp:     timestamp,
			RankType:      int32(req.GetReq().GetRankType()),
			IsSetScoreReq: false,
		})
	// put into info chan
	// RankWriter.putInfoChan(userinfo{req.GetUserName(), req.GetUserId(), int32(req.GetReq().GetRankType())})

	rsp.ErrCode = proto.Int32(0)
	rsp.ErrMsg = proto.String("ok")
	return nil
}

// TopNV2 return topn users
func (s *RankHandler) TopNV2(ctx context.Context, req *game_rank.TopNRequestV2, rsp *game_rank.TopnResponse) error {
	var err error

	if req.GetReq().GetFlowType() == constant.FlowTypeAdmin {
		rankWeek := AdminCheckAndFillRankWeek(req.GetReq().GetScope(), req.GetReq().GetRankType(), req.GetReq().GetRankWeek())
		if len(rankWeek) == 0 {
			rsp.Resp = &game_rank.CommonResponse{
				ErrCode: proto.Int32(102),
				ErrMsg:  proto.String("fill rank_week failed"),
			}
			return nil
		}
		req.GetReq().RankWeek = proto.String(rankWeek)
	} else {
		if err = checkRankWeekOrFill(req.GetReq()); err != nil {
			rsp.Resp = &game_rank.CommonResponse{
				ErrCode: proto.Int32(100),
				ErrMsg:  proto.String("rank week err or rank type not support"),
			}
			return nil
		}
	}

	// N
	if req.GetN() > MaxUserRank+1 && rankConfig.IsTopNReturnErr() {
		nString := strconv.FormatInt(int64(req.GetN()), 10)
		_ = reporter.ReportCounter(METRIC_API_N_TOO_BIG, 1, reporter.Label{"n", nString})

		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(100),
			ErrMsg:  proto.String("N > 10000"),
		}
		return nil
	}

	// build rank redis rankName
	redisRankName := concatRankNameAndRankWeek(req.GetReq().GetRankName(), req.GetReq().GetRankWeek())
	nameWithoutBucket := scopeRankName(req.GetReq().GetScope(), redisRankName)
	gamelog.Debug("TopNV2 debug", gamelog.Fields{"nameWithoutBucket": nameWithoutBucket, "req": req})

	var res []cache.ZsetElem
	if IsUseTop10kKey(int32(req.GetReq().GetRankType())) {
		res, err = QueryTopNFromSingleKey(ctx, req.GetReq().GetScope(), redisRankName, req.GetN(), req.GetReq().GetRankType(), req.GetReverse(), true)
	} else {
		res, err = allbucketZSetRange(nameWithoutBucket, req.GetN(), req.GetReverse(), conf.rankRedisBucket, true, false)
	}

	if err != nil {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(100),
			ErrMsg:  proto.String(err.Error()),
		}
		return nil
	}

	rsp.Resp = &game_rank.CommonResponse{
		ErrCode: proto.Int32(0),
		ErrMsg:  proto.String("ok"),
	}

	scope := req.GetReq().GetScope()

	// res N*bucketNum
	// finalResRankList N
	filterRes := filterBlackRankListTopN(ctx, scope, res, req.GetN(), nameWithoutBucket)
	uids := transZElem2UserIds(filterRes)
	rsp.Items = []*game_rank.RankItem{}
	var finalResRankList []*game_rank.RankItem
	for _, v := range filterRes {
		if v.Value == "" {
			continue
		}

		userId, err := strconv.ParseUint(v.Value, 10, 64)
		if err != nil {
			rsp.Resp = &game_rank.CommonResponse{
				ErrCode: proto.Int32(100),
				ErrMsg:  proto.String(err.Error()),
			}
			return nil
		}

		score, weekTimeStamp := SplitFloat32AndTimestamp(v.Score)
		finalResRankList = append(finalResRankList, &game_rank.RankItem{
			Score:         proto.Float64(score),
			UserId:        proto.Uint64(userId),
			WeekTimeStamp: proto.Uint64(uint64(weekTimeStamp)),
		})
	}

	if req.GetReq().GetRankType() == constant.RankTypeRetention {
		finalResRankList = ReSortByUserTime(finalResRankList, uids, req.GetReq(), req.GetReverse(), req.GetN())
	}

	finalUids := transRankItem2UserIds(finalResRankList)
	userInfos, err := batchGetUserBaseInfo(ctx, finalUids)
	if err != nil {
		gamelog.ErrorWithContext(ctx, "BatchGetUserBaseInfo error", gamelog.Fields{"scope": scope, "err": err})
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(100),
			ErrMsg:  proto.String(err.Error()),
		}
		return nil
	}

	for i := 0; i < len(finalResRankList); i++ {
		userId := finalResRankList[i].GetUserId()
		user, ok := userInfos[userId]
		if !ok || user == nil {
			//gamelog.Info("username not found", gamelog.Fields{"uid": userId})
		} else {
			finalResRankList[i].UserName = proto.String(user.GetUserName())
			finalResRankList[i].Avatar = proto.String(user.GetPortrait())
		}
	}

	rsp.Items = finalResRankList

	return nil
}

// DownLoadTopNV2 return topn users
func (s *RankHandler) DownLoadTopNV2(ctx context.Context, req *game_rank.TopNRequestV2, rsp *game_rank.TopnResponse) error {
	// this interface is called by admin, so just use rank list instead
	return nil
}

// UserRankV2 get rank for a member
func (s *RankHandler) UserRankV2(ctx context.Context, req *game_rank.UserRankRequestV2, rsp *game_rank.UserRankResponse) error {
	rsp.Resp = &game_rank.CommonResponse{}
	if util.CheckUserInBlacklist(ctx, int64(req.GetUserId()), req.GetReq().GetScope(), rsp.Resp) {
		return nil
	}

	if err := checkRankWeekOrFill(req.GetReq()); err != nil {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(100),
			ErrMsg:  proto.String("rank week err or rank type not support"),
		}
		return nil
	}

	member := strconv.FormatUint(req.GetUserId(), 10)
	// build rank redis rankName
	redisRankName := concatRankNameAndRankWeek(req.GetReq().GetRankName(), req.GetReq().GetRankWeek())
	key, name := userIdHashBucketRank(req.GetReq().GetScope(), redisRankName, req.GetUserId(), conf.rankRedisBucket)
	gamelog.Debug("UserRankV2 debug", gamelog.Fields{"key": key, "name": name, "req": req})

	var rank int64
	var err error
	if IsUseTop10kKey(int32(req.GetReq().GetRankType())) {
		rank, err = QueryUserRankV2(ctx, req.GetUserId(), req.GetReq().GetScope(), redisRankName, int32(req.GetReq().GetRankType()), req.GetReverse())
	} else {
		member := strconv.FormatUint(req.GetUserId(), 10)

		// get rank
		if req.GetReverse() {
			rank, err = cache.RedisUtilZRank(ctx, key, member)
		} else {
			rank, err = cache.RedisUtilZRank(ctx, key, member)
		}
	}
	if err == spiCache.RedisNil {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(101),
			ErrMsg:  proto.String("not found"),
		}
		return nil
	}

	if err != nil {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(100),
			ErrMsg:  proto.String(err.Error()),
		}
		return nil
	}

	if IsUseTop10kKey(int32(req.GetReq().GetRankType())) {
		// 如果是retention的前100，则resort再精确一下。
		if rank < TopNMaxRank && req.GetReq().GetRankType() == constant.RankTypeRetention {
			res, err := QueryTopNFromSingleKey(ctx, req.GetReq().GetScope(), redisRankName, TopNMaxRank, constant.RankTypeRetention, req.GetReverse(), true)
			if err != nil {
				rsp.Resp = &game_rank.CommonResponse{
					ErrCode: proto.Int32(100),
					ErrMsg:  proto.String(err.Error()),
				}
				return nil
			}
			itemList := ReSortByUserTime(transZElem2RankItem(res), transZElem2UserIds(res), req.GetReq(), req.GetReverse(), TopNMaxRank)
			for tmpRank, tmpItem := range itemList {
				if tmpItem.GetUserId() == req.GetUserId() {
					gamelog.Info("ReSortByUserTime Debug", gamelog.Fields{"uid": req.GetUserId(), "req": req.GetReq(), "rank": rank, "tmpRank": tmpRank})
					rank = int64(tmpRank) + 1
					break
				}
			}
		}

		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(0),
			ErrMsg:  proto.String("ok"),
		}
		rsp.Rank = proto.Int32(int32(rank))
		rsp.RawRank = proto.Int64(rank)
		return nil
	}

	scope := req.GetReq().GetScope()
	isCheckBlack, _ := anticheat_rpc.CheckBlacklistOpen(ctx, gameplatform_game.GameGrpcClient, scope, anticheat_rpc.BizRank)
	if rank < UserRankThreshold {
		// isCheckBlack=true时，需要过滤黑名单，拿到全部数据 needCut=false
		needCut := !isCheckBlack
		// preciseRank
		res, err := allbucketZSetRange(name, TopNMaxRank, req.GetReverse(), conf.rankRedisBucket, true, needCut)
		if err != nil {
			rsp.Resp = &game_rank.CommonResponse{
				ErrCode: proto.Int32(100),
				ErrMsg:  proto.String(err.Error()),
			}
			return nil
		}

		if req.GetReq().GetRankType() == constant.RankTypeRetention {
			itemList := ReSortByUserTime(transZElem2RankItem(res), transZElem2UserIds(res), req.GetReq(), req.GetReverse(), TopNMaxRank)
			res = transRankItem2ZElem(itemList)
		}

		// get score
		redisScore, err := cache.RedisUtilZScore(ctx, key, member)
		if err == spiCache.RedisNil {
			rsp.Resp = &game_rank.CommonResponse{
				ErrCode: proto.Int32(101),
				ErrMsg:  proto.String("not found"),
			}
			return nil
		}

		if err != nil {
			rsp.Resp = &game_rank.CommonResponse{
				ErrCode: proto.Int32(100),
				ErrMsg:  proto.String(err.Error()),
			}
			return nil
		}

		if isCheckBlack {
			rank = getFilterBlackPreciseRank(ctx, res, member, rank, name, req)
		} else {
			rank = getNormalPreciseRank(res, redisScore, req.GetReverse(), rank, member)
		}

	} else {
		// roughlyRank
		rank = rank * int64(conf.rankRedisBucket)
	}

	rsp.RawRank = proto.Int64(rank)
	// 游戏默认排名
	if rank > MaxUserRank {
		rank = MaxUserRank
	}

	rsp.Resp = &game_rank.CommonResponse{
		ErrCode: proto.Int32(0),
		ErrMsg:  proto.String("ok"),
	}
	rsp.Rank = proto.Int32(int32(rank))

	return nil
}

// UserScoreV2 get score for a member
func (s *RankHandler) UserScoreV2(ctx context.Context, req *game_rank.UserScoreRequestV2, rsp *game_rank.UserScoreResponse) error {
	rsp.Resp = &game_rank.CommonResponse{}
	if util.CheckUserInBlacklist(ctx, int64(req.GetUserId()), req.GetReq().GetScope(), rsp.Resp) {
		return nil
	}

	if err := checkRankWeekOrFill(req.GetReq()); err != nil {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(100),
			ErrMsg:  proto.String("rank week err or rank type not support"),
		}
		return nil
	}

	// build rank redis rankName
	redisRankName := concatRankNameAndRankWeek(req.GetReq().GetRankName(), req.GetReq().GetRankWeek())
	key, _ := userIdHashBucketRank(req.GetReq().GetScope(), redisRankName, req.GetUserId(), conf.rankRedisBucket)
	gamelog.Debug("UserScoreV2 debug", gamelog.Fields{"key": key, "req": req})
	member := strconv.FormatUint(req.GetUserId(), 10)

	redisScore, err := cache.RedisUtilZScore(ctx, key, member)
	if err == spiCache.RedisNil {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(101),
			ErrMsg:  proto.String("not found"),
		}
		return nil
	}

	if err != nil {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(100),
			ErrMsg:  proto.String(err.Error()),
		}
		return nil
	}

	rsp.Resp = &game_rank.CommonResponse{
		ErrCode: proto.Int32(0),
		ErrMsg:  proto.String("ok"),
	}
	score, _ := SplitFloat32AndTimestamp(redisScore)
	rsp.Score = proto.Float64(score)
	return nil
}

// DeleteUserRank delete User score
func (s *RankHandler) DeleteUserRankV2(ctx context.Context, req *game_rank.DeleteUserRankRequestV2, rsp *game_rank.CommonResponse) error {
	if !checkRankTypeIsWeek(req.GetReq().GetRankType()) {
		// TODO global rank operation
		rsp.ErrCode = proto.Int32(101)
		rsp.ErrMsg = proto.String("not support rank type")
		return nil
	}

	rankWeek := AdminCheckAndFillRankWeek(req.GetReq().GetScope(), req.GetReq().GetRankType(), req.GetReq().GetRankWeek())
	if len(rankWeek) == 0 {
		rsp.ErrCode = proto.Int32(102)
		rsp.ErrMsg = proto.String("fill rank_week failed")
		return nil
	}

	// build rank redis rankName
	redisRankName := concatRankNameAndRankWeek(req.GetReq().GetRankName(), rankWeek)
	key, _ := userIdHashBucketRank(req.GetReq().GetScope(), redisRankName, req.GetUserId(), conf.rankRedisBucket)
	gamelog.Debug("DeleteUserRankV2 debug", gamelog.Fields{"key": key, "req": req})
	member := strconv.FormatUint(req.GetUserId(), 10)

	find := false
	zscore, err := cache.RedisUtilZScore(ctx, key, member)
	if err != nil && err != spiCache.RedisNil {
		gamelog.Error("zscore failed", gamelog.Fields{"err": err, "uid": member, "key": key})
		return err
	}
	if err == nil {
		find = true
		gamelog.Info("[v2]zscore success", gamelog.Fields{"req": req, "score": zscore})
	}

	_, err = cache.RedisUtilBatchZRem(ctx, key, member)

	if err != nil && err != spiCache.RedisNil {
		rsp.ErrCode = proto.Int32(100)
		rsp.ErrMsg = proto.String(err.Error())
		return nil
	}

	// 双删新Key
	DeleteUserFromSingleKey(req.GetUserId(), req.GetReq().GetRankType(), req.GetReq().GetScope(), redisRankName, false)

	// add es operation
	go func() {
		if err := SaveUserRecoveryData(req.GetReq().GetScope(), req.GetReq().GetRankName(),
			rankWeek, "", req.GetReq().GetRankType(), req.GetUserId(), uint64(zscore), find == false); err != nil {
			gamelog.Error("SaveUserRecoveryData failed", gamelog.Fields{"err": err, "req": req})
		}
		DeleteRankEsWriter.Write(
			&Rankings{
				Scope:    req.GetReq().GetScope(),
				RankName: redisRankName,
				Userid:   req.GetUserId(),
			})

		timestamp, _ := utils.GetTimeStampMilli()
		DeleteRankDetailEsWriter.Write(
			&Rankings{
				Scope:     req.GetReq().GetScope(),
				RankName:  redisRankName,
				Userid:    req.GetUserId(),
				Timestamp: timestamp,
			})
	}()

	rsp.ErrCode = proto.Int32(0)
	rsp.ErrMsg = proto.String("ok")
	return nil
}

// DeleteRank delete rank
func (s *RankHandler) DeleteRankV2(ctx context.Context, req *game_rank.RankV2CommonRequest, rsp *game_rank.CommonResponse) error {
	if !checkRankTypeIsWeek(req.GetRankType()) {
		// TODO global rank operation
		rsp.ErrCode = proto.Int32(101)
		rsp.ErrMsg = proto.String("not support rank type")
		return nil
	}

	rankWeek := AdminCheckAndFillRankWeek(req.GetScope(), req.GetRankType(), req.GetRankWeek())
	if len(rankWeek) == 0 {
		rsp.ErrCode = proto.Int32(102)
		rsp.ErrMsg = proto.String("fill rank_week failed")
		return nil
	}

	// build rank redis rankName
	redisRankName := concatRankNameAndRankWeek(req.GetRankName(), rankWeek)
	nameWithoutBucket := scopeRankName(req.GetScope(), redisRankName)
	if conf.rankRedisBucket <= 1 {
		rankKey := rankingKey(nameWithoutBucket, nobucket)
		_, err := cache.RedisUtilDelete(ctx, rankKey)
		if err != nil && err != spiCache.RedisNil {
			rsp.ErrCode = proto.Int32(100)
			rsp.ErrMsg = proto.String(err.Error())
			return nil
		}
	} else {
		for i := 0; i < conf.rankRedisBucket; i++ {
			rankKey := rankingKey(nameWithoutBucket, strconv.FormatInt(int64(i), 10))
			_, err := cache.RedisUtilDelete(ctx, rankKey)
			if err != nil && err != spiCache.RedisNil {
				rsp.ErrCode = proto.Int32(100)
				rsp.ErrMsg = proto.String(err.Error())
				return nil
			}
		}
	}

	DeleteUserFromSingleKey(0, req.GetRankType(), req.GetScope(), redisRankName, true)

	// add es operation
	go func() {
		deleteRankAllDataAndIndex(req.GetScope(), req.GetRankName(), rankWeek)

		timestamp, _ := utils.GetTimeStampMilli()
		DeleteRankDetailEsWriter.Write(
			&Rankings{
				Scope:     req.GetScope(),
				RankName:  req.GetRankName(),
				Userid:    0,
				Username:  "special tag: delete all data",
				Timestamp: timestamp,
			})
	}()

	rsp.ErrCode = proto.Int32(0)
	rsp.ErrMsg = proto.String("ok")
	return nil
}

func (s *RankHandler) UserScoreListV2(ctx context.Context, req *game_rank.UserScoreListRequestV2, rsp *game_rank.UserScoreListResponse) (err error) {
	startTime := time.Now()
	defer func() {
		util.AccessLog(startTime, gamelog.Fields{
			"req.commonReq":    req.GetReq(),
			"req.detail":       req.GetDetail(),
			"resp.len(userid)": len(req.GetUserIds()),
			"endpoint":         "UserScoreListV2",
			"err":              err,
		})
	}()

	if err := checkRankWeekOrFill(req.GetReq()); err != nil {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(100),
			ErrMsg:  proto.String("rank week err or rank type not support"),
		}
		return nil
	}

	// build rank redis rankName
	scope := req.GetReq().GetScope()
	redisRankName := concatRankNameAndRankWeek(req.GetReq().GetRankName(), req.GetReq().GetRankWeek())
	nameWithoutBucket := scopeRankName(scope, redisRankName)
	gamelog.Debug("UserScoreListV2 debug", gamelog.Fields{"nameWithoutBucket": nameWithoutBucket, "req": req})
	var scores []*game_rank.RankItem

	users := req.GetUserIds()
	isBlacklistCheck, _ := anticheat_rpc.CheckBlacklistOpen(ctx, gameplatform_game.GameGrpcClient, scope, anticheat_rpc.BizRank)
	blackUserIds := make([]uint64, 0)
	m := make(map[uint64]struct{})

	if isBlacklistCheck {
		m = BatchGetBlacklistUserIdMap(ctx, scope, users)
	}

	pipeline := cache.GetPipeline()

	length := len(users)
	if length > constant.UserLimit {
		gamelog.Info("UserScoreListV2 user length reach limit", gamelog.Fields{"length": length, "scope": scope})
		metrics.ReportCounterWithLabels(constant.MetricUserLimitTotal, map[string]string{
			"func":  "UserScoreListV2",
			"appid": scope,
		})
	}

	// conn.Send("MULTI") //not support multi exec...
	prefix := RedisPrefix()
	cmdList := make([]*tempUserScoreListV2Struct, 0)
	for _, v := range users {
		if isBlacklistCheck {
			if _, ok := m[v]; ok {
				blackUserIds = append(blackUserIds, v)
				continue
			}
		}

		rankKey := rankingKey(nameWithoutBucket, userIdHashBucket(v, conf.rankRedisBucket))
		member := strconv.FormatUint(v, 10)
		tmpKey := prefix + rankKey
		cmdList = append(cmdList, &tempUserScoreListV2Struct{
			Uid:      v,
			ScoreCmd: pipeline.ZScore(ctx, tmpKey, member),
		})
	}

	_, err = pipeline.Exec(ctx)
	if err != nil && err != spiCache.RedisNil {
		gamelog.ErrorWithContext(ctx, "User ScoreList pipeline error", gamelog.Fields{"cmdList": cmdList, "err": err})

		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(100),
			ErrMsg:  proto.String(err.Error()),
		}
		rsp.Items = scores
		return nil
	}

	for _, tmpCMD := range cmdList {
		tmpResult, err := tmpCMD.ScoreCmd.Result()
		if err != nil && err != spiCache.RedisNil {
			gamelog.ErrorWithContext(ctx, "User ScoreList ScoreCmd error", gamelog.Fields{"tmpCMD": tmpCMD, "err": err})

			rsp.Resp = &game_rank.CommonResponse{
				ErrCode: proto.Int32(100),
				ErrMsg:  proto.String(err.Error()),
			}
			rsp.Items = scores
			return nil
		}
		if tmpResult == 0 {
			continue
		}

		tmpUID := tmpCMD.Uid
		if isBlacklistCheck {
			if _, ok := m[tmpUID]; ok {
				continue
			}
		}

		item := game_rank.RankItem{}
		score, timestamp := SplitFloat32AndTimestamp(tmpResult)
		item.Score = proto.Float64(score)
		item.WeekTimeStamp = proto.Uint64(uint64(timestamp))
		item.UserId = proto.Uint64(tmpUID)
		scores = append(scores, &item)

	}

	if len(blackUserIds) > 0 {
		gamelog.InfoWithContext(ctx, "UserScoreListV2 found black user", gamelog.Fields{"blackUser": blackUserIds, "req": req})
	}

	mapUserInfo, err := batchGetUserBaseInfo(ctx, req.GetUserIds())
	if err != nil {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(100),
			ErrMsg:  proto.String("get usename failed"),
		}
		return nil
	}

	if req.GetDetail() {
		for i, _ := range scores {
			userid := scores[i].GetUserId()

			user, ok := mapUserInfo[userid]
			if !ok || user == nil {
				//gamelog.Info("userid not found", gamelog.Fields{"uid": userid})
			} else {
				scores[i].UserName = proto.String(user.GetUserName())
				scores[i].Avatar = proto.String(user.GetPortrait())
			}
		}
	}
	rsp.Resp = &game_rank.CommonResponse{
		ErrCode: proto.Int32(0),
		ErrMsg:  proto.String("ok"),
	}
	rsp.Items = scores
	return nil
}

func (s *RankHandler) RankListARCatchV2(ctx context.Context, req *game_rank.RankListARCatchRequestV2, rsp *game_rank.RankListResponse) error {
	if !checkRankTypeIsWeek(req.GetRankType()) {
		// TODO global rank operation
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(101),
			ErrMsg:  proto.String("not support rank type"),
		}
		return nil
	}

	rankWeek := AdminCheckAndFillRankWeek(constant.GameARScope, req.GetRankType(), req.GetRankWeek())
	if len(rankWeek) == 0 {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(102),
			ErrMsg:  proto.String("check and fill rank_week failed"),
		}
		return nil
	}

	// if rank value is not exist then set -1
	if req.RankValue == nil {
		req.RankValue = proto.Float32(-1)
	}

	esIndex := strings.ToLower(fmt.Sprintf(constant.WriteRankIndexFormatV2, constant.GameARScope,
		env.GetEnv(), env.GetCID(), strconv.FormatInt(int64(req.GetEventId()), 10), rankWeek))
	gamelog.Info("ar catch rank list params", map[string]interface{}{
		"event_id":   req.GetEventId(),
		"offset":     req.GetOffset(),
		"limit":      req.GetLimit(),
		"user_id":    req.GetUserId(),
		"user_name":  req.GetUserName(),
		"rank_value": req.GetRankValue(),
		"rank_num":   req.GetRankNum(),
		"rank_week":  rankWeek,
		"es_index":   esIndex,
	})
	if err := checkOpenIndex(esIndex); err != nil {
		if elastic.IsNotFound(err) {
			rsp.Resp = &game_rank.CommonResponse{
				ErrCode: proto.Int32(0),
				ErrMsg:  proto.String("ok"),
			}
			return nil
		}
		return err
	}

	items, totalNum := rankList(req.GetEventId(), req.GetOffset(), req.GetLimit(), req.GetUserId(), req.GetUserName(),
		float64(req.GetRankValue()), req.GetRankNum(), rankWeek, constant.GameARScope)

	rsp.Resp = &game_rank.CommonResponse{
		ErrCode: proto.Int32(0),
		ErrMsg:  proto.String("ok"),
	}
	rsp.Items = items
	rsp.TotalNum = proto.Uint64(totalNum)

	return nil
}

func (s *RankHandler) RankListV2(ctx context.Context, req *game_rank.RankListRequestV2, rsp *game_rank.RankListResponseV2) error {
	if !checkRankTypeIsWeek(req.GetRankType()) {
		// TODO global rank operation
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(101),
			ErrMsg:  proto.String("not support rank type"),
		}
		return nil
	}

	rankWeek := AdminCheckAndFillRankWeek(req.GetScope(), req.GetRankType(), req.GetRankWeek())
	if len(rankWeek) == 0 {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(102),
			ErrMsg:  proto.String("check and fill rank_week failed"),
		}
		return nil
	}

	if req.RankValue == nil {
		req.RankValue = proto.Float32(-1)
	}

	esIndex := getEsV2Index(req.GetScope(), rankWeek, req.GetRankId(), int64(req.GetEventId()))
	gamelog.Info("rank list params", map[string]interface{}{
		"event_id":   req.GetEventId(),
		"offset":     req.GetOffset(),
		"limit":      req.GetLimit(),
		"user_id":    req.GetUserId(),
		"user_name":  req.GetUserName(),
		"rank_value": req.GetRankValue(),
		"rank_num":   req.GetRankNum(),
		"rank_week":  rankWeek,
		"scope":      req.GetScope(),
		"es_index":   esIndex,
	})
	if err := checkOpenIndex(esIndex); err != nil {
		if elastic.IsNotFound(err) {
			rsp.Resp = &game_rank.CommonResponse{
				ErrCode: proto.Int32(0),
				ErrMsg:  proto.String("ok"),
			}
			return nil
		}
		return err
	}

	// refresh index for edit/add
	_, err := esutil.EsClient.Refresh().Index(esIndex).Do(ctx)
	if err != nil {
		gamelog.Error("RankListV2 refresh index error", gamelog.Fields{"index": esIndex})
	}

	items, totalNum, nextScrollId := rankListV2(esIndex, &RankEsCondition{
		Offset:    req.GetOffset(),
		Limit:     req.GetLimit(),
		UserId:    req.GetUserId(),
		UserName:  req.GetUserName(),
		RankValue: req.GetRankValue(),
		RankNum:   req.GetRankNum(),
		ScrollId:  req.GetScrollId(),
		RobotId:   req.GetRobotId(),
	})

	noUserNameArr := make([]uint64, 0)
	for _, item := range items {
		userId := item.GetUserId()
		userName := item.GetUserName()
		if userName == "" {
			noUserNameArr = append(noUserNameArr, userId)
		}
	}

	if len(noUserNameArr) > 0 {
		//这里都是取的es老的数据可能，admin操作，不设置local缓存了
		//不用分组调用，看了下游friends服务有做分组的逻辑
		cli := gameplatform_friend.GetGrpcClient()
		rq := &gameplatform_friend.BatchGetUserBaseInfoRequest{
			UserId: noUserNameArr,
		}
		rp, err := cli.BatchGetUserBaseInfo(ctx, rq)
		if err != nil {
			gamelog.Error("RankListV2 BatchGetUserBaseInfo error", gamelog.Fields{"err": err})
		} else {
			mm := make(map[uint64]string)
			for _, user := range rp.GetInfoList() {
				mm[user.GetUserId()] = user.GetUserName()
			}

			for _, item := range items {
				userId := item.GetUserId()
				userName := item.GetUserName()
				if userName == "" {
					if name, ok := mm[userId]; ok {
						item.UserName = proto.String(name)
					}
				}
			}
		}
	}

	rsp.Resp = &game_rank.CommonResponse{
		ErrCode: proto.Int32(0),
		ErrMsg:  proto.String("ok"),
	}
	rsp.Items = items
	rsp.TotalNum = proto.Uint64(totalNum)
	rsp.NextScrollId = proto.String(nextScrollId)

	return nil
}

func (s *RankHandler) RankListByUsers(ctx context.Context, req *game_rank.RankListByUsersRequest, rsp *game_rank.RankListByUsersResponse) error {
	if !checkRankTypeIsWeek(req.GetRankType()) {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(101),
			ErrMsg:  proto.String("not support rank type"),
		}
		return nil
	}

	rankWeek := AdminCheckAndFillRankWeek(req.GetScope(), req.GetRankType(), req.GetRankWeek())
	if len(rankWeek) == 0 {
		rsp.Resp = &game_rank.CommonResponse{
			ErrCode: proto.Int32(102),
			ErrMsg:  proto.String("check and fill rank_week failed"),
		}
		return nil
	}

	var esIndex string
	var items []*game_rank.RankListItem
	var err error
	if req.GetScope() == constant.GameRCScope {
		rankId, _ := strconv.ParseInt(req.GetRankId(), 10, 64)
		esIndex = EsWeekRankIndex(int32(rankId), rankWeek)
		items, err = esQueryRankListByUsers(esIndex, req.GetUserIds())
	} else {
		esIndex = getEsV2Index(req.GetScope(), rankWeek, req.GetRankId(), 0)
		items, err = esQueryRankListV2ByUsers(esIndex, req.GetUserIds())
	}

	if err != nil {
		return err
	}
	rsp.Resp = &game_rank.CommonResponse{}
	rsp.Items = items
	return nil
}

func getFilterBlackPreciseRank(ctx context.Context, res []cache.ZsetElem, member string, rankInBucket int64, scopeRankName string, req *game_rank.UserRankRequestV2) int64 {
	// res为未截断的m * Top100的数据
	var originRank int64 = -1
	var finalRank int64
	needCheckUser := make([]uint64, 0)
	scope := req.GetReq().GetScope()

	for index := range res {
		v := res[index].Value
		if v == member {
			originRank = int64(index)
			break
		}

		userId, err := strconv.ParseUint(v, 10, 64)
		if err != nil {
			continue
		}

		needCheckUser = append(needCheckUser, userId)
	}

	if originRank == -1 {
		// not found
		finalRank = rankInBucket * int64(conf.rankRedisBucket)
		if finalRank <= TopNMaxRank {
			finalRank = TopNMaxRank + 1
		}

		return finalRank
	}

	originRank++
	blacks := BatchGetBlacklistUserIdMap(ctx, scope, needCheckUser)
	finalRank = originRank - int64(len(blacks))

	gamelog.InfoWithContext(ctx, "getFilterBlackPreciseRank check black", gamelog.Fields{"req": req, "blacks": blacks, "originRank": originRank, "finalRank": finalRank})

	if len(blacks) > 0 {
		go doCleanRankJob(ctx, scopeRankName, conf.rankRedisBucket, scope)
	}

	return finalRank
}

func getNormalPreciseRank(res []cache.ZsetElem, redisScore float64, isReverse bool, rankInBucket int64, member string) int64 {
	if len(res) == 0 {
		return 0
	}

	// res为截断Top100的数据
	var rank int64
	last := res[len(res)-1]
	if (redisScore < last.Score && !isReverse) || (redisScore > last.Score && isReverse) {
		// score not in TopNMaxRank, degrade to roughlyRank
		rank = rankInBucket * int64(conf.rankRedisBucket)
		if rank <= TopNMaxRank {
			rank = TopNMaxRank + 1
		}
	} else {
		if isReverse {
			for index := range res {
				if res[index].Value == member {
					rank = int64(index)
					break
				}
			}
		} else {
			for index := range res {
				if res[index].Value == member {
					rank = int64(index)
					break
				}
			}
		}

		rank = rank + 1
	}

	return rank
}

func batchGetUserBaseInfo(ctx context.Context, uids []uint64) (map[uint64]*gameplatform_friend.UserBaseInfo, error) {
	if len(uids) == 0 {
		return nil, nil
	}
	// 优先从本地缓存中获取
	cachedUsers, missCachedUsers := GetUserInfoFromLocalCache(uids)

	// slice转换成Map以提高查询效率
	retMap := make(map[uint64]*gameplatform_friend.UserBaseInfo)
	for _, info := range cachedUsers {
		retMap[info.GetUserId()] = info
	}

	if len(missCachedUsers) > 0 {
		cli := gameplatform_friend.GetGrpcClient()
		req := &gameplatform_friend.BatchGetUserBaseInfoRequest{
			UserId: missCachedUsers,
		}
		rsp, err := cli.BatchGetUserBaseInfo(ctx, req)
		gamelog.Debug("debug info BatchGetUserBaseInfo", gamelog.Fields{"req": req, "resp": rsp, "err": err})

		if err != nil {
			gamelog.Error("BatchGetUserBaseInfo rpc failed", map[string]interface{}{
				"err": err,
			})
			metrics.ReportCounterWithLabels(metrics.RPC_FAILED, map[string]string{
				"from": "rank_server",
				"to":   "game_friends_server",
				"func": "BatchGetUserBaseInfo",
			})
			return nil, err
		}

		for _, info := range rsp.GetInfoList() {
			retMap[info.GetUserId()] = info
		}

		SetUserInfoToLocalCache(missCachedUsers, retMap)
	}

	return retMap, nil
}

func concatRankNameAndRankWeek(rankName, rankWeek string) string {
	return "rank_" + rankName + "_" + rankWeek
}

func checkRankTypeIsWeek(rankType uint32) bool {
	if rankType == constant.RankTypeMonth || rankType == constant.RankTypeDaily || rankType == constant.RankTypeWeek || rankType == constant.RankTypeRetention {
		return true
	} else {
		return false
	}
}

func checkRankWeekOrFill(req *game_rank.RankV2CommonRequest) error {
	// from admin
	if req.GetRankWeek() != "" {
		if req.GetScope() == constant.GameARScope {
			if matched, _ := regexp.MatchString(`^\d{1,2}$`, req.GetRankWeek()); matched {
				return nil
			}
		} else {
			if matched, _ := regexp.MatchString(`^\d{4}.\d{1,2}$`, req.GetRankWeek()); matched {
				return nil
			}
		}
	}

	// from game client call
	rankType := req.GetRankType()
	getFunc, exists := RankKeyMap[rankType]
	if !exists {
		gamelog.Error("rank type error", map[string]interface{}{
			"err": errors.New("rank type error"),
		})
		return errors.New("rank type error")
	}

	rankKey, err := getFunc()
	if err != nil {
		gamelog.Error("get rank key failed", map[string]interface{}{
			"err": err,
		})
		return err
	}

	req.RankWeek = rankKey
	return nil

}

func adminCheckRankWeek(rankWeek string, scope string, rankType uint32) (bool, error) {
	if rankWeek == "" {
		return false, errors.New("rank week is nil")
	}

	if rankType == constant.RankTypeWeek {
		if scope == constant.GameARScope {
			if matched, _ := regexp.MatchString(`^\d{1,2}$`, rankWeek); !matched {
				return false, errors.New("rank week is illegal")
			}
		} else {
			if matched, _ := regexp.MatchString(`^\d{4}.\d{1,2}$`, rankWeek); !matched {
				return false, errors.New("rank week is illegal")
			}
		}
	} else if rankType == constant.RankTypeMonth {
		if matched, _ := regexp.MatchString(`^\d{4}.\d{1,2}$`, rankWeek); !matched {
			return false, errors.New("rank month is illegal")
		}
	} else if rankType == constant.RankTypeDaily {
		if matched, _ := regexp.MatchString(`^\d{4}.\d{1,3}$`, rankWeek); !matched {
			return false, errors.New("rank daily is illegal")
		}
	}

	return true, nil
}

func AdminCheckAndFillRankWeek(AppId string, RankType uint32, inRankWeek string) (RankWeek string) {
	if RankType == constant.RankTypeRetention {
		inRankWeek = constant.RankRetention
	}
	if right, err := adminCheckRankWeek(inRankWeek, AppId, RankType); !right {
		gamelog.Error("admin check ranktype and rankweek failed", map[string]interface{}{
			"rank_week": inRankWeek,
			"appid":     AppId,
			"rank_type": RankType,
			"error":     err,
		})
		return ""
	}

	rankWeek := inRankWeek
	prefixValue, exists := RankTypePrefix[RankType]
	if exists {
		rankWeek = prefixValue + "." + rankWeek
	}

	return rankWeek
}

func transZElem2UserIds(elem []cache.ZsetElem) []uint64 {
	uids := make([]uint64, 0, len(elem))
	for _, v := range elem {
		if v.Value == "" {
			continue
		}

		userId, err := strconv.ParseUint(v.Value, 10, 64)
		if err != nil {
			gamelog.Error("transZElem2UserIds parse userid failed", gamelog.Fields{"err": err, "value": v.Value})
			continue
		}

		uids = append(uids, userId)
	}

	return uids
}

func checkOpenIndex(index string) error {
	esIndexRes, err := getEsIndexInfo(index)
	if err != nil {
		return err
	}
	if len(esIndexRes) > 0 {
		esIndexInfo := esIndexRes[0]
		if esIndexInfo.Status == "close" {
			gamelog.Info("need reopen index", gamelog.Fields{"index": index})
			_, err := esutil.EsClient.OpenIndex(index).Do(context.Background())
			if err != nil {
				gamelog.Error("reopen index failed", gamelog.Fields{"index": index})
				return err
			}
		}
	}
	return nil
}

func GetUsersRankTimestamp(uids []uint64, scope, rankName, rankWeek string, rankType int32) (map[uint64]int64, error) {
	res := make(map[uint64]int64)
	if len(uids) == 0 {
		return res, nil
	}
	keys := make([]string, 0, len(uids))
	for _, v := range uids {
		redisRankName := concatRankNameAndRankWeek(rankName, rankWeek)
		keys = append(keys, UserRankTimeKey(scope, redisRankName, rankType, v))
	}
	list, err := dao.GetUsersRankTimestamp(keys)
	if err != nil {
		return nil, err
	}
	if len(uids) != len(list) {
		gamelog.Error("size not match", gamelog.Fields{"uids": len(uids), "list": len(list)})
		return nil, fmt.Errorf("size not match")
	}
	for i := 0; i < len(uids); i++ {
		if len(list[i]) > 0 {
			ts, err := strconv.ParseInt(list[i], 10, 64)
			if err != nil {
				gamelog.Error("ParseInt failed", gamelog.Fields{"val": list[i], "uid": uids[i]})
				res[uids[i]] = 0
				continue
			}
			res[uids[i]] = ts
		} else {
			res[uids[i]] = 0
		}
	}
	gamelog.Debug("GetUsersRankTimestamp done", gamelog.Fields{"len": len(res)})
	return res, nil
}

func ReSortByUserTime(resRankList []*game_rank.RankItem, uids []uint64, req *game_rank.RankV2CommonRequest, reverse bool, N int32) []*game_rank.RankItem {
	if len(resRankList) == 0 || len(resRankList) == 1 {
		return resRankList
	}
	fn := func() (interface{}, error) {
		tsMap, err := GetUsersRankTimestamp(uids, req.GetScope(), req.GetRankName(), req.GetRankWeek(), int32(req.GetRankType()))
		if err != nil {
			gamelog.Error("GetUsersRankTimestamp failed", gamelog.Fields{"err": err, "uids": uids})
			tsMap = make(map[uint64]int64)
		}

		for i := 0; i < len(resRankList); i++ {
			uid := resRankList[i].GetUserId()
			if ts, ok := tsMap[uid]; ok {
				resRankList[i].NewRankTimestamp = proto.Int64(ts)
			} else {
				// retention time
				ts = int64(constant.DefaultBaseTimestamp - resRankList[i].GetWeekTimeStamp())
				ts = ts*1000 + 999
				resRankList[i].NewRankTimestamp = proto.Int64(ts)
			}
		}

		sort.Slice(resRankList, func(i int, j int) bool {
			if util.IsEqual(resRankList[i].GetScore(), resRankList[j].GetScore()) {
				if reverse {
					return resRankList[i].GetNewRankTimestamp() > resRankList[j].GetNewRankTimestamp()
				}
				return resRankList[i].GetNewRankTimestamp() < resRankList[j].GetNewRankTimestamp()
			}
			if reverse {
				return resRankList[i].GetScore() < resRankList[j].GetScore()
			}
			return resRankList[i].GetScore() > resRankList[j].GetScore()
		})

		return resRankList, nil
	}

	cacheKey := fmt.Sprintf("resort_cache_%s_%s_%s_%d_%t_%d",
		req.GetScope(), req.GetRankName(), req.GetRankWeek(), req.GetRankType(), reverse, N)
	res, err := CacheWrapper.CacheHelper(context.Background(), cacheKey, fn, time.Second*1)
	if err != nil {
		gamelog.Error("ReSortByUserTime->CacheHelper failed", gamelog.Fields{"err": err, "req": req})
		return resRankList
	}
	r, ok := res.([]*game_rank.RankItem)
	if ok {
		return r
	}
	return resRankList
}

func transZElem2RankItem(elem []cache.ZsetElem) []*game_rank.RankItem {
	var res []*game_rank.RankItem
	for i, _ := range elem {
		if elem[i].Value == "" {
			continue
		}
		userId, err := strconv.ParseUint(elem[i].Value, 10, 64)
		if err != nil {
			gamelog.Error("ParseUint failed", gamelog.Fields{"err": err, "val": elem[i]})
			continue
		}
		score, weekTimeStamp := SplitFloat32AndTimestamp(elem[i].Score)

		res = append(res, &game_rank.RankItem{
			Score:         proto.Float64(score),
			UserId:        proto.Uint64(userId),
			WeekTimeStamp: proto.Uint64(uint64(weekTimeStamp)),
		})
	}
	return res
}

func transRankItem2ZElem(itemList []*game_rank.RankItem) []cache.ZsetElem {
	var res []cache.ZsetElem
	for _, v := range itemList {
		res = append(res, cache.ZsetElem{
			Value: strconv.FormatUint(v.GetUserId(), 10),
			Score: ConcatFloat32AndTimestamp(v.GetScore(), uint32(v.GetWeekTimeStamp())),
		})
	}
	return res
}

// filterBlackRankListTopN check black open, do clean job
func filterBlackRankListTopN(ctx context.Context, scope string, resRankList []cache.ZsetElem, N int32, scopeRankName string) (finalResRankList []cache.ZsetElem) {
	length := len(resRankList)
	finalResRankList = resRankList

	if N < int32(length) {
		finalResRankList = resRankList[:N]
	}

	defer func() {
		if r := recover(); r != nil {
			gamelog.Error("filterBlackRankListTopN panic recover", gamelog.Fields{"err": r, "scopeRankName": scopeRankName})
		}
	}()

	isCheckBlack, _ := anticheat_rpc.CheckBlacklistOpen(ctx, gameplatform_game.GameGrpcClient, scope, anticheat_rpc.BizRank)
	if !isCheckBlack {
		// 未打开配置 直接返回TopN
		return finalResRankList
	}

	gamelog.InfoWithContext(ctx, "check blacklist open", gamelog.Fields{"scope": scope})

	// 查询前TopN有多少黑名单用户
	blacks := BatchGetBlacklistUserIdMap(ctx, scope, transZElem2UserIds(finalResRankList))
	numToCover := len(blacks)

	gamelog.InfoWithContext(ctx, "blacklist in TopN result", gamelog.Fields{"scope": scope, "blacks": blacks, "N": N, "numToCover": numToCover})

	if numToCover != 0 {
		// 需要向后补位,并起一个goroutine清理数据
		go doCleanRankJob(ctx, scopeRankName, conf.rankRedisBucket, scope)

		// 清除回包中的黑名单用户
		finalResRankList = cleanBlackResult(finalResRankList, blacks)

		// length = N * bucketNum
		// length = 200 ; N = 200 不拉了
		// length = 300 ; N = 200 最多再拉一次
		// length = 400 ; N = 200 最多再拉一次
		// length = 401 ; N = 200 最多再拉两次
		count := length/int(N) - 1
		if length%int(N) != 0 {
			count++
		}

		gamelog.InfoWithContext(ctx, "start to cover topn", gamelog.Fields{"count": count, "N": N, "length": length})
		for i := 0; i < count; i++ {
			// 已完成补位
			if numToCover <= 0 {
				gamelog.InfoWithContext(ctx, "cover topn finish", gamelog.Fields{"numToCover": numToCover, "count": count})
				break
			}

			// begin,end
			begin := (i + 1) * int(N)
			end := begin + int(N)
			if end > length {
				end = length
			}

			// 新一轮数据和用户id
			coverResRankList := resRankList[begin:end]
			coverUids := transZElem2UserIds(coverResRankList)

			// 新一轮黑名单用户
			coverResult := BatchGetBlacklistUserIdMap(ctx, scope, coverUids)

			// numToCover = 10; len(coverUids) = 200; len(coverResult) = 195 => numToCover = 5
			// numToCover = 10; len(coverUids) = 200; len(coverResult) = 5 => numToCover = -185
			numToCover = numToCover - (len(coverUids) - len(coverResult))

			// 清除新一轮黑名单用户后补充到原来的数据中
			finalResRankList = append(finalResRankList, cleanBlackResult(coverResRankList, coverResult)...)
			gamelog.InfoWithContext(ctx, "cover topn over once", gamelog.Fields{"i": i, "numToCover": numToCover, "len(coverUids)": len(coverUids), "len(coverResult)": len(coverResult)})
		}
	}

	// 重新截取
	if N < int32(len(finalResRankList)) {
		finalResRankList = finalResRankList[:N]
	}

	return finalResRankList
}

func transRankItem2UserIds(elem []*game_rank.RankItem) []uint64 {
	res := make([]uint64, 0, len(elem))

	for _, e := range elem {
		res = append(res, e.GetUserId())
	}

	return res
}

func RedisPrefix() string {
	env, region := env.GetEnv(), env.GetCID()
	return fmt.Sprintf("gameabr_%s_%s.", env, region)
}

func fillUserName(ctx context.Context, username string, userId uint64) string {
	if username != "" {
		return username
	}

	m, _ := batchGetUserBaseInfo(ctx, []uint64{userId})
	user, _ := m[userId]
	if user == nil {
		return ""
	}
	return user.GetUserName()
}
