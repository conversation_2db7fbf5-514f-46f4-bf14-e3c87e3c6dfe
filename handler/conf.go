package handler

import (
	"fmt"
	"git.garena.com/shopee/game_platform/comm_lib/env"
)

type regionSetting struct {
	region          string
	rankRedisBucket int
	infoRedisBucket int
}

var conf = regionSetting{
	"sg", 20, 10,
}

func init() {
	region := env.GetCID()
	conf.region = region
	switch region {
	case "id", "xx":
		conf.infoRedisBucket = 100
		conf.rankRedisBucket = 100
	case "vn", "tw":
		conf.infoRedisBucket = 10
		conf.rankRedisBucket = 10
	default:
		conf.infoRedisBucket = 3
		conf.rankRedisBucket = 3
	}
	fmt.Printf("region:%s,set rankRedisBucket %d, infoRedisBucket %d\n", region, conf.rankRedisBucket, conf.infoRedisBucket)
}
