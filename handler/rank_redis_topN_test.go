package handler

import (
	"context"
	"fmt"
	spiCache "git.garena.com/shopee/mts/go-application-server/spi/cache"
	"math/rand"
	"strconv"
	"testing"
	"time"

	"git.garena.com/shopee/game_platform/comm_lib/utils"
	rankConfig "git.garena.com/shopee/game_platform/rank_server/config"
	"git.garena.com/shopee/game_platform/rank_server/constant"
	"git.garena.com/shopee/game_platform/rank_server/dao/cache"
	. "github.com/smartystreets/goconvey/convey"
)

func TestNewRankKey(t *testing.T) {
	Init()
	testUidA := uint64(88888)
	testUidB := uint64(99999)
	testScope := "test_scope"
	testRankName := "test_rank"

	nextWeekLeftTime, _ := utils.GetNextWeekLeftTimeMilli(time.Now().Unix() * 1000)

	// TODO: sg - test 测试之前需要把 topNRankKeySizeMax 改成30, topNRankKeyReInitSize = 10
	Convey("TestInit", t, func() {
		defer func() {
			// 清除Redis
			cache.RedisUtilDelete(context.Background(), GetRankKeyV2(testScope, testRankName, false))
			cache.RedisUtilDelete(context.Background(), GetRankKeyV2(testScope, testRankName, true))
			for bucket := 0; bucket < conf.rankRedisBucket; bucket++ {
				cache.RedisUtilDelete(context.Background(), fmt.Sprintf("%s.%s.%d", testScope, testRankName, bucket))
			}

		}()

		testValue := rankConfig.GetNewKeyTimestampMonthly()
		So(testValue, ShouldEqual, 1)

		totalUserMap := make(map[int]float64)

		// 没有Key时查询
		_, err := QueryUserRankV2(context.Background(), testUidA, testScope, testRankName, constant.RankTypeDaily, false)
		So(err, ShouldEqual, spiCache.RedisNil)

		// 做一次空的初始化
		topUserList, err := InitRetentionRank(testScope, testRankName, false)
		So(err, ShouldBeNil)
		So(len(topUserList), ShouldEqual, 0)

		// 初始化
		scoreMap := map[float64]bool{}
		for bucket := 0; bucket < conf.rankRedisBucket; bucket++ {
			rankKey := rankingKey(scopeRankName(testScope, testRankName), strconv.FormatInt(int64(bucket), 10))

			for j := 0; j < 10; j++ {
				uidMember := rand.Intn(100000)
				randomScore := float64(rand.Intn(10000) + 1)
				for {
					if scoreMap[randomScore] {
						randomScore = float64(rand.Intn(10000) + 1)
					} else {
						scoreMap[randomScore] = true
						break
					}
				}
				newRedisScore := ConcatFloat32AndTimestamp(randomScore, nextWeekLeftTime)
				cache.RedisUtilZAdd(context.Background(), rankKey, newRedisScore, uidMember)
				totalUserMap[uidMember] = newRedisScore
			}

			cache.RedisUtilExpire(context.Background(), rankKey, constant.RankKeyStressExp)
		}

		topUserList, err = InitRetentionRank(testScope, testRankName, false)
		So(err, ShouldBeNil)
		So(len(topUserList), ShouldEqual, len(totalUserMap))

		bottomUserList, err := InitRetentionRank(testScope, testRankName, true)
		So(err, ShouldBeNil)
		So(len(bottomUserList), ShouldEqual, len(totalUserMap))

		// 分数应当相等
		So(bottomUserList[0].Score, ShouldEqual, topUserList[len(totalUserMap)-1].Score)

		// 有key，但是查询的uid不在榜单中
		_, err = QueryUserRankV2(context.Background(), testUidB, testScope, testRankName, constant.RankTypeDaily, false)
		So(err, ShouldEqual, spiCache.RedisNil)

		ctx := context.Background()
		// 查询榜单前10
		userTopList, err := QueryTopNFromSingleKey(ctx, testScope, testRankName, 10, constant.RankTypeDaily, false, false)
		for index, tmpElem := range userTopList {
			// 不检查member，是因为我们的随机分数一致太高，真实情况精确到毫秒，基本不会重合
			//So(tmpElem.Value, ShouldEqual, topUserList[index].Value)
			So(tmpElem.Score, ShouldEqual, topUserList[index].Score)
		}

		// 重复查询，应当幂等
		userTopListV2, err := QueryTopNFromSingleKey(ctx, testScope, testRankName, 10, constant.RankTypeDaily, false, false)
		for index, tmpElem := range userTopList {
			// 不检查member，是因为我们的随机分数一致太高，真实情况精确到毫秒，基本不会重合
			So(tmpElem.Value, ShouldEqual, userTopListV2[index].Value)
			So(tmpElem.Score, ShouldEqual, userTopListV2[index].Score)
		}

		userBottomList, err := QueryTopNFromSingleKey(ctx, testScope, testRankName, 10, constant.RankTypeDaily, true, false)
		for index, tmpElem := range userBottomList {
			//So(tmpElem.Value, ShouldEqual, topUserList[index].Value)
			So(tmpElem.Score, ShouldEqual, bottomUserList[index].Score)
		}

		// 查询第一名
		firstOne, _ := strconv.ParseUint(userTopList[0].Value, 10, 64)
		tmpRank, err := QueryUserRankV2(context.Background(), firstOne, testScope, testRankName, constant.RankTypeDaily, false)
		So(err, ShouldBeNil)
		So(tmpRank, ShouldEqual, 1)

		//  写入最后一名分数
		lastOneScore := topUserList[len(topUserList)-1].Score + 1
		DoubleWriteTopNKey(lastOneScore, true, false, &Rankings{
			Scope:    testScope,
			RankName: testRankName,
			Userid:   testUidA,
			Score:    lastOneScore,
			RankType: constant.RankTypeDaily,
		})
		So(err, ShouldBeNil)

		// 查询刚写入的最后一名
		tmpRank, err = QueryUserRankV2(context.Background(), testUidA, testScope, testRankName, constant.RankTypeDaily, false)
		So(err, ShouldBeNil)
		So(tmpRank, ShouldEqual, topNRankKeySizeMax)

		// 再次写入，但把他的值设置为第一名
		DoubleWriteTopNKey((1<<50)-1, true, false, &Rankings{
			Scope:    testScope,
			RankName: testRankName,
			Userid:   testUidA,
			Score:    (1 << 50) - 1,
			RankType: constant.RankTypeDaily,
		})
		So(err, ShouldBeNil)
		// 再次查询， 第一名
		tmpRank, err = QueryUserRankV2(context.Background(), testUidA, testScope, testRankName, constant.RankTypeDaily, false)
		So(err, ShouldBeNil)
		So(tmpRank, ShouldEqual, 1)

		// 再次写入，但把他的值设置为最后一名
		DoubleWriteTopNKey(0, true, false, &Rankings{
			Scope:    testScope,
			RankName: testRankName,
			Userid:   testUidA,
			Score:    0,
			RankType: constant.RankTypeDaily,
		})
		So(err, ShouldBeNil)
		// 再次查询， 不存在
		tmpRank, err = QueryUserRankV2(context.Background(), testUidA, testScope, testRankName, constant.RankTypeDaily, false)
		So(err, ShouldEqual, spiCache.RedisNil)
		So(tmpRank, ShouldEqual, MaxUserRank)

		// 测试满员情 topNRankKeySizeMax = 10k，太大了，需要手动改小到30
		DoubleWriteTopNKey((1<<50)-1, true, false, &Rankings{
			Scope:    testScope,
			RankName: testRankName,
			Userid:   testUidA,
			Score:    (1 << 50) - 1,
			RankType: constant.RankTypeDaily,
		})

		// 分数过小，排不上
		oldKey, _ := userIdHashBucketRank(testScope, testRankName, testUidB, conf.rankRedisBucket)
		_, err = cache.RedisUtilZAdd(context.Background(), oldKey, lastOneScore-10, testUidB)
		So(err, ShouldBeNil)

		DoubleWriteTopNKey(lastOneScore-10, true, false, &Rankings{
			Scope:    testScope,
			RankName: testRankName,
			Userid:   uint64(testUidB),
			Score:    lastOneScore - 10,
			RankType: constant.RankTypeDaily,
		})

		uidBRank, err := QueryUserRankV2(context.Background(), testUidB, testScope, testRankName, constant.RankTypeDaily, false)
		So(err, ShouldBeNil)
		So(uidBRank, ShouldEqual, MaxUserRank)

		// bottom 同理校验

		// 查询倒数第一名
		firstOne, _ = strconv.ParseUint(bottomUserList[0].Value, 10, 64)
		tmpRank, err = QueryUserRankV2(context.Background(), firstOne, testScope, testRankName, constant.RankTypeDaily, true)
		So(err, ShouldBeNil)
		So(tmpRank, ShouldEqual, 1)

		//  写入榜单最后一名
		lastOneScore = bottomUserList[len(bottomUserList)-1].Score - 1
		DoubleWriteTopNKey(lastOneScore, false, true, &Rankings{
			Scope:    testScope,
			RankName: testRankName,
			Userid:   testUidA,
			Score:    lastOneScore,
			RankType: constant.RankTypeDaily,
		})
		So(err, ShouldBeNil)

		// 查询刚写入的榜单最后一名
		tmpRank, err = QueryUserRankV2(context.Background(), testUidA, testScope, testRankName, constant.RankTypeDaily, true)
		So(err, ShouldBeNil)
		So(tmpRank, ShouldEqual, topNRankKeySizeMax)

		// 测试满员情 topNRankKeySizeMax = 10k，太大了，需要手动改小到30
		// 分数过大，排不上
		oldKey, _ = userIdHashBucketRank(testScope, testRankName, testUidB, conf.rankRedisBucket)
		_, err = cache.RedisUtilZAdd(context.Background(), oldKey, lastOneScore-10, testUidB)
		So(err, ShouldBeNil)

		DoubleWriteTopNKey(lastOneScore+10, false, true, &Rankings{
			Scope:    testScope,
			RankName: testRankName,
			Userid:   uint64(testUidB),
			Score:    lastOneScore + 10,
			RankType: constant.RankTypeDaily,
		})

		uidBRank, err = QueryUserRankV2(context.Background(), testUidB, testScope, testRankName, constant.RankTypeDaily, true)
		So(err, ShouldBeNil)
		So(uidBRank, ShouldEqual, MaxUserRank)

		// 删除单个User
		oldKey, _ = userIdHashBucketRank(testScope, testRankName, testUidA, conf.rankRedisBucket)
		cache.RedisUtilBatchZRem(context.Background(), oldKey, testUidA)
		DeleteUserFromSingleKey(testUidA, constant.RankTypeDaily, testScope, testRankName, false)
		_, err = QueryUserRankV2(context.Background(), testUidA, testScope, testRankName, constant.RankTypeDaily, false)
		So(err, ShouldEqual, spiCache.RedisNil)

		oldKey, _ = userIdHashBucketRank(testScope, testRankName, testUidB, conf.rankRedisBucket)
		cache.RedisUtilBatchZRem(context.Background(), oldKey, testUidB, "1")
		DeleteUserFromSingleKey(testUidB, constant.RankTypeDaily, testScope, testRankName, false)
		_, err = QueryUserRankV2(context.Background(), testUidB, testScope, testRankName, constant.RankTypeDaily, false)
		So(err, ShouldEqual, spiCache.RedisNil)

		// 大量设置分数为最小值，触发重新初始化
		for _, tmpUser := range topUserList {
			tmpUid, _ := strconv.ParseUint(tmpUser.Value, 10, 64)
			DoubleWriteTopNKey(100, true, false, &Rankings{
				Scope:    testScope,
				RankName: testRankName,
				Userid:   tmpUid,
				Score:    100,
				RankType: constant.RankTypeDaily,
			})
		}

		// 再次查询
		newUserTopList, err := QueryTopNFromSingleKey(ctx, testScope, testRankName, 10, constant.RankTypeDaily, false, false)
		So(err, ShouldBeNil)
		for index, tmpElem := range newUserTopList {
			So(tmpElem.Score, ShouldEqual, topUserList[index].Score)
		}

		// 删除整个Key
		DeleteUserFromSingleKey(0, constant.RankTypeDaily, testScope, testRankName, true)
		for bucket := 0; bucket < conf.rankRedisBucket; bucket++ {
			cache.RedisUtilDelete(context.Background(), fmt.Sprintf("%s.%s.%d", testScope, testRankName, bucket))
		}
		_, err = QueryUserRankV2(context.Background(), testUidB, testScope, testRankName, constant.RankTypeDaily, false)
		So(err, ShouldEqual, spiCache.RedisNil)
	})
}

func TestDWFChannel(t *testing.T) {
	//rankConfig.NewConfigCenterClient()
	Init()
	testUidA := uint64(88888)

	testScope := "test_scope"
	testRankName := "test_rank"
	Convey("TestInit", t, func() {
		defer func() {
			// 清除Redis
			cache.RedisUtilDelete(context.Background(), GetRankKeyV2(testScope, testRankName, false))
			cache.RedisUtilDelete(context.Background(), GetRankKeyV2(testScope, testRankName, true))
			for bucket := 0; bucket < conf.rankRedisBucket; bucket++ {
				cache.RedisUtilDelete(context.Background(), fmt.Sprintf("%s.%s.%d", testScope, testRankName, bucket))
			}

		}()

		// 测试双写失败的情况，需要手动 return err
		go RetryFailedDoubleWriteTopN()

		putDWFChan(true, false, &Rankings{
			Scope:    testScope,
			RankName: testRankName,
			Userid:   testUidA,
			Score:    1,
			RankType: constant.RankTypeDaily,
		})

		time.Sleep(time.Second)
		rank, err := QueryUserRankV2(context.Background(), testUidA, testScope, testRankName, constant.RankTypeDaily, false)
		So(err, ShouldEqual, nil)
		So(rank, ShouldEqual, 1)
	})
}
