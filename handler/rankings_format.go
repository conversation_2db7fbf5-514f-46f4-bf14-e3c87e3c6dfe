package handler

import (
	"fmt"
	"github.com/shopspring/decimal"
	"strconv"
)

// rankingKey = scopeRankName.bucket = scope.rankName.bucket
const rankingKeyFormat = "%s.%s"
const topNRankKeyFormat = "topN.%s"       // scoreRankName
const bottomNRankKeyFormat = "bottomN.%s" // scoreRankName

const scopeRankNameFormat = "%s.%s"
const weekRankFmt = "rank_%d_%d" //{eventid}, {sessionid}
const userRankTimeFmt = "user_rank_time_%s_%s_%d_%d"

const nobucket = "none"

const topNRankKeySizeMax = 10000 + 10000   // NOTE: hard code = 10k + 5k
const topNRankKeyReInitSize = 10000 + 1000 // 重新初始化阈值, NOTE topNRankKeySizeMax + 1000

func userInfoKey(userid uint64, bucketSize int) string {
	bucket := userIdHashBucket(userid, bucketSize)
	if bucket == nobucket {
		return "userinfo"
	}
	return fmt.Sprintf("userinfo.%s", bucket)
}

func rankingKey(scopeRankName, bucket string) string {
	if bucket == nobucket {
		return scopeRankName
	}
	return fmt.Sprintf(rankingKeyFormat, scopeRankName, bucket)
}

func GetRankKeyV2(scope, rankName string, isReverse bool) string {
	if isReverse {
		return fmt.Sprintf(bottomNRankKeyFormat, scopeRankName(scope, rankName))
	}
	return fmt.Sprintf(topNRankKeyFormat, scopeRankName(scope, rankName))
}

func scopeRankName(scope, rankName string) string {
	return fmt.Sprintf(scopeRankNameFormat, scope, rankName)
}

func userIdHashBucket(userid uint64, bucketSize int) string {
	if bucketSize <= 1 {
		return nobucket
	}
	bucketNum := userid % uint64(bucketSize)
	return strconv.FormatUint(bucketNum, 10)
}

func userIdHashBucketRank(scope, rankName string, userid uint64, bucketSize int) (key, keyWithoutBucket string) {
	bucket := userIdHashBucket(userid, bucketSize)
	keyWithoutBucket = scopeRankName(scope, rankName)
	key = rankingKey(keyWithoutBucket, bucket)
	return
}

func WeekRankName(eventid int32, week int32) (string, error) {
	//todo 可以加上evenid,week 格式的校验
	return WeekRankKey(eventid, week), nil
}

func WeekRankKey(eventid, week int32) string {
	return fmt.Sprintf(weekRankFmt, eventid, week)
}

func UserRankTimeKey(scope, redisRankName string, rankType int32, userId uint64) string {
	return fmt.Sprintf(userRankTimeFmt, scope, redisRankName, rankType, userId)
}

//使用位运算
//func ConcatFloat32AndTimestamp(head float64, tail uint32) float64 {
//	//直接将float 转成 int 会丢失小数的精度，使用字节直接处理
//	//temp := math.Float32bits(head)
//	headu64 := uint64(head * 100)
//	res := headu64<<32 ^ uint64(tail)
//	return math.Float64frombits(res)
//}
//
//func SplitFloat32AndTimestamp(score float64) (head float64, tail uint32) {
//	uintScore := math.Float64bits(score)
//	temp := uint64(1<<32 - 1)
//	tail = uint32(uintScore & temp)
//	//直接将float 转成 int 会丢失小数的精度，使用字节直接处理
//	//head = math.Float32frombits(uint32((uintScore &^ temp) >> 32))
//	/**
//	head 精度改为 float64 。减少运算时出现精度不准的问题
//
//	例如 float32(8294/100.0) 输出 82.94 echo 82.940002
//
//	*/
//	head = float64((uintScore &^ temp) >> 32)/100.0
//	return
//}

// 使用乘法拼接 毫秒最大   3600 * 24 * 7 * 1000 = 604800 * 1000
const multiFactor float64 = 1000 * 1000 * 1000

// 保留两位
var deMultiFactor = decimal.NewFromFloat(multiFactor * 100)

func ConcatFloat32AndTimestamp(head float64, tail uint32) float64 {
	dHead := decimal.NewFromFloat(adjustDecimal(head))
	dTail := decimal.NewFromFloat(float64(tail))
	res, _ := dHead.Mul(deMultiFactor).Add(dTail).Float64()
	return res
}

func SplitFloat32AndTimestamp(score float64) (head float64, tail uint32) {
	//temp1 := score / multiFactor
	//temp2 := uint64(temp1)
	//temp3 := float64(temp2)
	//head = temp3
	head_100 := float64(uint64(score / multiFactor))
	head = head_100 / 100
	head = adjustDecimal(head)
	tail = uint32(score - head_100*multiFactor)
	return
}

func adjustDecimal(score float64) float64 {
	v, _ := decimal.NewFromFloat(score).Mul(decimal.NewFromFloat(100.00)).Float64()
	return float64(int64(v)) / 100.0
}
