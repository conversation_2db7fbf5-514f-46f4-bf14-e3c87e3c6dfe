package handler

import (
	"context"
	"testing"

	"git.garena.com/shopee/game_platform/proto/base"
	"git.garena.com/shopee/game_platform/proto/game_rank"
	"github.com/golang/protobuf/proto"
)

func TestFriendsRank(t *testing.T) {

	h := RankHandler{}
	rsp := &game_rank.FriendsRankResponseV3{}
	ft := game_rank.FriendType_All
	err := h.FriendsRankV3(context.TODO(), &game_rank.FriendsRankRequestV3{
		Base: &base.CommonRequest{
			//TraceId:  nil,
			//FlowType: nil,
			AppId: proto.String("LcqcAMvwNcX8MR63xX"),
		},
		FriendType: &ft,
		UserId:     proto.Int64(1225239520),
		RankId:     proto.Int32(2),
		DeviceId:   proto.String("Roga1MFc58lx2oG/9ECxVQ1KYeYgB7aaKk80oqWhfQc="),
		N:          proto.Int32(10),
	}, rsp)
	if err != nil {
		t.Log(err)
		return
	}
	t.Log(rsp)
}
