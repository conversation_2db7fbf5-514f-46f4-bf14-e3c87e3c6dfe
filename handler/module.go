package handler

import (
	"context"
	"errors"
	"fmt"
	"time"

	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/comm_lib/utils"
	"git.garena.com/shopee/game_platform/proto/base"
	proto_constant "git.garena.com/shopee/game_platform/proto/constant"
	"git.garena.com/shopee/game_platform/proto/game_rank"
	"git.garena.com/shopee/game_platform/proto/gameplatform_game"
	"git.garena.com/shopee/game_platform/rank_server/constant"
	"git.garena.com/shopee/game_platform/rank_server/dao"
	"git.garena.com/shopee/game_platform/rank_server/model"
	"github.com/golang/protobuf/proto"
)

type RankModuleInfo struct {
	Config     *model.NewRankConfigVersionTab
	Appid      string
	ActivityId uint64
	SlotId     uint64
	StartTime  int64
	EndTime    int64
}

func (r *RankModuleInfo) IsTimeValid() bool {
	now := time.Now().Unix()
	return now >= r.StartTime && now < r.EndTime
}

func (r *RankModuleInfo) GetRankType() uint32 {
	cd := r.Config.ConfigData
	jd := game_rank.NewRankModuleConfig(*cd)
	switch jd.GetRefreshCycle() {
	case int32(game_rank.Cycle_NoRefresh):
		return constant.RankTypeRetention
	case int32(game_rank.Cycle_Day):
		return constant.RankTypeDaily
	case int32(game_rank.Cycle_Week):
		return constant.RankTypeWeek
	case int32(game_rank.Cycle_Month):
		return constant.RankTypeMonth
	default:
		return constant.RankTypeWeek
	}
}

func GetCachedNewRankModule(appId string, rankId int32, isGrayscale bool) (*RankModuleInfo, error) {
	cacheKey := fmt.Sprintf("newrank_module_%s_%d_%v", appId, rankId, isGrayscale)
	f := func() (interface{}, error) {
		var err error
		var cfg *model.NewRankConfigVersionTab
		if isGrayscale {
			cfg, err = dao.NewRankDao().QueryNewRankConfigDraft(rankId)
		} else {
			cfg, err = dao.NewRankDao().QueryNewRankConfigLive(rankId)
		}

		if err != nil {
			gamelog.Error("GetCachedNewRankModule|QueryNewRankConfigLive db error", gamelog.Fields{"err": err, "rankId": rankId, "appId": appId})
			return nil, err
		}

		cache := &RankModuleInfo{Config: cfg}
		cli := gameplatform_game.GetGrpcClient()
		slotRsp, err := cli.GetEventIDByModule(context.TODO(), &gameplatform_game.GetEventIDByModuleRequest{
			Req: &base.CommonRequest{
				AppId: proto.String(appId),
			},
			ModuleName:  proto.String(proto_constant.ModuleNameNewRank),
			ModuleId:    proto.Uint64(uint64(rankId)),
			IsGrayscale: proto.Bool(isGrayscale),
		})

		gamelog.Info("debug info GetEventIDByModule", gamelog.Fields{"err": err, "resp": slotRsp})

		if err != nil {
			gamelog.Error("GetCachedNewRankModule|GetEventIDByModule error", gamelog.Fields{"err": err, "rankId": rankId, "appId": appId})
			return nil, err
		}

		/*if slotRsp.GetResp().GetErrCode() != 0 {
			gamelog.Error("GetCachedNewRankModule|GetEventIDByModule failed", gamelog.Fields{"resp": slotRsp, "rankId": rankId, "appId": appId})
			return nil, errors.New("call rpc GetEventIDByModule failed")
		}*/

		slotId := slotRsp.GetEventId()
		if slotId != 0 {
			resp, err := cli.GetEventDetail(context.TODO(), &gameplatform_game.EventDetailRequest{
				Req: &base.CommonRequest{
					AppId: proto.String(appId),
				},
				EventId:     proto.Uint64(slotId),
				IsGrayscale: proto.Bool(isGrayscale),
			})

			if err != nil {
				gamelog.Error("GetCachedNewRankModule|GetEventIDByModule error", gamelog.Fields{"err": err, "rankId": rankId, "appId": appId})
				return nil, err
			}

			if resp.GetResp().GetErrCode() != 0 {
				gamelog.Error("GetCachedNewRankModule|GetEventIDByModule failed", gamelog.Fields{"resp": resp, "rankId": rankId, "appId": appId})
				return nil, errors.New("call rpc GetEventIDByModule failed")
			}

			cache.Appid = appId
			cache.StartTime = resp.GetBasic().GetStartTime()
			cache.EndTime = resp.GetBasic().GetEndTime()
			cache.SlotId = slotId
			return cache, nil
		}

		activityRsp, err := cli.GetActivityIDByModule(context.TODO(), &gameplatform_game.GetActivityIDByModuleRequest{
			Req: &base.CommonRequest{
				AppId: proto.String(appId),
			},
			ModuleName:  proto.String(proto_constant.ModuleNameNewRank),
			ModuleId:    proto.Uint64(uint64(rankId)),
			IsGrayscale: proto.Bool(isGrayscale),
		})

		if err != nil {
			gamelog.Error("GetCachedNewRankModule|GetActivityIDByModule error", gamelog.Fields{"err": err})
			return nil, err
		}

		/*if activityRsp.GetResp().GetErrCode() != 0 {
			gamelog.Error("GetCachedNewRankModule|GetActivityIDByModule failed", gamelog.Fields{"activityRsp": activityRsp})
			return nil, errors.New("call rpc GetActivityIDByModule failed")
		}*/

		activityId := activityRsp.GetActivityId()
		if activityId != 0 {
			resp, err := cli.GetGameActivity(context.TODO(), &gameplatform_game.GetGameActivityRequest{
				Req: &base.CommonRequest{
					AppId: proto.String(appId),
				},
				ActivateId:  proto.Int64(int64(activityId)),
				AppId:       proto.String(appId),
				IsGrayscale: proto.Bool(isGrayscale),
			})

			if err != nil {
				gamelog.Error("GetCachedNewRankModule|GetGameActivity error", gamelog.Fields{"err": err})
				return nil, err
			}

			if resp.GetResp().GetErrCode() != 0 {
				gamelog.Error("GetCachedNewRankModule|GetGameActivity failed", gamelog.Fields{"resp": resp})
				return nil, errors.New("call rpc GetGameActivity failed")
			}

			cache.Appid = appId
			cache.StartTime = resp.GetActivity().GetStartTime()
			cache.EndTime = resp.GetActivity().GetEndTime()
			cache.ActivityId = activityId
			return cache, nil
		}

		return cache, nil
	}

	res, err := utils.CacheHelper(context.Background(), cacheKey, f, time.Minute*3)
	if err != nil {
		gamelog.Error("GetCachedNewRankModule err", gamelog.Fields{"err": err, "rankId": rankId, "appId": appId})
		return nil, err
	}

	return res.(*RankModuleInfo), nil
}

// getNewRankName new.{moduleId}
// client just input moduleId in new version
func getNewRankName(rankId int32) string {
	return fmt.Sprintf("new.%d", rankId)
}
