package handler

import (
	"git.garena.com/shopee/game_platform/rank_server/constant"
)

var (
	RankingsEsWriter         *RankEsWriter
	RankDetailEsWriter       *RankEsWriter
	DeleteRankEsWriter       *RankEsWriter
	DeleteRankDetailEsWriter *RankEsWriter
)

func getRankingsEsWriter() *RankEsWriter {
	writer := &RankEsWriter{}
	writer.Init(
		InitEsIndexFormat(constant.WriteRankIndexFormatV2),
		InitEsDataAndConvertFunc(HandleEachEsDataRankings),
		InitEsOperation(constant.EsAdd),
		InitPrometheusMonitorLabel(constant.RankingsEsChanLabel))

	return writer
}

func getRankDetailEsWriter() *RankEsWriter {
	writer := &RankEsWriter{}
	writer.Init(
		InitEsIndexFormat(constant.WriteRankDetailIndexFormatV2),
		InitEsDataAndConvertFunc(HandleEachEsDataRankDetail),
		InitEsOperation(constant.EsAdd),
		InitPrometheusMonitorLabel(constant.RankDetailEsChanLabel))

	return writer
}

func getDeleteRankEsWriter() *RankEsWriter {
	writer := &RankEsWriter{}
	writer.Init(
		InitEsIndexFormat(constant.WriteRankIndexFormatV2),
		InitEsDataAndConvertFunc(HandleDelete),
		InitEsOperation(constant.EsDelete),
		InitPrometheusMonitorLabel(constant.DeleteRankingsEsChanLabel))

	return writer
}

func getDeleteRankDetailEsWriter() *RankEsWriter {
	writer := &RankEsWriter{}
	writer.Init(
		InitEsIndexFormat(constant.DeleteRankDetailIndexFormatV2),
		InitEsDataAndConvertFunc(HandleEachEsDataRankDetail),
		InitEsOperation(constant.EsAdd),
		InitPrometheusMonitorLabel(constant.DeleteRankDetailEsChanLabel))

	return writer
}

// init rank es writers
func InitRankEsWriter() {
	RankingsEsWriter = getRankingsEsWriter()
	RankDetailEsWriter = getRankDetailEsWriter()
	DeleteRankEsWriter = getDeleteRankEsWriter()
	DeleteRankDetailEsWriter = getDeleteRankDetailEsWriter()
}
