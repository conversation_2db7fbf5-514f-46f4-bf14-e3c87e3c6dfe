<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800">
  <defs>
    <style>
      .client { fill: #e1bee7; stroke: #8e24aa; stroke-width: 2; }
      .service { fill: #c8e6c9; stroke: #4caf50; stroke-width: 2; }
      .module { fill: #bbdefb; stroke: #2196f3; stroke-width: 2; }
      .domain { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .dependency { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .storage { fill: #ffcdd2; stroke: #f44336; stroke-width: 2; }
      .middleware { fill: #e8f5e8; stroke: #66bb6a; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; }
      .title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; text-anchor: middle; }
      .arrow { stroke: #666; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="600" y="30" class="title" style="font-size: 18px;">Rank Server Architecture</text>
  
  <!-- External Users -->
  <rect x="50" y="60" width="100" height="40" class="client" rx="5"/>
  <text x="100" y="85" class="text">Games Admin</text>
  
  <rect x="200" y="60" width="100" height="40" class="client" rx="5"/>
  <text x="250" y="85" class="text">Games User</text>
  
  <rect x="350" y="60" width="100" height="40" class="client" rx="5"/>
  <text x="400" y="85" class="text">ODP</text>
  
  <!-- Gateway Layer -->
  <rect x="30" y="140" width="140" height="80" class="domain" rx="5"/>
  <text x="100" y="160" class="title">Gateway</text>
  <rect x="40" y="170" width="120" height="20" class="domain" rx="3"/>
  <text x="100" y="185" class="text">Admin/User/ODP Gateway</text>
  
  <!-- SPEX Layer -->
  <rect x="200" y="140" width="200" height="80" class="dependency" rx="5"/>
  <text x="300" y="160" class="title">SPEX</text>
  <text x="300" y="180" class="text">game_rank.proto</text>
  <text x="300" y="195" class="text">gameplatform_*.proto</text>
  
  <!-- Application Layer -->
  <rect x="50" y="250" width="700" height="300" class="domain" rx="5"/>
  <text x="400" y="275" class="title">Application</text>
  
  <!-- Rank Service -->
  <rect x="70" y="290" width="150" height="120" class="service" rx="5"/>
  <text x="145" y="310" class="title">Rank Service</text>
  <text x="145" y="330" class="text">V1.0: SetScore, IncrScore</text>
  <text x="145" y="345" class="text">V2.0: Enhanced APIs</text>
  <text x="145" y="360" class="text">V3.0: Admin Functions</text>
  
  <!-- Data Processing -->
  <rect x="250" y="290" width="150" height="120" class="module" rx="5"/>
  <text x="325" y="310" class="title">Data Processing</text>
  <text x="325" y="330" class="text">Rank Writer</text>
  <text x="325" y="345" class="text">ES Writer</text>
  <text x="325" y="360" class="text">TopN Processor</text>
  
  <!-- Middleware -->
  <rect x="430" y="290" width="150" height="120" class="middleware" rx="5"/>
  <text x="505" y="310" class="title">Middleware</text>
  <text x="505" y="330" class="text">Access Log, Tracing</text>
  <text x="505" y="345" class="text">Rate Limit, Recovery</text>
  <text x="505" y="360" class="text">Metrics</text>
  
  <!-- Background Tasks -->
  <rect x="610" y="290" width="130" height="120" class="module" rx="5"/>
  <text x="675" y="310" class="title">Background Tasks</text>
  <text x="675" y="330" class="text">Index Close Task</text>
  <text x="675" y="345" class="text">User Cache Task</text>
  <text x="675" y="360" class="text">Monitor Task</text>
  
  <!-- Gameplatform Services -->
  <rect x="800" y="250" width="150" height="120" class="service" rx="5"/>
  <text x="875" y="275" class="title">Gameplatform</text>
  <text x="875" y="300" class="text">Game Service</text>
  <text x="875" y="320" class="text">Friend Service</text>
  <text x="875" y="340" class="text">Asset Service</text>
  
  <!-- Storage Layer -->
  <rect x="100" y="600" width="600" height="120" class="storage" rx="5"/>
  <text x="400" y="625" class="title">Storage</text>
  
  <circle cx="150" cy="660" r="30" class="storage"/>
  <text x="150" y="665" class="text">Admin MySQL</text>
  
  <circle cx="250" cy="660" r="30" class="storage"/>
  <text x="250" y="665" class="text">User MySQL</text>
  
  <circle cx="350" cy="660" r="30" class="storage"/>
  <text x="350" y="665" class="text">Redis</text>
  
  <circle cx="450" cy="660" r="30" class="storage"/>
  <text x="450" y="665" class="text">Elasticsearch</text>
  
  <circle cx="550" cy="660" r="30" class="storage"/>
  <text x="550" y="665" class="text">Local Cache</text>
  
  <!-- Key Arrows -->
  <line x1="100" y1="100" x2="100" y2="140" class="arrow"/>
  <line x1="250" y1="100" x2="100" y2="140" class="arrow"/>
  <line x1="400" y1="100" x2="100" y2="140" class="arrow"/>
  
  <line x1="100" y1="220" x2="145" y2="290" class="arrow"/>
  <line x1="145" y1="410" x2="325" y2="290" class="arrow"/>
  <line x1="325" y1="410" x2="350" y2="600" class="arrow"/>
  <line x1="325" y1="410" x2="450" y2="600" class="arrow"/>
  
  <line x1="145" y1="410" x2="875" y2="250" class="arrow"/>
  
  <!-- Legend -->
  <rect x="1000" y="100" width="180" height="200" fill="none" stroke="#ccc" stroke-width="1" rx="5"/>
  <text x="1090" y="125" class="title">LEGEND</text>
  
  <rect x="1010" y="140" width="20" height="15" class="client"/>
  <text x="1040" y="152" class="text" style="text-anchor: start;">Client</text>
  
  <rect x="1010" y="160" width="20" height="15" class="service"/>
  <text x="1040" y="172" class="text" style="text-anchor: start;">Service</text>
  
  <rect x="1010" y="180" width="20" height="15" class="module"/>
  <text x="1040" y="192" class="text" style="text-anchor: start;">Module</text>
  
  <rect x="1010" y="200" width="20" height="15" class="domain"/>
  <text x="1040" y="212" class="text" style="text-anchor: start;">Domain</text>
  
  <rect x="1010" y="220" width="20" height="15" class="dependency"/>
  <text x="1040" y="232" class="text" style="text-anchor: start;">Dependency</text>
  
  <rect x="1010" y="240" width="20" height="15" class="storage"/>
  <text x="1040" y="252" class="text" style="text-anchor: start;">Storage</text>
  
  <rect x="1010" y="260" width="20" height="15" class="middleware"/>
  <text x="1040" y="272" class="text" style="text-anchor: start;">Middleware</text>
</svg>
