<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .section-title { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; text-anchor: middle; }
      .module-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; text-anchor: middle; }
      .text { font-family: Arial, sans-serif; font-size: 11px; text-anchor: middle; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; }
      
      .user { fill: #e1bee7; stroke: #8e24aa; stroke-width: 2; }
      .spex { fill: #c8e6c9; stroke: #4caf50; stroke-width: 2; }
      .application { fill: #bbdefb; stroke: #2196f3; stroke-width: 2; }
      .gameplatform { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .storage { fill: #ffcdd2; stroke: #f44336; stroke-width: 2; }
      .middleware { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
    </style>
  </defs>
  
  <!-- Title -->
  <text x="700" y="30" class="title">Rank Server Architecture</text>
  
  <!-- External Users -->
  <text x="700" y="60" class="section-title">External Users</text>
  
  <rect x="200" y="80" width="120" height="50" class="user" rx="5"/>
  <text x="260" y="100" class="module-title">Games Admin</text>
  <text x="260" y="115" class="text">Management Portal</text>
  
  <rect x="400" y="80" width="120" height="50" class="user" rx="5"/>
  <text x="460" y="100" class="module-title">Games User</text>
  <text x="460" y="115" class="text">Game Client</text>
  
  <rect x="600" y="80" width="120" height="50" class="user" rx="5"/>
  <text x="660" y="100" class="module-title">ODP</text>
  <text x="660" y="115" class="text">Operations Platform</text>
  
  <!-- SPEX Section -->
  <rect x="50" y="160" width="280" height="200" class="spex" rx="10" fill="none"/>
  <text x="190" y="180" class="section-title">SPEX</text>
  
  <rect x="70" y="200" width="240" height="30" class="spex" rx="3"/>
  <text x="190" y="220" class="text">grpc4spex.Init - gRPC Service Discovery</text>
  
  <rect x="70" y="240" width="240" height="30" class="spex" rx="3"/>
  <text x="190" y="260" class="text">grpc4spex.GetConn - Service Connection</text>
  
  <rect x="70" y="280" width="240" height="30" class="spex" rx="3"/>
  <text x="190" y="300" class="text">config_center.get - Configuration Center</text>
  
  <rect x="70" y="320" width="240" height="30" class="spex" rx="3"/>
  <text x="190" y="340" class="text">service_registry.register - Service Registry</text>
  
  <!-- Application Section -->
  <rect x="360" y="160" width="680" height="500" class="application" rx="10" fill="none"/>
  <text x="700" y="180" class="section-title">Application</text>
  
  <!-- Admin Gateway -->
  <rect x="380" y="200" width="150" height="140" class="application" rx="5"/>
  <text x="455" y="220" class="module-title">Admin Gateway</text>
  
  <rect x="390" y="230" width="130" height="20" class="application" rx="2"/>
  <text x="455" y="243" class="small-text">IncrScoreV3Admin</text>
  
  <rect x="390" y="255" width="130" height="20" class="application" rx="2"/>
  <text x="455" y="268" class="small-text">RankListV2</text>
  
  <rect x="390" y="280" width="130" height="20" class="application" rx="2"/>
  <text x="455" y="293" class="small-text">DeleteUserRankV2</text>
  
  <rect x="390" y="305" width="130" height="20" class="application" rx="2"/>
  <text x="455" y="318" class="small-text">QueryNewRankModule</text>
  
  <!-- User Gateway -->
  <rect x="550" y="200" width="150" height="140" class="application" rx="5"/>
  <text x="625" y="220" class="module-title">User Gateway</text>
  
  <rect x="560" y="230" width="130" height="20" class="application" rx="2"/>
  <text x="625" y="243" class="small-text">SetScoreV2/V3</text>
  
  <rect x="560" y="255" width="130" height="20" class="application" rx="2"/>
  <text x="625" y="268" class="small-text">IncrScoreV2/V3</text>
  
  <rect x="560" y="280" width="130" height="20" class="application" rx="2"/>
  <text x="625" y="293" class="small-text">TopNV2/V3</text>
  
  <rect x="560" y="305" width="130" height="20" class="application" rx="2"/>
  <text x="625" y="318" class="small-text">FriendsRankV3</text>
  
  <!-- Task Scheduler -->
  <rect x="720" y="200" width="150" height="140" class="application" rx="5"/>
  <text x="795" y="220" class="module-title">Task Scheduler</text>
  
  <rect x="730" y="230" width="130" height="20" class="application" rx="2"/>
  <text x="795" y="243" class="small-text">AsyncCloseEsIndex</text>
  
  <rect x="730" y="255" width="130" height="20" class="application" rx="2"/>
  <text x="795" y="268" class="small-text">doCloseEsIndex</text>
  
  <rect x="730" y="280" width="130" height="20" class="application" rx="2"/>
  <text x="795" y="293" class="small-text">tryAcquireTaskLock</text>
  
  <rect x="730" y="305" width="130" height="20" class="application" rx="2"/>
  <text x="795" y="318" class="small-text">ClearUserInfoLocalCache</text>
  
  <!-- Middleware -->
  <rect x="890" y="200" width="140" height="140" class="middleware" rx="5"/>
  <text x="960" y="220" class="module-title">Middleware</text>
  
  <rect x="900" y="230" width="120" height="18" class="middleware" rx="2"/>
  <text x="960" y="242" class="small-text">Access Log</text>
  
  <rect x="900" y="252" width="120" height="18" class="middleware" rx="2"/>
  <text x="960" y="264" class="small-text">Metrics</text>
  
  <rect x="900" y="274" width="120" height="18" class="middleware" rx="2"/>
  <text x="960" y="286" class="small-text">Tracing</text>
  
  <rect x="900" y="296" width="120" height="18" class="middleware" rx="2"/>
  <text x="960" y="308" class="small-text">Recovery</text>
  
  <rect x="900" y="318" width="120" height="18" class="middleware" rx="2"/>
  <text x="960" y="330" class="small-text">Health Check</text>
  
  <!-- Async Processing -->
  <rect x="380" y="360" width="320" height="120" class="application" rx="5"/>
  <text x="540" y="380" class="module-title">Async Processing</text>
  
  <rect x="390" y="390" width="140" height="35" class="application" rx="3"/>
  <text x="460" y="405" class="text">Rank Writer</text>
  <text x="460" y="418" class="small-text">Channel + Workers</text>
  
  <rect x="550" y="390" width="140" height="35" class="application" rx="3"/>
  <text x="620" y="405" class="text">ES Writer</text>
  <text x="620" y="418" class="small-text">Bulk Operations</text>
  
  <rect x="390" y="435" width="300" height="35" class="application" rx="3"/>
  <text x="540" y="450" class="text">Data Access Layer</text>
  <text x="540" y="463" class="small-text">Cache DAO | ES DAO | MySQL DAO</text>
  
  <!-- Business Logic Handlers -->
  <rect x="720" y="360" width="310" height="120" class="application" rx="5"/>
  <text x="875" y="380" class="module-title">Business Logic Handlers</text>
  
  <rect x="730" y="390" width="90" height="35" class="application" rx="3"/>
  <text x="775" y="405" class="text">Handler V1.0</text>
  <text x="775" y="418" class="small-text">Basic</text>
  
  <rect x="830" y="390" width="90" height="35" class="application" rx="3"/>
  <text x="875" y="405" class="text">Handler V2.0</text>
  <text x="875" y="418" class="small-text">Bucket &amp; TopN</text>
  
  <rect x="930" y="390" width="90" height="35" class="application" rx="3"/>
  <text x="975" y="405" class="text">Handler V3.0</text>
  <text x="975" y="418" class="small-text">Module Config</text>
  
  <rect x="730" y="435" width="140" height="35" class="application" rx="3"/>
  <text x="800" y="450" class="text">Friends Handler</text>
  <text x="800" y="463" class="small-text">Friend Rankings</text>
  
  <rect x="880" y="435" width="140" height="35" class="application" rx="3"/>
  <text x="950" y="450" class="text">Module Handler</text>
  <text x="950" y="463" class="small-text">Config Management</text>
  
  <!-- Storage Section -->
  <rect x="50" y="700" width="600" height="150" class="storage" rx="10" fill="none"/>
  <text x="350" y="720" class="section-title">Storage</text>
  
  <ellipse cx="150" cy="780" rx="80" ry="40" class="storage"/>
  <text x="150" y="775" class="module-title">Redis</text>
  <text x="150" y="790" class="small-text">Ranking Cache</text>
  <text x="150" y="803" class="small-text">Bucket Strategy</text>
  
  <ellipse cx="350" cy="780" rx="80" ry="40" class="storage"/>
  <text x="350" y="775" class="module-title">Elasticsearch</text>
  <text x="350" y="790" class="small-text">Search &amp; Analytics</text>
  <text x="350" y="803" class="small-text">Historical Data</text>
  
  <ellipse cx="550" cy="780" rx="80" ry="40" class="storage"/>
  <text x="550" y="775" class="module-title">MySQL</text>
  <text x="550" y="790" class="small-text">Configuration</text>
  <text x="550" y="803" class="small-text">Recovery Data</text>
  
  <!-- Gameplatform Section -->
  <rect x="700" y="700" width="600" height="150" class="gameplatform" rx="10" fill="none"/>
  <text x="1000" y="720" class="section-title">Gameplatform</text>
  
  <rect x="750" y="740" width="200" height="80" class="gameplatform" rx="5"/>
  <text x="850" y="760" class="module-title">Game Platform</text>
  <text x="850" y="775" class="small-text">Blacklist Service</text>
  <text x="850" y="790" class="small-text">Game Config</text>
  <text x="850" y="805" class="small-text">Anti-cheat</text>
  
  <rect x="980" y="740" width="200" height="80" class="gameplatform" rx="5"/>
  <text x="1080" y="760" class="module-title">Friend Service</text>
  <text x="1080" y="775" class="small-text">User Info</text>
  <text x="1080" y="790" class="small-text">Friend List</text>
  <text x="1080" y="805" class="small-text">Social Data</text>
  
  <!-- Legend -->
  <rect x="1100" y="160" width="250" height="200" fill="none" stroke="#666" stroke-width="1" rx="5"/>
  <text x="1225" y="180" class="section-title">LEGEND</text>
  
  <rect x="1120" y="200" width="30" height="20" class="user" rx="2"/>
  <text x="1160" y="213" class="text">External Users</text>
  
  <rect x="1120" y="230" width="30" height="20" class="spex" rx="2"/>
  <text x="1160" y="243" class="text">SPEX Services</text>
  
  <rect x="1120" y="260" width="30" height="20" class="application" rx="2"/>
  <text x="1160" y="273" class="text">Application Layer</text>
  
  <rect x="1120" y="290" width="30" height="20" class="gameplatform" rx="2"/>
  <text x="1160" y="303" class="text">Gameplatform</text>
  
  <rect x="1120" y="320" width="30" height="20" class="storage" rx="2"/>
  <text x="1160" y="333" class="text">Storage Layer</text>
</svg>
