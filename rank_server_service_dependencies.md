# Rank Server Service Dependencies

## Service Dependency Matrix

| Service Name | Redis | Elasticsearch | MySQL | Game Platform | Friend Service | SPEX | Kafka | Local Cache |
|--------------|-------|---------------|-------|---------------|----------------|------|-------|-------------|
| **Rank Handler V1.0** | YES | YES | NO | NO | NO | YES | NO | NO |
| **Rank Handler V2.0** | YES | YES | NO | YES | YES | YES | NO | YES |
| **Rank Handler V3.0** | YES | YES | YES | YES | YES | YES | NO | YES |
| **Friends Handler** | NO | YES | NO | NO | YES | YES | NO | YES |
| **Module Handler** | NO | NO | YES | NO | NO | YES | NO | NO |
| **Admin Handler** | YES | YES | NO | YES | NO | YES | NO | NO |
| **Rank Writer** | YES | YES | NO | NO | NO | NO | NO | NO |
| **ES Writer** | NO | YES | NO | NO | NO | NO | NO | NO |
| **Task Scheduler** | YES | YES | NO | NO | NO | YES | NO | NO |
| **gRPC Server** | NO | NO | NO | NO | NO | YES | NO | NO |
| **Middleware** | NO | NO | NO | NO | NO | YES | NO | NO |

## Detailed Dependency Analysis

### 1. Rank Handler V1.0 (`handler/rank_handler.go`)
- **Redis**: YES - Basic ranking operations using Redis sorted sets
- **Elasticsearch**: YES - Async write operations for ranking data
- **MySQL**: NO - Does not directly access database
- **Game Platform**: NO - Basic version without external service integration
- **Friend Service**: NO - No friend ranking functionality
- **SPEX**: YES - Service discovery and configuration
- **Kafka**: NO - Uses internal channels instead
- **Local Cache**: NO - No local caching mechanism

### 2. Rank Handler V2.0 (`handler/rank_handler_2.0.go`)
- **Redis**: YES - Enhanced with bucket strategy and TopN keys
- **Elasticsearch**: YES - Bulk write operations and complex queries
- **MySQL**: NO - Does not directly access database
- **Game Platform**: YES - Blacklist checking and game configuration
- **Friend Service**: YES - User info retrieval for ranking display
- **SPEX**: YES - Service discovery and configuration
- **Kafka**: NO - Uses internal channels for async processing
- **Local Cache**: YES - User info local caching to reduce RPC calls

### 3. Rank Handler V3.0 (`handler/rank_handler_3.0.go`)
- **Redis**: YES - Inherits V2.0 functionality
- **Elasticsearch**: YES - Direct ES operations for admin functions
- **MySQL**: YES - Module configuration management
- **Game Platform**: YES - Game configuration validation
- **Friend Service**: YES - User information retrieval
- **SPEX**: YES - Service discovery and configuration
- **Kafka**: NO - Uses internal channels
- **Local Cache**: YES - Enhanced caching mechanisms

### 4. Friends Handler (`handler/rank_handler_friends.go`)
- **Redis**: NO - Does not directly access Redis
- **Elasticsearch**: YES - Batch queries for friend ranking data
- **MySQL**: NO - Does not access database directly
- **Game Platform**: NO - Focused on friend functionality
- **Friend Service**: YES - Core dependency for friend list retrieval
- **SPEX**: YES - Service discovery
- **Kafka**: NO - Synchronous operations
- **Local Cache**: YES - Caches friend information

### 5. Module Handler (`handler/rank_handler_module.go`)
- **Redis**: NO - Configuration management only
- **Elasticsearch**: NO - Does not handle ranking data
- **MySQL**: YES - Stores and retrieves module configurations
- **Game Platform**: NO - Independent configuration service
- **Friend Service**: NO - No friend-related functionality
- **SPEX**: YES - Service discovery and configuration
- **Kafka**: NO - Simple CRUD operations
- **Local Cache**: NO - Direct database operations

### 6. Admin Handler (`handler/admin_es.go`, `handler/admin_es_v2.go`)
- **Redis**: YES - Cache synchronization for admin operations
- **Elasticsearch**: YES - Primary storage for admin queries and operations
- **MySQL**: NO - Does not access database directly
- **Game Platform**: YES - Blacklist management and validation
- **Friend Service**: NO - Admin operations don't require friend data
- **SPEX**: YES - Service discovery and configuration
- **Kafka**: NO - Direct operations
- **Local Cache**: NO - Admin operations are typically one-time

### 7. Rank Writer (`handler/rank_redis.go`)
- **Redis**: YES - Core functionality for writing ranking data
- **Elasticsearch**: YES - Async ES write operations
- **MySQL**: NO - Does not access database
- **Game Platform**: NO - Pure data processing component
- **Friend Service**: NO - Data processing only
- **SPEX**: NO - Internal component
- **Kafka**: NO - Uses internal channels
- **Local Cache**: NO - Stateless processing

### 8. ES Writer (`handler/rank_es_2.0.go`)
- **Redis**: NO - Elasticsearch operations only
- **Elasticsearch**: YES - Bulk write operations to ES
- **MySQL**: NO - Does not access database
- **Game Platform**: NO - Pure ES operations
- **Friend Service**: NO - Data writing only
- **SPEX**: NO - Internal component
- **Kafka**: NO - Internal processing
- **Local Cache**: NO - Stateless operations

### 9. Task Scheduler (`handler/close_index_task.go`)
- **Redis**: YES - Task locking mechanism
- **Elasticsearch**: YES - Index cleanup operations
- **MySQL**: NO - Does not access database
- **Game Platform**: NO - Independent maintenance tasks
- **Friend Service**: NO - System maintenance only
- **SPEX**: YES - Configuration for task parameters
- **Kafka**: NO - Simple scheduled tasks
- **Local Cache**: NO - Maintenance operations

### 10. gRPC Server (`mod/grpc_server/server.go`)
- **Redis**: NO - Pure protocol layer
- **Elasticsearch**: NO - Does not access storage directly
- **MySQL**: NO - Protocol layer only
- **Game Platform**: NO - Routing layer
- **Friend Service**: NO - Protocol handling
- **SPEX**: YES - Service registration and health checks
- **Kafka**: NO - gRPC protocol only
- **Local Cache**: NO - Stateless routing

### 11. Middleware (`mod/server/module.go`)
- **Redis**: NO - Cross-cutting concerns only
- **Elasticsearch**: NO - Logging and monitoring
- **MySQL**: NO - Does not access storage
- **Game Platform**: NO - Infrastructure layer
- **Friend Service**: NO - Middleware functionality
- **SPEX**: YES - Configuration and service discovery
- **Kafka**: NO - Internal middleware
- **Local Cache**: NO - Stateless middleware

## Dependency Patterns

### High Dependency Services
- **Rank Handler V2.0 & V3.0**: Most dependencies due to comprehensive functionality
- **Admin Handler**: High ES and external service dependencies

### Low Dependency Services
- **Module Handler**: Focused on configuration management
- **ES Writer**: Specialized for Elasticsearch operations
- **Middleware**: Infrastructure-only dependencies

### Critical Dependencies
- **SPEX**: Required by almost all services for discovery and configuration
- **Redis**: Core dependency for all ranking operations
- **Elasticsearch**: Essential for complex queries and admin operations

### Optional Dependencies
- **Local Cache**: Performance optimization, not critical for functionality
- **Kafka**: Not used in current implementation (uses internal channels)
- **MySQL**: Only required for configuration and recovery data
