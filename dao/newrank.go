package dao

import (
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	commlibMysql "git.garena.com/shopee/game_platform/comm_lib/mysql"
	"git.garena.com/shopee/game_platform/rank_server/dao/mysql"
	"git.garena.com/shopee/game_platform/rank_server/model"
	"github.com/jinzhu/gorm"
	"time"
)

type RankDao struct{}

func NewRankDao() *RankDao {
	return &RankDao{}
}

func (d *RankDao) CreateNewRank(tab *model.NewRankTab) (int32, error) {
	defer func(table, action string, start time.Time) {
		commlibMysql.AddMysqlRequest(table, action, time.Since(start))
	}(model.NewRankTab{}.TableName(), "create", time.Now())

	err := mysql.MasterDb.Create(tab).Error

	gamelog.Info("debug info CreateNewRank", gamelog.Fields{"err": err})

	if err != nil {
		gamelog.Error("dao create newrank error", gamelog.Fields{"tab": tab, "err": err})
		return 0, err
	}

	return tab.ModuleId, nil
}

func (d *RankDao) CreateNewRankConfigVersion(tab *model.NewRankConfigVersionTab) error {
	defer func(table, action string, start time.Time) {
		commlibMysql.AddMysqlRequest(table, action, time.Since(start))
	}(tab.TableName(), "create", time.Now())

	err := mysql.MasterDb.Create(tab).Error

	gamelog.Info("debug info CreateNewRankConfigVersion", gamelog.Fields{"err": err})

	if err != nil {
		gamelog.Error("dao create newrank config error", gamelog.Fields{"tab": tab, "err": err})
		return err
	}
	return nil
}

func (d *RankDao) UpdateNewRankConfigDraft(tab *model.NewRankConfigVersionTab) error {
	defer func(table, action string, start time.Time) {
		commlibMysql.AddMysqlRequest(table, action, time.Since(start))
	}(tab.TableName(), "update", time.Now())

	tab.ConfigDataVersion += 1
	tab.Mtime = time.Now().Unix()
	err := mysql.MasterDb.Model(tab).Where("module_id = ? AND is_draft = 1", tab.ModuleId).Save(tab).Error
	if err != nil {
		gamelog.Error("dao update newrank draft error", gamelog.Fields{"tab": tab, "err": err})
		return err
	}
	return nil
}

func (d *RankDao) QueryNewRankConfigDraft(moduleId int32) (*model.NewRankConfigVersionTab, error) {
	defer func(table, action string, start time.Time) {
		commlibMysql.AddMysqlRequest(table, action, time.Since(start))
	}(model.NewRankConfigVersionTab{}.TableName(), "query", time.Now())

	ret := model.NewRankConfigVersionTab{}
	err := mysql.MasterDb.Table(model.NewRankConfigVersionTab{}.TableName()).Where("module_id = ? AND is_draft = 1", moduleId).Limit(1).Find(&ret).Error

	gamelog.Info("debug info QueryNewRankConfigDraft", gamelog.Fields{"err": err})

	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, nil
		}
		gamelog.Error("dao query newrank draft error", gamelog.Fields{"moduleId": moduleId, "err": err})
		return nil, err
	}
	return &ret, nil
}

func (d *RankDao) QueryNewRankConfigLive(moduleId int32) (*model.NewRankConfigVersionTab, error) {
	defer func(table, action string, start time.Time) {
		commlibMysql.AddMysqlRequest(table, action, time.Since(start))
	}(model.NewRankConfigVersionTab{}.TableName(), "query", time.Now())

	ret := model.NewRankConfigVersionTab{}
	err := mysql.MasterDb.Table(model.NewRankConfigVersionTab{}.TableName()).Where("module_id = ? AND is_draft = 0", moduleId).Order("config_id desc").Limit(1).Find(&ret).Error

	gamelog.Info("debug info QueryNewRankConfigLive", gamelog.Fields{"err": err, "ret": ret})

	if err != nil {
		/*	if gorm.IsRecordNotFoundError(err) {
			return nil, nil
		}*/
		gamelog.Error("dao query newrank live error", gamelog.Fields{"moduleId": moduleId, "err": err})
		return nil, err
	}
	return &ret, nil
}

func (d *RankDao) QueryNewRankConfigById(configId int32) (*model.NewRankConfigVersionTab, error) {
	defer func(table, action string, start time.Time) {
		commlibMysql.AddMysqlRequest(table, action, time.Since(start))
	}(model.NewRankConfigVersionTab{}.TableName(), "query", time.Now())

	ret := model.NewRankConfigVersionTab{}
	err := mysql.MasterDb.Table(model.NewRankConfigVersionTab{}.TableName()).Where("config_id = ?", configId).Find(&ret).Error

	gamelog.Info("debug info QueryNewRankConfigById", gamelog.Fields{"err": err})

	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, nil
		}
		gamelog.Error("dao query chat config", gamelog.Fields{"configId": configId, "err": err})
		return nil, err
	}
	return &ret, nil
}

func (d *RankDao) ListNewRankConfig(modelIds []int32) ([]*model.NewRankConfigVersionTab, error) {
	defer func(table, action string, start time.Time) {
		commlibMysql.AddMysqlRequest(table, action, time.Since(start))
	}(model.NewRankConfigVersionTab{}.TableName(), "query", time.Now())

	ret := make([]*model.NewRankConfigVersionTab, 0)
	if len(modelIds) == 0 {
		return ret, nil
	}

	err := mysql.MasterDb.Raw("SELECT t1.config_id,t1.module_id,t1.config_data_version,t1.config_data "+
		"FROM gameplatform_newrank_config_version_tab AS t1 "+
		"JOIN "+
		"(SELECT max(config_id) AS config_id "+
		"FROM gameplatform_newrank_config_version_tab WHERE module_id IN (?) AND is_draft = 0 GROUP BY module_id) AS t2 "+
		"WHERE t1.config_id = t2.config_id", modelIds).Scan(&ret).Error

	gamelog.Info("debug info ListNewRankConfig", gamelog.Fields{"err": err})

	return ret, err
}
