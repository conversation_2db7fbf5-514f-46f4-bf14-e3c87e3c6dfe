package mysql

import (
	"fmt"
	"git.garena.com/shopee/game_platform/rank_server/config"
	"git.garena.com/shopee/mts/go-application-server/gas"
	"git.garena.com/shopee/mts/go-application-server/testing/gastest"
	"os"
	"testing"
)

func printRedMsg(err error) {
	if err == nil {
		return
	}

	fmt.Printf("\033[1;37;41m%s\033[0m\n", err.Error())
	panic(err)
}

func printGreenMsg(msg string) {
	fmt.Printf("\033[1;32;40m%s\033[0m\n", msg)
}

func TestMysqlUtil(t *testing.T) {
	// Test Set
	testBM := gas.Options(
		gas.Object(new(config.ConfigWrapper)),
		gas.Object(new(DBInitialize)),
	)

	configFile, err := os.Open("../../etc/server.yml")
	if err != nil {
		t.Error(err)
	}

	shutdown := gastest.Setup(t, configFile, testBM)
	defer shutdown()

	data, err := SelectUserRankRecoveryData("", "", "", 0, []uint64{0})

	fmt.Printf("\033[1;37;41m%s\033[0m\n", err.Error())
	fmt.Printf("\033[1;32;40m%s\033[0m\n", fmt.Sprintf("data = %v", data))
}
