package mysql

import (
	"git.garena.com/shopee/game_platform/comm_lib/sddl"
	hardyspi "git.garena.com/shopee/mts/go-application-server/spi/db/hardy"
	"gorm.io/gorm"
)

var (
	MasterDb *gorm.DB
	SlaveDb  *gorm.DB
)

type DBInitialize struct {
	rankConnector hardyspi.Connector `inject:"gameplatform_game_db"`
}

func (d *DBInitialize) Init() error {
	rankConn := hardyspi.OpenDB(d.rankConnector)
	rankDB := sddl.ConfigSDDL(sddl.WithDBConn(rankConn))
	MasterDb = sddl.Master(rankDB)
	SlaveDb = sddl.Slave(rankDB)
	MasterDb.Statement.RaiseErrorOnNotFound = true
	SlaveDb.Statement.RaiseErrorOnNotFound = true

	return nil
}
