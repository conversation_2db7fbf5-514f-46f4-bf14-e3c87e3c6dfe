package mysql

import (
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/comm_lib/metrics"
	commlibMysql "git.garena.com/shopee/game_platform/comm_lib/mysql"
	"git.garena.com/shopee/game_platform/rank_server/model"
	"github.com/jinzhu/gorm"
	"strings"
	"time"
)

func InsertUserRankRecoveryData(data *model.UserRankRecovery) error {
	defer func(table, action string, start time.Time) {
		commlibMysql.AddMysqlRequest(table, action, time.Since(start))
	}(data.TableName(), "insert", time.Now())

	err := MasterDb.Table(data.TableName()).Create(data).Error

	gamelog.Info("debug info InsertUserRankRecoveryData", gamelog.Fields{"data": data, "err": err})

	if err != nil {
		gamelog.Error("insert failed", gamelog.Fields{
			"err":  err,
			"data": data,
		})
		metrics.ReportCounterWithLabels(metrics.RPC_FAILED, map[string]string{
			"from": "rank_server",
			"to":   "mysql",
			"func": "InsertUserRankRecoveryData",
		})
		return err
	}

	return nil
}

func UpdateUserRankRecoveryData(data *model.UserRankRecovery) error {
	defer func(table, action string, start time.Time) {
		commlibMysql.AddMysqlRequest(table, action, time.Since(start))
	}(data.TableName(), "update", time.Now())

	err := MasterDb.Table(data.TableName()).Save(data).Error

	gamelog.Info("debug info UpdateUserRankRecoveryData", gamelog.Fields{"err": err})

	if err != nil {
		gamelog.Error("update failed", gamelog.Fields{
			"err":  err,
			"data": data,
		})
		metrics.ReportCounterWithLabels(metrics.RPC_FAILED, map[string]string{
			"from": "rank_server",
			"to":   "mysql",
			"func": "UpdateUserRankRecoveryData",
		})
		return err
	}

	return nil
}

func UpdateRecoveryByIds(operator string, ids []uint64) error {
	tableName := model.UserRankRecovery{}.TableName()
	defer func(table, action string, start time.Time) {
		commlibMysql.AddMysqlRequest(table, action, time.Since(start))
	}(tableName, "update", time.Now())

	if len(ids) == 0 {
		return nil
	}
	query := "id in (?)"
	err := MasterDb.Table(tableName).Where(query, ids).Updates(map[string]interface{}{"recover": 1, "operator": operator}).Error

	gamelog.Info("debug info UpdateRecoveryByIds", gamelog.Fields{"err": err})

	if err != nil {
		gamelog.Error("update recovery failed", gamelog.Fields{"err": err, "ids": ids})
		return err
	}

	return nil
}

func SelectUserRankRecoveryData(scope, rankName, rankWeek string, rankType int32, uids []uint64) ([]*model.UserRankRecovery, error) {
	tableName := model.UserRankRecovery{}.TableName()
	defer func(table, action string, start time.Time) {
		commlibMysql.AddMysqlRequest(table, action, time.Since(start))
	}(tableName, "select", time.Now())

	records := make([]*model.UserRankRecovery, 0)
	query := "scope=? and rank_name=? and rank_week=? and rank_type=? and recover=?"
	var args []interface{}
	args = append(args, scope, rankName, rankWeek, rankType, 0)
	if len(uids) > 0 {
		query += " and user_id in ("
		placeholder := make([]string, 0, len(uids))
		for _, v := range uids {
			placeholder = append(placeholder, "?")
			args = append(args, v)
		}
		query += strings.Join(placeholder, ",") + ")"
	}
	err := MasterDb.Table(tableName).Where(query, args...).Order("create_time desc").Offset(0).Limit(10000).Find(&records).Error

	gamelog.Info("debug info SelectUserRankRecoveryData", gamelog.Fields{"err": err})

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// record not found
			return []*model.UserRankRecovery{}, nil
		}

		gamelog.Error("select failed", gamelog.Fields{"err": err})
		return nil, err
	}

	return records, nil
}
