package dao

import (
	"fmt"
	"testing"
	"time"
)

func init() {
}

func TestSelectUserRankRecoveryData(t *testing.T) {
	//data, err := SelectUserRankRecoveryData("gamear", "231", "45", 1, []uint64{10805})
	data, err := SelectUserRankRecoveryData("gamear", "231", "45", 1, []uint64{})
	if err != nil {
		time.Sleep(500 * time.Millisecond)
		t.<PERSON>al(err)
	}
	t.Log("success, ", fmt.Sprintf("%+v", data[0]))
	time.Sleep(500 * time.Millisecond)
}
