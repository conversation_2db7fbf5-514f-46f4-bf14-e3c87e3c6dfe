package dao

import (
	"context"
	"fmt"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/comm_lib/utils"
	"git.garena.com/shopee/game_platform/rank_server/constant"
	"git.garena.com/shopee/game_platform/rank_server/dao/cache"
	"git.garena.com/shopee/game_platform/rank_server/dao/mysql"
	"time"

	"git.garena.com/shopee/game_platform/rank_server/model"
	"math/rand"
)

func InsertUserRankRecoveryData(data *model.UserRankRecovery) error {
	return mysql.InsertUserRankRecoveryData(data)
}

func UpdateUserRankRecoveryData(data *model.UserRankRecovery) error {
	return mysql.UpdateUserRankRecoveryData(data)
}

func UpdateRecoveryByIds(operator string, ids []uint64) error {
	return mysql.UpdateRecoveryByIds(operator, ids)
}

func SelectUserRankRecoveryData(scope, rankName, rankWeek string, rankType int32, uids []uint64) ([]*model.UserRankRecovery, error) {
	return mysql.SelectUserRankRecoveryData(scope, rankName, rankWeek, rankType, uids)
}

// ms
func SetUserRankTimestamp(key string, ts int64) error {
	gamelog.Debug("user rank ts key", gamelog.Fields{"key": key})

	var exp int64
	exp = constant.UsersRankTimestampExp
	if utils.IsStressTest(context.Background()) {
		exp = constant.UsersRankTimestampExpStress + rand.Int63n(constant.UsersRankTimestampExpStress)
	}

	return cache.RedisUtilSet(context.Background(), key, fmt.Sprintf("%v", ts), time.Duration(exp)*time.Second)
}

func GetUsersRankTimestamp(keys []string) ([]string, error) {
	gamelog.Debug("user rank ts keys", gamelog.Fields{"keys": keys, "len": len(keys)})
	const step = 100
	st, ed := 0, step
	if ed > len(keys) {
		ed = len(keys)
	}

	allInfos := make([]string, 0, len(keys))
	for {
		partialInfos, err := cache.RedisUtilMGet(context.Background(), keys[st:ed])
		if err != nil {
			return nil, err
		}

		allInfos = append(allInfos, partialInfos...)
		st = ed
		ed += step
		if ed > len(keys) {
			ed = len(keys)
		}
		if st >= len(keys) {
			break
		}
	}
	return allInfos, nil
}
