package dao

import (
	"git.garena.com/shopee/game_platform/proto/game_rank"
	"git.garena.com/shopee/game_platform/rank_server/model"
	"github.com/golang/protobuf/proto"
	"testing"
	"time"
)

func init() {
}

func TestAddNewRank(t *testing.T) {
	now := time.Now().Unix()
	id, err := NewRankDao().CreateNewRank(&model.NewRankTab{
		Ctime: now,
		Mtime: now,
	})

	if err != nil {
		t.Error(err)
	}

	t.Log(id)
}

func TestAddNewRankConfigVersion(t *testing.T) {
	now := time.Now().Unix()
	jd := model.RankJSON(game_rank.NewRankModuleConfig{RefreshCycle: proto.Int32(int32(game_rank.Cycle_Week))})
	err := NewRankDao().CreateNewRankConfigVersion(&model.NewRankConfigVersionTab{
		ModuleId:          1,
		IsDraft:           1,
		ConfigData:        &jd,
		ConfigDataVersion: 1,
		Ctime:             now,
		Mtime:             now,
	})

	if err != nil {
		t.Error(err)
	}
}

func TestQueryAndUpdate(t *testing.T) {
	tab, err := NewRankDao().QueryNewRankConfigDraft(1)
	if err != nil {
		t.Error(err)
	}

	if tab == nil {
		t.Error("tab not found")
	}

	jd := model.RankJSON(game_rank.NewRankModuleConfig{RefreshCycle: proto.Int32(int32(game_rank.Cycle_Month))})
	tab.ConfigData = &jd
	err = NewRankDao().UpdateNewRankConfigDraft(tab)
	if err != nil {
		t.Error(err)
	}
}

func TestRankDao_QueryNewRankConfigById(t *testing.T) {
	tab, err := NewRankDao().QueryNewRankConfigLive(1)
	if err != nil {
		t.Error(err)
	}

	if tab == nil {
		t.Error("tab not found")
	}
}
