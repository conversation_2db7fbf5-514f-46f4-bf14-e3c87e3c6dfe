package cache

import (
	"fmt"

	"git.garena.com/shopee/game_platform/comm_lib/redisutil"
	cachespi "git.garena.com/shopee/mts/go-application-server/spi/cache"
)

var rdbCmder cachespi.RedisCmder

type RedisClientWrapper struct {
	RDB      cachespi.RedisCache `inject:"redis_cache"` //不要直接使用 RDB ，请使用 RDBCmder，否则 hook 不生效
	RDBCmder cachespi.RedisCmder
}

func (r *RedisClientWrapper) Init() error {
	r.RDBCmder = r.RDB.RedisCmder()
	r.RDBCmder.AddHook(&redisutil.Hook{})

	rdbCmder = r.RDBCmder

	fmt.Println("redis init")
	return nil
}
