package cache

import (
	"context"
	"fmt"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/game_platform/comm_lib/env"
	"git.garena.com/shopee/game_platform/comm_lib/gamelog"
	"git.garena.com/shopee/game_platform/comm_lib/utils"
	"git.garena.com/shopee/mts/go-application-server/spi/cache"
	"go.uber.org/zap"
	"time"
)

type ZsetElem struct {
	Value string
	Score float64
}

/*
@author: haoyuan.yu
接入 GAS 时，为了解决大量使用 redis_utils 的兼容问题，将使用的相关函数，在此文件中重写
*/

var (
	redisPrefix string
)

func init() {
	tmpEnv, region := env.GetEnv(), env.GetCID()
	redisPrefix = fmt.Sprintf("gameabr_%s_%s.", tmpEnv, region)
}

func GetRedisPrefix() string {
	if len(redisPrefix) == 0 {
		tmpEnv, region := env.GetEnv(), env.GetCID()
		redisPrefix = fmt.Sprintf("gameabr_%s_%s.", tmpEnv, region)
	}
	return redisPrefix
}

func RedisUtilGet(ctx context.Context, key string) (string, error) {
	key = GetRedisPrefix() + key

	tmpRes, err := rdbCmder.Get(ctx, key).Result()

	gamelog.DebugWithContext(ctx, "RedisUtilGet Debug Info", gamelog.Fields{"key": key, "tmpRes": tmpRes, "err": err})

	return tmpRes, err
}

func RedisUtilSet(ctx context.Context, key, val string, duration time.Duration) error {
	key = GetRedisPrefix() + key
	var err error
	_, err = rdbCmder.Set(ctx, key, val, duration).Result()

	gamelog.DebugWithContext(ctx, "RedisUtilSet Debug Info", gamelog.Fields{"key": key, "val": val, "err": err})

	return err
}

func RedisUtilSetNX(ctx context.Context, key string, val interface{}, duration time.Duration) (bool, error) {
	key = GetRedisPrefix() + key
	var err error
	var isOk bool

	isOk, err = rdbCmder.SetNX(ctx, key, val, duration).Result()
	if err == cache.RedisNil {
		isOk = false
		err = nil
	}
	gamelog.DebugWithContext(ctx, "RedisUtilSetNX Debug Info", gamelog.Fields{"key": key, "val": val, "isOk": isOk, "err": err})

	return isOk, err
}

func RedisUtilDelete(ctx context.Context, keys ...string) (int64, error) {
	keysList := []string{}
	for _, key := range keys {
		tmpKey := GetRedisPrefix() + key
		keysList = append(keysList, tmpKey)
	}

	gamelog.DebugWithContext(ctx, "RedisUtilDelete Debug Info", gamelog.Fields{"keys": keys})

	return rdbCmder.Del(ctx, keysList...).Result()
}

func RedisUtilZCard(ctx context.Context, key string) (uint64, error) {
	key = GetRedisPrefix() + key
	cardInt64, err := rdbCmder.ZCard(ctx, key).Result()
	card := uint64(cardInt64)

	gamelog.DebugWithContext(ctx, "RedisUtilZCard Debug Info", gamelog.Fields{"key": key, "card": card, "err": err})

	return card, err
}

func RedisUtilZScore(ctx context.Context, key string, member string) (float64, error) {
	key = GetRedisPrefix() + key
	redisScore, err := rdbCmder.ZScore(ctx, key, member).Result()

	gamelog.DebugWithContext(ctx, "RedisUtilZScore Debug Info", gamelog.Fields{"key": key, "member": member, "redisScore": redisScore, "err": err})

	return redisScore, err
}

func RedisUtilBatchZRem(ctx context.Context, key string, members ...interface{}) (uint64, error) {
	key = GetRedisPrefix() + key
	remCount, err := rdbCmder.ZRem(ctx, key, members...).Result()

	gamelog.DebugWithContext(ctx, "RedisUtilBatchZRem Debug Info", gamelog.Fields{"key": key, "remCount": remCount, "err": err})

	return uint64(remCount), err
}

func RedisUtilZRemRangeByRank(ctx context.Context, key string, start, stop int64) (uint64, error) {
	key = GetRedisPrefix() + key
	remCount, err := rdbCmder.ZRemRangeByRank(ctx, key, start, stop).Result()

	gamelog.DebugWithContext(ctx, "RedisUtilZRemRangeByRank Debug Info", gamelog.Fields{"key": key, "remCount": remCount, "err": err})

	return uint64(remCount), err
}

func RedisUtilZAdd(ctx context.Context, key string, addScore float64, member interface{}) (int64, error) {
	key = GetRedisPrefix() + key

	addCount, err := rdbCmder.ZAdd(ctx, key, &cache.Z{Score: addScore, Member: member}).Result()

	gamelog.DebugWithContext(ctx, "RedisUtilZAdd Debug Info", gamelog.Fields{"key": key, "member": member, "addScore": addScore, "err": err})

	return addCount, err
}

func RedisUtilZAddList(ctx context.Context, key string, elem []*ZsetElem) (int64, error) {
	key = GetRedisPrefix() + key
	args := make([]*cache.Z, 0, len(elem))
	for _, tmpInfo := range elem {
		args = append(args, &cache.Z{
			Member: tmpInfo.Value,
			Score:  tmpInfo.Score,
		})
	}

	addCount, err := rdbCmder.ZAdd(ctx, key, args...).Result()

	gamelog.DebugWithContext(ctx, "RedisUtilZAddList Debug Info", gamelog.Fields{"key": key, "addCount": addCount, "err": err})

	return addCount, err
}

func RedisUtilZRange(ctx context.Context, key string, min, max int64) ([]ZsetElem, error) {
	key = GetRedisPrefix() + key
	tmpRes, err := rdbCmder.ZRangeWithScores(ctx, key, min, max).Result()

	gamelog.DebugWithContext(ctx, "RedisUtilZRange Debug Info", gamelog.Fields{"key": key, "min": min, "max": max, "tmpRes": tmpRes, "err": err})

	if err != nil {
		return nil, err
	}

	res := make([]ZsetElem, 0, len(tmpRes))
	for _, tmpZ := range tmpRes {
		tempString, ok := tmpZ.Member.(string)
		if !ok {
			continue
		}

		res = append(res, ZsetElem{
			Value: tempString,
			Score: tmpZ.Score,
		})
	}

	return res, nil
}

func RedisUtilZRevRange(ctx context.Context, key string, min, max int64) ([]ZsetElem, error) {
	key = GetRedisPrefix() + key
	tmpRes, err := rdbCmder.ZRevRangeWithScores(ctx, key, min, max).Result()

	gamelog.DebugWithContext(ctx, "RedisUtilZRevRange Debug Info", gamelog.Fields{"key": key, "min": min, "max": max, "tmpRes": tmpRes, "err": err})

	if err != nil {
		return nil, err
	}

	res := make([]ZsetElem, 0, len(tmpRes))
	for _, tmpZ := range tmpRes {
		tempString, ok := tmpZ.Member.(string)
		if !ok {
			continue
		}

		res = append(res, ZsetElem{
			Value: tempString,
			Score: tmpZ.Score,
		})
	}

	return res, nil
}

func RedisUtilExpire(ctx context.Context, key string, duration time.Duration) (bool, error) {
	key = GetRedisPrefix() + key
	return rdbCmder.Expire(ctx, key, duration).Result()
}

func RedisUtilZRank(ctx context.Context, key, member string) (int64, error) {
	key = GetRedisPrefix() + key
	tmpRes, err := rdbCmder.ZRank(ctx, key, member).Result()

	gamelog.DebugWithContext(ctx, "RedisUtilZRank Debug Info", gamelog.Fields{"key": key, "member": member, "tmpRes": tmpRes, "err": err})

	return tmpRes, err
}

func RedisUtilZRevRank(ctx context.Context, key, member string) (int64, error) {
	key = GetRedisPrefix() + key
	tmpRes, err := rdbCmder.ZRevRank(ctx, key, member).Result()

	gamelog.DebugWithContext(ctx, "RedisUtilZRevRank Debug Info", gamelog.Fields{"key": key, "member": member, "tmpRes": tmpRes, "err": err})
	return tmpRes, err

}

func GetCachedZRange(ctx context.Context, key string, start, stop int64, d time.Duration) ([]ZsetElem, error) {
	cacheKey := fmt.Sprintf("%s.%s_from%d_to%d", "ZRANGE", key, start, stop)

	fn := func() (interface{}, error) {
		return RedisUtilZRange(ctx, key, start, stop)
	}

	res, err := utils.CacheHelper(ctx, cacheKey, fn, d)
	if err != nil {
		return nil, err
	}

	gamelog.DebugWithContext(ctx, "GetCachedZRange Debug Info", gamelog.Fields{"key": key, "start": start, "stop": stop, "res": res, "err": err})

	return res.([]ZsetElem), nil
}

func GetCachedZRevRange(ctx context.Context, key string, start, stop int64, d time.Duration) ([]ZsetElem, error) {
	cacheKey := fmt.Sprintf("%s.%s_from%d_to%d", "ZREVRANGE", key, start, stop)

	fn := func() (interface{}, error) {
		return RedisUtilZRevRange(ctx, key, start, stop)
	}

	res, err := utils.CacheHelper(ctx, cacheKey, fn, d)
	if err != nil {
		return nil, err
	}

	gamelog.DebugWithContext(ctx, "GetCachedZRevRange Debug Info", gamelog.Fields{"key": key, "start": start, "stop": stop, "res": res, "err": err})

	return res.([]ZsetElem), nil
}

func RedisUtilExists(ctx context.Context, key string) (int64, error) {
	key = GetRedisPrefix() + key
	return rdbCmder.Exists(ctx, key).Result()
}

func GetPipeline() cache.RedisCmderPipeline {
	return rdbCmder.Pipeline()
}

func RedisUtilMGet(ctx context.Context, keys []string) ([]string, error) {
	prefixKeysList := []string{}
	for _, key := range keys {
		tmpKey := GetRedisPrefix() + key
		prefixKeysList = append(prefixKeysList, tmpKey)
	}

	infoI, err := rdbCmder.MGet(ctx, prefixKeysList...).Result()
	if err != nil {
		logkit.FromContext(ctx).Error("Mgets failed", zap.Error(err))
		return nil, err
	}
	var infos []string

	for index, _ := range infoI {
		switch infoI[index].(type) {
		case string:
			infos = append(infos, infoI[index].(string))
		case []uint8:
			tmpString := string(infoI[index].([]uint8))
			infos = append(infos, tmpString)
		default:
			// nil
			infos = append(infos, "")
		}
	}

	gamelog.DebugWithContext(ctx, "RedisUtilMGet Debug Info", gamelog.Fields{"infos": infos, "err": err})

	return infos, nil
}
