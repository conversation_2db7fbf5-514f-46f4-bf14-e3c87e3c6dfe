package cache

import (
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/game_platform/comm_lib/env"
	"git.garena.com/shopee/game_platform/rank_server/config"
	"git.garena.com/shopee/game_platform/rank_server/dao/mysql"
	"git.garena.com/shopee/mts/go-application-server/gas"
	spiCache "git.garena.com/shopee/mts/go-application-server/spi/cache"
	"git.garena.com/shopee/mts/go-application-server/testing/gastest"
	"math/rand"
	"os"
	"strconv"
	"testing"
	"time"
)

func RedisPrefix() string {
	env, region := env.GetEnv(), env.GetCID()
	return fmt.Sprintf("gameabr_%s_%s.", env, region)
}

func init() {

}

func printRedMsg(err error) {
	if err == nil {
		return
	}

	fmt.Printf("\033[1;37;41m%s\033[0m\n", err.Error())
	panic(err)
}

func printGreenMsg(msg string) {
	fmt.Printf("\033[1;32;40m%s\033[0m\n", msg)
}

func TestRedisUtil(t *testing.T) {

	// Test Set
	testBM := gas.Options(
		gas.Object(new(config.ConfigWrapper)),
		gas.Object(new(RedisClientWrapper)),
		gas.Object(new(mysql.DBInitialize)),
	)

	configFile, err := os.Open("../../etc/server.yml")
	if err != nil {
		printRedMsg(err)
	}

	shutdown := gastest.Setup(t, configFile, testBM)
	defer shutdown()

	testKeyV1 := fmt.Sprintf("test_key_name_%d", rand.Int31())
	value := rand.Int31()
	ctx := context.Background()
	defer RedisUtilDelete(ctx, testKeyV1)

	//
	testConfigCenter(ctx)

	// test set, setnx
	redisSetNxUT(t, ctx, testKeyV1, value)

	testZSetKey := fmt.Sprintf("test_key_name_%d", rand.Int31())
	defer RedisUtilDelete(ctx, testZSetKey)

	// test single user
	redisZSetSingleUserUT(ctx, testZSetKey)

	// test redis nil
	redisNilUT(ctx, testZSetKey)

	// test add list
	redisZSetAddUserListUT(ctx, testZSetKey)

	// test hash
	testHashKey := fmt.Sprintf("test_hash_key_%d", rand.Int31())
	defer RedisUtilDelete(ctx, testHashKey)

	// test mget
	redisMGetUT(ctx)

	pipelineUT(ctx)
}

func redisSetNxUT(t *testing.T, ctx context.Context, testKey string, value int32) {
	printGreenMsg("------------- Test RedisUtilSet  RedisUtilSetNx ------------- ")
	expiration := int64(10)
	defer RedisUtilDelete(ctx, testKey)

	isOk, err := RedisUtilSetNX(ctx, testKey, value, time.Duration(expiration)*time.Second)
	if err != nil {
		printRedMsg(err)
	}
	if !isOk {
		printRedMsg(errors.New("ok should be true"))
	}

	tmpValueS, err := RedisUtilGet(ctx, testKey)
	if err != nil {
		printRedMsg(err)
	}
	tmpValue, _ := strconv.ParseInt(tmpValueS, 10, 64)
	if int32(tmpValue) != value {
		printRedMsg(errors.New("tmpValue != value"))
	}

	valueV2 := rand.Int()
	isOk, err = RedisUtilSetNX(ctx, testKey, valueV2, time.Duration(expiration)*time.Second)
	// NOTE：这里 SPI 会返回自定义的 redis nil error
	if err != spiCache.RedisNil {
		printRedMsg(err)
	}
	if isOk { // 应当失败
		printRedMsg(errors.New("ok should be false"))
	}

	err = RedisUtilSet(ctx, testKey, fmt.Sprintf("%v", valueV2), time.Duration(expiration)*time.Second)
	if err != nil {
		printRedMsg(err)
	}
	tmpValueS, err = RedisUtilGet(ctx, testKey)
	if err != nil {
		printRedMsg(err)
	}
	tmpValue, _ = strconv.ParseInt(tmpValueS, 10, 64)
	if int(tmpValue) != valueV2 {
		printRedMsg(errors.New("tmpValue != value"))
	}

	printGreenMsg("------------- Test RedisUtilSet  RedisUtilSetNx  Success ------------- ")
}

func redisZSetSingleUserUT(ctx context.Context, testKey string) {
	printGreenMsg("------------- Test redisZSetSingleUserUT ------------- ")
	randomUid := fmt.Sprintf("%d", rand.Int31())
	randomScore := rand.Float64()
	_, err := RedisUtilZAdd(ctx, testKey, randomScore, randomUid)
	if err != nil {
		printRedMsg(err)
	}

	userCount, err := RedisUtilZCard(ctx, testKey)
	if err != nil {
		printRedMsg(err)
	}
	if userCount != 1 {
		printRedMsg(errors.New("userCount should be 1"))
	}

	userScore, err := RedisUtilZScore(ctx, testKey, randomUid)
	if err != nil {
		printRedMsg(err)
	}
	if userScore != randomScore {
		printRedMsg(errors.New("userScore != randomScore"))
	}

	userRank, err := RedisUtilZRank(ctx, testKey, randomUid)
	if err != nil {
		printRedMsg(err)
	}
	if userRank != 0 {
		printRedMsg(errors.New(fmt.Sprintf("userRank != 0, rank = %d", userRank)))
	}

	userInfo, err := RedisUtilZRange(ctx, testKey, 0, 1)
	if err != nil {
		printRedMsg(err)
	}
	if userInfo[0].Value != randomUid {
		printRedMsg(errors.New("userInfo[0].Value != randomUid"))
	}

	// 查询不存在的用户
	userRank, err = RedisUtilZRank(ctx, testKey, "1")
	if err != spiCache.RedisNil {
		printRedMsg(err)
	}
	if userRank != 0 {
		printRedMsg(errors.New(fmt.Sprintf("userRank != 0, rank = %d", userRank)))
	}

	remCount, err := RedisUtilBatchZRem(ctx, testKey, randomUid, "1", "2")
	if err != nil {
		printRedMsg(err)
	}
	if remCount != 1 {
		printRedMsg(errors.New("userCount should be 1"))
	}

	printGreenMsg("------------- Test redisZSetSingleUserUT success ------------- ")
}

func redisNilUT(ctx context.Context, testKey string) {
	printGreenMsg("------------- Test redis nil  ------------- ")

	// test redis nil
	userCount, err := RedisUtilZCard(ctx, testKey) // 此时应当为 0，nil
	if err != nil {
		printRedMsg(err)
	}

	userScore, err2 := RedisUtilZScore(ctx, testKey, "1") // 此时应当为 0，spiCache.RedisNil
	userRank, err3 := RedisUtilZRank(ctx, testKey, "1")   // 此时应当为 0，spiCache.RedisNil
	if err2 != spiCache.RedisNil || err3 != spiCache.RedisNil {
		printRedMsg(err)
	}

	if userRank != 0 || userCount != 0 || userScore != 0 {
		printRedMsg(errors.New(fmt.Sprintf("userRank, userCount, userScore should be 0: %d, %d, %f", userRank, userCount, userScore)))
	}

	_, err = RedisUtilBatchZRem(ctx, testKey, "1", "2")
	if err != nil {
		printRedMsg(err)
	}
	_, err = RedisUtilZRemRangeByRank(ctx, testKey, 0, 1)
	if err != nil {
		printRedMsg(err)
	}

	_, err = RedisUtilZRange(ctx, testKey, 0, 1)
	if err != nil {
		printRedMsg(err)
	}
	_, err = RedisUtilZRevRange(ctx, testKey, 0, 1)
	if err != nil {
		printRedMsg(err)
	}

	printGreenMsg("------------- Test redis nil success------------- ")
}

func redisZSetAddUserListUT(ctx context.Context, testKey string) {
	printGreenMsg("------------- Test redisZSetAddUserListUT start ------------- ")

	elems := make([]*ZsetElem, 0, 10)
	for i := 1; i <= 10; i++ {
		elems = append(elems, &ZsetElem{
			Value: fmt.Sprintf("%d", i),
			Score: float64(i) * 1.0,
		})
	}

	addCount, err := RedisUtilZAddList(ctx, testKey, elems)
	if err != nil {
		printRedMsg(err)
	}
	if addCount != 10 {
		printRedMsg(errors.New("addCount != 10"))
	}

	userRank, err := RedisUtilZRevRank(ctx, testKey, "9")
	if err != nil {
		printRedMsg(err)
	}
	if userRank != 1 {
		printRedMsg(errors.New("userRank != 1"))
	}

	// 从大到小
	userList, err := RedisUtilZRevRange(ctx, testKey, 0, -1)
	if err != nil {
		printRedMsg(err)
	}
	for index, user := range userList {
		if user.Value != fmt.Sprintf("%d", 10-index) {
			printRedMsg(errors.New(fmt.Sprintf("wrong rank ,user = %s-%f", user.Value, user.Score)))
		}
	}

	// 从小到大
	userList, err = RedisUtilZRange(ctx, testKey, 0, -1)
	if err != nil {
		printRedMsg(err)
	}
	for index, user := range userList {
		if user.Value != fmt.Sprintf("%d", index+1) {
			printRedMsg(errors.New(fmt.Sprintf("wrong rank ,user = %s-%f", user.Value, user.Score)))
		}
	}

	// test local cached
	userList, err = GetCachedZRevRange(ctx, testKey, 0, -1, 10*time.Second)
	if err != nil {
		printRedMsg(err)
	}
	for index, user := range userList {
		if user.Value != fmt.Sprintf("%d", 10-index) {
			printRedMsg(errors.New(fmt.Sprintf("wrong rank ,user = %s, index = %d", user.Value, index)))
		}
	}

	userList, err = GetCachedZRange(ctx, testKey, 0, -1, 10*time.Second)
	if err != nil {
		printRedMsg(err)
	}
	for index, user := range userList {
		if user.Value != fmt.Sprintf("%d", index+1) {
			printRedMsg(errors.New(fmt.Sprintf("wrong rank ,user = %s-%f", user.Value, user.Score)))
		}
	}

	printGreenMsg("------------- Test redisZSetAddUserListUT success------------- ")
}

func redisMGetUT(ctx context.Context) {
	testK1 := fmt.Sprintf("test_key_name_%d", rand.Int31())
	testK2 := fmt.Sprintf("test_key_name_%d", rand.Int31())
	testK3 := fmt.Sprintf("test_key_name_%d", rand.Int31())

	defer RedisUtilDelete(ctx, testK1)
	defer RedisUtilDelete(ctx, testK2)
	defer RedisUtilDelete(ctx, testK3)

	v1 := rand.Int31()
	v2 := rand.Int31()
	v3 := rand.Int31()
	RedisUtilSet(ctx, testK1, fmt.Sprintf("%v", v1), time.Duration(60)*time.Second)
	RedisUtilSet(ctx, testK2, fmt.Sprintf("%v", v2), time.Duration(60)*time.Second)
	RedisUtilSet(ctx, testK3, fmt.Sprintf("%v", v3), time.Duration(60)*time.Second)
	valuseList := []int32{v1, v2, v3}

	results, err := RedisUtilMGet(ctx, []string{testK1, testK2, testK3})
	if err != nil {
		printRedMsg(err)
	}

	for index, tmp := range results {
		tmpV, err := strconv.ParseInt(tmp, 10, 64)
		if err != nil {
			printRedMsg(err)
		}

		if int32(tmpV) != valuseList[index] {
			printRedMsg(errors.New("int32(tmpV) != valuseList[index]"))
		}
	}

	printGreenMsg("------------- Test redisMGetUT success------------- ")
}

type tempUserScoreListV2Struct struct {
	Uid      uint64
	ScoreCmd spiCache.FloatCmd
}

func pipelineUT(ctx context.Context) {
	pipeline := GetPipeline()

	testKey := fmt.Sprintf("test_key_name_%d", rand.Int31())
	defer RedisUtilDelete(ctx, testKey)

	elems := make([]*ZsetElem, 0, 10)
	for i := 1; i <= 10; i++ {
		elems = append(elems, &ZsetElem{
			Value: fmt.Sprintf("%d", i),
			Score: rand.Float64(),
		})
	}

	RedisUtilZAddList(ctx, testKey, elems)

	cmdList := make([]*tempUserScoreListV2Struct, 0)
	for i := 1; i <= 10; i++ {
		trueKey := GetRedisPrefix() + testKey
		cmdList = append(cmdList, &tempUserScoreListV2Struct{
			Uid:      uint64(i),
			ScoreCmd: pipeline.ZScore(ctx, trueKey, fmt.Sprintf("%v", i)),
		})
	}

	_, err := pipeline.Exec(ctx)
	if err != nil {
		printRedMsg(err)
	}

	for index, tmpCMD := range cmdList {
		tmpResult, err := tmpCMD.ScoreCmd.Result()
		if err != nil {
			printRedMsg(err)
		}

		if tmpResult != elems[index].Score {
			printRedMsg(errors.New("tmpResult != elems[index].Score"))
		}
	}

	printGreenMsg("------------- Test pipelineUT success------------- ")
}

func testConfigCenter(ctx context.Context) {
	isRE := config.IsTopNReturnErr()
	if !isRE {
		printRedMsg(errors.New("isRE is false"))
	}

	td := config.GetNewKeyTimestampDaily()
	if td == 0 {
		printRedMsg(errors.New("rd = 0"))
	}

	printGreenMsg(fmt.Sprintf("td = %v", td))
	printGreenMsg("------------- testConfigCenter success------------- ")
}
