# Rank Server 关键类及接口文档 (基于真实代码结构)

## 1. 核心处理器类

### **RankHandler - 主要业务处理器**
**服务**: User Gateway / Admin Gateway  
**关键类/接口**: RankHandler  
**文件位置**: `handler/rank_handler.go`, `handler/rank_handler_2.0.go`, `handler/rank_handler_3.0.go`

#### **类定义**
```go
type RankHandler struct {
}
```

#### **用户接口函数原型**
```go
// 分数操作
func (s *RankHandler) SetScoreV2(ctx context.Context, req *game_rank.SetScoreRequestV2, rsp *game_rank.CommonResponse) error
func (s *RankHandler) SetScoreV3(ctx context.Context, req *game_rank.SetScoreRequestV3, rsp *game_rank.SetScoreResponseV3) error
func (s *RankHandler) IncrScoreV2(ctx context.Context, req *game_rank.IncrScoreRequestV2, rsp *game_rank.CommonResponse) error
func (s *RankHandler) IncrScoreV3(ctx context.Context, req *game_rank.IncrScoreRequestV3, rsp *game_rank.IncrScoreResponseV3) error

// 排名查询
func (s *RankHandler) TopNV2(ctx context.Context, req *game_rank.TopNRequestV2, rsp *game_rank.TopnResponse) error
func (s *RankHandler) TopNV3(ctx context.Context, req *game_rank.TopNRequestV3, rsp *game_rank.TopNResponseV3) error
func (s *RankHandler) UserRankV2(ctx context.Context, req *game_rank.UserRankRequestV2, rsp *game_rank.UserRankResponse) error
func (s *RankHandler) UserRankV3(ctx context.Context, req *game_rank.UserRankRequestV3, rsp *game_rank.UserRankResponseV3) error

// 分数查询
func (s *RankHandler) UserScoreV2(ctx context.Context, req *game_rank.UserScoreRequestV2, rsp *game_rank.UserScoreResponse) error
func (s *RankHandler) UserScoreV3(ctx context.Context, req *game_rank.UserScoreRequestV3, rsp *game_rank.UserScoreResponseV3) error
func (s *RankHandler) UserScoreListV2(ctx context.Context, req *game_rank.UserScoreListRequestV2, rsp *game_rank.UserScoreListResponse) error
func (s *RankHandler) UserScoreListV3(ctx context.Context, req *game_rank.UserScoreListRequestV3, rsp *game_rank.UserScoreListResponseV3) error

// 好友排行榜
func (s *RankHandler) FriendsRankV3(ctx context.Context, req *game_rank.FriendsRankRequestV3, rsp *game_rank.FriendsRankResponseV3) error
```

#### **管理员接口函数原型**
```go
// 管理员操作
func (s *RankHandler) IncrScoreV3Admin(ctx context.Context, req *game_rank.IncrScoreV3AdminRequest, rsp *game_rank.IncrScoreV3AdminResponse) error
func (s *RankHandler) DeleteUserRankV3Admin(ctx context.Context, req *game_rank.DeleteUserRankV3AdminRequest, rsp *game_rank.DeleteUserRankV3AdminResponse) error
func (s *RankHandler) RankListV3Admin(ctx context.Context, req *game_rank.RankListV3AdminRequest, rsp *game_rank.RankListV3AdminResponse) error

// 模块管理
func (s *RankHandler) QueryNewRankModule(ctx context.Context, req *game_rank.QueryNewRankModuleReq, rsp *game_rank.QueryNewRankModuleResp) error
func (s *RankHandler) UpsertNewRankModule(ctx context.Context, req *game_rank.UpsertNewRankModuleReq, rsp *game_rank.UpsertNewRankModuleResp) error
func (s *RankHandler) ListNewRankModule(ctx context.Context, req *game_rank.ListNewRankModuleReq, rsp *game_rank.ListNewRankModuleResp) error
```

**备注**: 实现所有gRPC接口的具体业务逻辑，支持版本化API和管理员特权操作

---

## 2. 异步写入处理器

### **rankWriter - Redis异步写入处理器**
**服务**: Async Processing  
**关键类/接口**: rankWriter  
**文件位置**: `handler/rank_redis.go`

#### **类定义**
```go
type rankWriter struct {
    rankChan []chan Rankings
    infoChan []chan userinfo
}
```

#### **关键函数原型**
```go
func (r *rankWriter) putRankChan(rank Rankings)
func (r *rankWriter) doSetRankScore(index int)
func DefaultRankWriter() rankWriter
```

**备注**: 使用Channel + Goroutine实现异步Redis写入，支持分桶策略和负载均衡

### **RankEsWriter - ES异步写入处理器**
**服务**: Async Processing  
**关键类/接口**: RankEsWriter  
**文件位置**: `handler/rank_es_2.0.go`

#### **类定义**
```go
type RankEsWriter struct {
    esIndexFormat string
    esOperation   string
    rankEsChan    chan interface{}
    buffer        []interface{}
    bufferSize    int
    bulkEsRequestDataPool Pool
    convert       EsChanDataConvert
    monitorLabel  string
    currentLimiter *semaphore.Semaphore
}
```

#### **关键函数原型**
```go
func (rsWriter *RankEsWriter) Init(options ...Options)
func (rsWriter *RankEsWriter) Write(data interface{})
func (rsWriter *RankEsWriter) produceToEsChan(data interface{})
func (rsWriter *RankEsWriter) consumerFromEsChan()
```

**备注**: 批量异步写入Elasticsearch，支持缓冲区优化和并发控制

---

## 3. 全局写入器实例

### **ES写入器实例集合**
**服务**: Async Processing  
**关键类/接口**: 全局变量  
**文件位置**: `handler/es_writer_2.0.go`

#### **全局实例定义**
```go
var (
    RankingsEsWriter         *RankEsWriter  // 排行榜数据写入器
    RankDetailEsWriter       *RankEsWriter  // 排行榜详情写入器
    DeleteRankEsWriter       *RankEsWriter  // 排行榜删除写入器
    DeleteRankDetailEsWriter *RankEsWriter  // 排行榜详情删除写入器
)
```

#### **初始化函数**
```go
func InitRankEsWriter()
func getRankingsEsWriter() *RankEsWriter
func getRankDetailEsWriter() *RankEsWriter
func getDeleteRankEsWriter() *RankEsWriter
func getDeleteRankDetailEsWriter() *RankEsWriter
```

**备注**: 提供不同类型的ES写入器实例，支持数据写入和删除操作

### **Redis写入器实例**
**服务**: Async Processing  
**关键类/接口**: 全局变量  
**文件位置**: `handler/rank_redis.go`

#### **全局实例定义**
```go
var RankWriter rankWriter
```

**备注**: 全局Redis异步写入器实例，处理所有排行榜数据的Redis写入操作

---

## 4. 管理员ES查询函数集合

### **Admin ES 查询函数**
**服务**: ODP (Operations Data Platform)  
**关键类/接口**: 函数集合 (非类)  
**文件位置**: `handler/admin_es.go`, `handler/admin_es_v2.go`

#### **核心查询函数原型**
```go
// V1版本查询函数
func rankList(eventId int32, offset, limit, userID uint64, userName string, rankValue float64, rankNum uint64, rankWeek, scope string) ([]*game_rank.RankListItem, uint64)
func esRankList(eventID int32, offset, limit, userId uint64, userName string, rankValue float64, rankWeek, scope string) ([]*game_rank.RankListItem, uint64, error)
func esQueryByRankNum(eventID int32, rankNum uint64, rankWeek, scope string) ([]*game_rank.RankListItem, uint64, error)
func queryAllRankListByScroll(eventID int32, offset, limit uint64, rankWeek, scope string) ([]*game_rank.RankListItem, uint64, error)

// V2版本查询函数
func rankListV2(index string, condition *RankEsCondition) ([]*game_rank.RankListItem, uint64, string)
func esRankListV2(esIndex string, condition *RankEsCondition) ([]*game_rank.RankListItem, uint64, error)
func esQueryByRankNumV2(rankNum uint64, esIndex string) ([]*game_rank.RankListItem, uint64, error)
func queryRankListByScrollV2(esIndex string, condition *RankEsCondition) ([]*game_rank.RankListItem, uint64, error)
```

**备注**: ODP专用ES查询函数集合，支持复杂条件查询、分页查询和滚动查询

---

## 5. Redis操作函数集合

### **Redis查询函数**
**服务**: Storage Access  
**关键类/接口**: 函数集合 (非类)  
**文件位置**: `handler/rank_redis.go`, `handler/rank_redis_topN.go`

#### **核心查询函数原型**
```go
// TopN优化查询
func QueryTopNFromSingleKey(ctx context.Context, scope, rankName string, n int32, rankType uint32, reverse bool, needCut bool) ([]cache.ZsetElem, error)
func QueryUserRankV2(ctx context.Context, userId uint64, scope, rankName string, rankType uint32, reverse bool) (uint64, error)

// 分桶查询
func allbucketZSetRange(nameWithoutBucket string, n int32, reverse bool, bucketNum int, needCut bool, needUserName bool) ([]cache.ZsetElem, error)
func userIdHashBucketRank(scope, rankName string, userId uint64, bucketNum int) (string, int)

// 双写机制
func DoubleWriteTopNKey(score float64, isAdd, isIncr bool, rank *Rankings)
func DoubleWriteBottomNKey(score float64, isAdd, isIncr bool, rank *Rankings)
```

**备注**: Redis操作函数集合，实现分桶策略、TopN优化和双写机制

---

## 6. 核心数据结构

### **Rankings - 排行榜数据结构**
```go
type Rankings struct {
    Scope         string
    RankName      string
    Username      string
    Userid        uint64
    Score         float64
    Timestamp     int64
    RankType      int32
    IsSetScoreReq bool
}
```

### **RankModuleInfo - 排行榜模块信息**
```go
type RankModuleInfo struct {
    Config     *model.NewRankConfigVersionTab
    Appid      string
    ActivityId uint64
    SlotId     uint64
    StartTime  int64
    EndTime    int64
}
```

### **RankEsCondition - ES查询条件**
```go
type RankEsCondition struct {
    offset    uint64
    limit     uint64
    userId    uint64
    userName  string
    rankValue float64
    rankNum   uint64
    scrollId  string
}
```

---

## 7. 任务调度器

### **Task Scheduler 函数集合**
**服务**: Task Scheduler  
**关键类/接口**: 函数集合 (非类)  
**文件位置**: `handler/close_index_task.go`, `handler/user_info_local_cache.go`

#### **关键函数原型**
```go
// ES索引管理
func AsyncCloseEsIndex()
func doCloseEsIndex()
func needCloseIndex(index string, createTime int64) bool
func tryAcquireTaskLock(taskName string) (bool, error)

// 缓存管理
func ClearUserInfoLocalCache()
func RunCronOfUserInfoLocalCache()
func SetUserInfoToLocalCache(userIds []uint64, userInfos map[uint64]*gameplatform_friend.UserBaseInfo)
```

**备注**: 后台任务调度函数，负责ES索引清理和本地缓存管理

---

## 8. 关键特性说明

### **异步处理架构**
- **Channel + Goroutine**: rankWriter和RankEsWriter使用异步处理
- **批量写入**: ES批量写入提高性能
- **缓冲区优化**: 支持可配置的缓冲区大小

### **性能优化策略**
- **分桶策略**: Redis分桶减少热点Key问题
- **TopN Key**: 前10000名精确查询优化
- **双写机制**: 保证Redis和ES数据一致性
- **本地缓存**: 用户信息本地缓存减少RPC调用

### **数据一致性保证**
- **分布式锁**: 使用Redis实现任务锁
- **重试机制**: 失败重试保证数据完整性
- **版本化API**: 支持渐进式升级和兼容性

### **安全和监控**
- **黑名单检查**: 集成反作弊系统
- **参数验证**: 统一参数验证和错误处理
- **监控指标**: 完整的性能监控和告警机制
